(()=>{var _e=Object.create;var ce=Object.defineProperty;var xe=Object.getOwnPropertyDescriptor;var Ce=Object.getOwnPropertyNames;var Ae=Object.getPrototypeOf,Te=Object.prototype.hasOwnProperty;var ke=e=>ce(e,"__esModule",{value:!0});var Fe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Me=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Ce(t))!Te.call(e,s)&&(r||s!=="default")&&ce(e,s,{get:()=>t[s],enumerable:!(i=xe(t,s))||i.enumerable});return e},we=(e,t)=>Me(ke(ce(e!=null?_e(Ae(e)):{},"default",!t&&e&&e.__esModule?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);var ve=Fe((exports,module)=>{typeof navigator!="undefined"&&function(e,t){typeof exports=="object"&&typeof module!="undefined"?module.exports=t():typeof define=="function"&&define.amd?define(t):(e=typeof globalThis!="undefined"?globalThis:e||self,e.lottie=t())}(exports,function(){"use strict";var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(e){return document.createElement(e)}function extendPrototype(e,t){var r,i=e.length,s;for(r=0;r<i;r+=1){s=e[r].prototype;for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(t.prototype[a]=s[a])}}function getDescriptor(e,t){return Object.getOwnPropertyDescriptor(e,t)}function createProxyFunction(e){function t(){}return t.prototype=e,t}var audioControllerFactory=function(){function e(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return e.prototype={addAudio:function(r){this.audios.push(r)},pause:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].pause()},resume:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].resume()},setRate:function(r){var i,s=this.audios.length;for(i=0;i<s;i+=1)this.audios[i].setRate(r)},createAudio:function(r){return this.audioFactory?this.audioFactory(r):window.Howl?new window.Howl({src:[r]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(r){this.audioFactory=r},setVolume:function(r){this._volume=r,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].volume(this._volume*(this._isMuted?0:1))}},function(){return new e}}(),createTypedArray=function(){function e(r,i){var s=0,a=[],n;switch(r){case"int16":case"uint8c":n=1;break;default:n=1.1;break}for(s=0;s<i;s+=1)a.push(n);return a}function t(r,i){return r==="float32"?new Float32Array(i):r==="int16"?new Int16Array(i):r==="uint8c"?new Uint8ClampedArray(i):e(r,i)}return typeof Uint8ClampedArray=="function"&&typeof Float32Array=="function"?t:e}();function createSizedArray(e){return Array.apply(null,{length:e})}function _typeof$6(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$6=function(r){return typeof r}:_typeof$6=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$6(e)}var subframeEnabled=!0,expressionsPlugin=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};(function(){var e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],t,r=e.length;for(t=0;t<r;t+=1)BMMath[e[t]]=Math[e[t]]})();function ProjectInterface$1(){return{}}BMMath.random=Math.random,BMMath.abs=function(e){var t=_typeof$6(e);if(t==="object"&&e.length){var r=createSizedArray(e.length),i,s=e.length;for(i=0;i<s;i+=1)r[i]=Math.abs(e[i]);return r}return Math.abs(e)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(e){_shouldRoundValues=!!e}function bmRnd(e){return _shouldRoundValues?Math.round(e):e}function styleDiv(e){e.style.position="absolute",e.style.top=0,e.style.left=0,e.style.display="block",e.style.transformOrigin="0 0",e.style.webkitTransformOrigin="0 0",e.style.backfaceVisibility="visible",e.style.webkitBackfaceVisibility="visible",e.style.transformStyle="preserve-3d",e.style.webkitTransformStyle="preserve-3d",e.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(e,t,r,i){this.type=e,this.currentTime=t,this.totalTime=r,this.direction=i<0?-1:1}function BMCompleteEvent(e,t){this.type=e,this.direction=t<0?-1:1}function BMCompleteLoopEvent(e,t,r,i){this.type=e,this.currentLoop=r,this.totalLoops=t,this.direction=i<0?-1:1}function BMSegmentStartEvent(e,t,r){this.type=e,this.firstFrame=t,this.totalFrames=r}function BMDestroyEvent(e,t){this.type=e,this.target=t}function BMRenderFrameErrorEvent(e,t){this.type="renderFrameError",this.nativeError=e,this.currentTime=t}function BMConfigErrorEvent(e){this.type="configError",this.nativeError=e}function BMAnimationConfigErrorEvent(e,t){this.type=e,this.nativeError=t}var createElementID=function(){var e=0;return function(){return e+=1,idPrefix$1+"__lottie_element_"+e}}();function HSVtoRGB(e,t,r){var i,s,a,n,p,c,m,g;switch(n=Math.floor(e*6),p=e*6-n,c=r*(1-t),m=r*(1-p*t),g=r*(1-(1-p)*t),n%6){case 0:i=r,s=g,a=c;break;case 1:i=m,s=r,a=c;break;case 2:i=c,s=r,a=g;break;case 3:i=c,s=m,a=r;break;case 4:i=g,s=c,a=r;break;case 5:i=r,s=c,a=m;break;default:break}return[i,s,a]}function RGBtoHSV(e,t,r){var i=Math.max(e,t,r),s=Math.min(e,t,r),a=i-s,n,p=i===0?0:a/i,c=i/255;switch(i){case s:n=0;break;case e:n=t-r+a*(t<r?6:0),n/=6*a;break;case t:n=r-e+a*2,n/=6*a;break;case r:n=e-t+a*4,n/=6*a;break;default:break}return[n,p,c]}function addSaturationToRGB(e,t){var r=RGBtoHSV(e[0]*255,e[1]*255,e[2]*255);return r[1]+=t,r[1]>1?r[1]=1:r[1]<=0&&(r[1]=0),HSVtoRGB(r[0],r[1],r[2])}function addBrightnessToRGB(e,t){var r=RGBtoHSV(e[0]*255,e[1]*255,e[2]*255);return r[2]+=t,r[2]>1?r[2]=1:r[2]<0&&(r[2]=0),HSVtoRGB(r[0],r[1],r[2])}function addHueToRGB(e,t){var r=RGBtoHSV(e[0]*255,e[1]*255,e[2]*255);return r[0]+=t/360,r[0]>1?r[0]-=1:r[0]<0&&(r[0]+=1),HSVtoRGB(r[0],r[1],r[2])}var rgbToHex=function(){var e=[],t,r;for(t=0;t<256;t+=1)r=t.toString(16),e[t]=r.length===1?"0"+r:r;return function(i,s,a){return i<0&&(i=0),s<0&&(s=0),a<0&&(a=0),"#"+e[i]+e[s]+e[a]}}(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(e){return document.createElementNS(svgNS,e)}function _typeof$5(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$5=function(r){return typeof r}:_typeof$5=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$5(e)}var dataManager=function(){var e=1,t=[],r,i,s={onmessage:function(){},postMessage:function(P){r({data:P})}},a={postMessage:function(P){s.onmessage({data:P})}};function n(u){if(window.Worker&&window.Blob&&getWebWorker()){var P=new Blob(["var _workerSelf = self; self.onmessage = ",u.toString()],{type:"text/javascript"}),v=URL.createObjectURL(P);return new Worker(v)}return r=u,s}function p(){i||(i=n(function(P){function v(){function S(w,k){var A,E,T=w.length,V,M,G,N;for(E=0;E<T;E+=1)if(A=w[E],"ks"in A&&!A.completed){if(A.completed=!0,A.tt&&(w[E-1].td=A.tt),A.hasMask){var z=A.masksProperties;for(M=z.length,V=0;V<M;V+=1)if(z[V].pt.k.i)y(z[V].pt.k);else for(N=z[V].pt.k.length,G=0;G<N;G+=1)z[V].pt.k[G].s&&y(z[V].pt.k[G].s[0]),z[V].pt.k[G].e&&y(z[V].pt.k[G].e[0])}A.ty===0?(A.layers=l(A.refId,k),S(A.layers,k)):A.ty===4?h(A.shapes):A.ty===5&&D(A)}}function o(w,k){if(w){var A=0,E=w.length;for(A=0;A<E;A+=1)w[A].t===1&&(w[A].data.layers=l(w[A].data.refId,k),S(w[A].data.layers,k))}}function f(w,k){for(var A=0,E=k.length;A<E;){if(k[A].id===w)return k[A];A+=1}return null}function l(w,k){var A=f(w,k);return A?A.layers.__used?JSON.parse(JSON.stringify(A.layers)):(A.layers.__used=!0,A.layers):null}function h(w){var k,A=w.length,E,T;for(k=A-1;k>=0;k-=1)if(w[k].ty==="sh")if(w[k].ks.k.i)y(w[k].ks.k);else for(T=w[k].ks.k.length,E=0;E<T;E+=1)w[k].ks.k[E].s&&y(w[k].ks.k[E].s[0]),w[k].ks.k[E].e&&y(w[k].ks.k[E].e[0]);else w[k].ty==="gr"&&h(w[k].it)}function y(w){var k,A=w.i.length;for(k=0;k<A;k+=1)w.i[k][0]+=w.v[k][0],w.i[k][1]+=w.v[k][1],w.o[k][0]+=w.v[k][0],w.o[k][1]+=w.v[k][1]}function b(w,k){var A=k?k.split("."):[100,100,100];return w[0]>A[0]?!0:A[0]>w[0]?!1:w[1]>A[1]?!0:A[1]>w[1]?!1:w[2]>A[2]?!0:A[2]>w[2]?!1:null}var _=function(){var w=[4,4,14];function k(E){var T=E.t.d;E.t.d={k:[{s:T,t:0}]}}function A(E){var T,V=E.length;for(T=0;T<V;T+=1)E[T].ty===5&&k(E[T])}return function(E){if(b(w,E.v)&&(A(E.layers),E.assets)){var T,V=E.assets.length;for(T=0;T<V;T+=1)E.assets[T].layers&&A(E.assets[T].layers)}}}(),C=function(){var w=[4,7,99];return function(k){if(k.chars&&!b(w,k.v)){var A,E=k.chars.length;for(A=0;A<E;A+=1){var T=k.chars[A];T.data&&T.data.shapes&&(h(T.data.shapes),T.data.ip=0,T.data.op=99999,T.data.st=0,T.data.sr=1,T.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},k.chars[A].t||(T.data.shapes.push({ty:"no"}),T.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}}(),F=function(){var w=[5,7,15];function k(E){var T=E.t.p;typeof T.a=="number"&&(T.a={a:0,k:T.a}),typeof T.p=="number"&&(T.p={a:0,k:T.p}),typeof T.r=="number"&&(T.r={a:0,k:T.r})}function A(E){var T,V=E.length;for(T=0;T<V;T+=1)E[T].ty===5&&k(E[T])}return function(E){if(b(w,E.v)&&(A(E.layers),E.assets)){var T,V=E.assets.length;for(T=0;T<V;T+=1)E.assets[T].layers&&A(E.assets[T].layers)}}}(),L=function(){var w=[4,1,9];function k(E){var T,V=E.length,M,G;for(T=0;T<V;T+=1)if(E[T].ty==="gr")k(E[T].it);else if(E[T].ty==="fl"||E[T].ty==="st")if(E[T].c.k&&E[T].c.k[0].i)for(G=E[T].c.k.length,M=0;M<G;M+=1)E[T].c.k[M].s&&(E[T].c.k[M].s[0]/=255,E[T].c.k[M].s[1]/=255,E[T].c.k[M].s[2]/=255,E[T].c.k[M].s[3]/=255),E[T].c.k[M].e&&(E[T].c.k[M].e[0]/=255,E[T].c.k[M].e[1]/=255,E[T].c.k[M].e[2]/=255,E[T].c.k[M].e[3]/=255);else E[T].c.k[0]/=255,E[T].c.k[1]/=255,E[T].c.k[2]/=255,E[T].c.k[3]/=255}function A(E){var T,V=E.length;for(T=0;T<V;T+=1)E[T].ty===4&&k(E[T].shapes)}return function(E){if(b(w,E.v)&&(A(E.layers),E.assets)){var T,V=E.assets.length;for(T=0;T<V;T+=1)E.assets[T].layers&&A(E.assets[T].layers)}}}(),R=function(){var w=[4,4,18];function k(E){var T,V=E.length,M,G;for(T=V-1;T>=0;T-=1)if(E[T].ty==="sh")if(E[T].ks.k.i)E[T].ks.k.c=E[T].closed;else for(G=E[T].ks.k.length,M=0;M<G;M+=1)E[T].ks.k[M].s&&(E[T].ks.k[M].s[0].c=E[T].closed),E[T].ks.k[M].e&&(E[T].ks.k[M].e[0].c=E[T].closed);else E[T].ty==="gr"&&k(E[T].it)}function A(E){var T,V,M=E.length,G,N,z,j;for(V=0;V<M;V+=1){if(T=E[V],T.hasMask){var H=T.masksProperties;for(N=H.length,G=0;G<N;G+=1)if(H[G].pt.k.i)H[G].pt.k.c=H[G].cl;else for(j=H[G].pt.k.length,z=0;z<j;z+=1)H[G].pt.k[z].s&&(H[G].pt.k[z].s[0].c=H[G].cl),H[G].pt.k[z].e&&(H[G].pt.k[z].e[0].c=H[G].cl)}T.ty===4&&k(T.shapes)}}return function(E){if(b(w,E.v)&&(A(E.layers),E.assets)){var T,V=E.assets.length;for(T=0;T<V;T+=1)E.assets[T].layers&&A(E.assets[T].layers)}}}();function I(w){w.__complete||(L(w),_(w),C(w),F(w),R(w),S(w.layers,w.assets),o(w.chars,w.assets),w.__complete=!0)}function D(w){w.t.a.length===0&&!("m"in w.t.p)}var B={};return B.completeData=I,B.checkColors=L,B.checkChars=C,B.checkPathProperties=F,B.checkShapes=R,B.completeLayers=S,B}if(a.dataManager||(a.dataManager=v()),a.assetLoader||(a.assetLoader=function(){function S(f){var l=f.getResponseHeader("content-type");return l&&f.responseType==="json"&&l.indexOf("json")!==-1||f.response&&_typeof$5(f.response)==="object"?f.response:f.response&&typeof f.response=="string"?JSON.parse(f.response):f.responseText?JSON.parse(f.responseText):null}function o(f,l,h,y){var b,_=new XMLHttpRequest;try{_.responseType="json"}catch{}_.onreadystatechange=function(){if(_.readyState===4)if(_.status===200)b=S(_),h(b);else try{b=S(_),h(b)}catch(C){y&&y(C)}};try{_.open("GET",f,!0)}catch{_.open("GET",l+"/"+f,!0)}_.send()}return{load:o}}()),P.data.type==="loadAnimation")a.assetLoader.load(P.data.path,P.data.fullPath,function(S){a.dataManager.completeData(S),a.postMessage({id:P.data.id,payload:S,status:"success"})},function(){a.postMessage({id:P.data.id,status:"error"})});else if(P.data.type==="complete"){var d=P.data.animation;a.dataManager.completeData(d),a.postMessage({id:P.data.id,payload:d,status:"success"})}else P.data.type==="loadData"&&a.assetLoader.load(P.data.path,P.data.fullPath,function(S){a.postMessage({id:P.data.id,payload:S,status:"success"})},function(){a.postMessage({id:P.data.id,status:"error"})})}),i.onmessage=function(u){var P=u.data,v=P.id,d=t[v];t[v]=null,P.status==="success"?d.onComplete(P.payload):d.onError&&d.onError()})}function c(u,P){e+=1;var v="processId_"+e;return t[v]={onComplete:u,onError:P},v}function m(u,P,v){p();var d=c(P,v);i.postMessage({type:"loadAnimation",path:u,fullPath:window.location.origin+window.location.pathname,id:d})}function g(u,P,v){p();var d=c(P,v);i.postMessage({type:"loadData",path:u,fullPath:window.location.origin+window.location.pathname,id:d})}function x(u,P,v){p();var d=c(P,v);i.postMessage({type:"complete",animation:u,id:d})}return{loadAnimation:m,loadData:g,completeAnimation:x}}(),ImagePreloader=function(){var e=function(){var o=createTag("canvas");o.width=1,o.height=1;var f=o.getContext("2d");return f.fillStyle="rgba(0,0,0,0)",f.fillRect(0,0,1,1),o}();function t(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(o,f,l){var h="";if(o.e)h=o.p;else if(f){var y=o.p;y.indexOf("images/")!==-1&&(y=y.split("/")[1]),h=f+y}else h=l,h+=o.u?o.u:"",h+=o.p;return h}function s(o){var f=0,l=setInterval(function(){var h=o.getBBox();(h.width||f>500)&&(this._imageLoaded(),clearInterval(l)),f+=1}.bind(this),50)}function a(o){var f=i(o,this.assetsPath,this.path),l=createNS("image");isSafari?this.testImageLoaded(l):l.addEventListener("load",this._imageLoaded,!1),l.addEventListener("error",function(){h.img=e,this._imageLoaded()}.bind(this),!1),l.setAttributeNS("http://www.w3.org/1999/xlink","href",f),this._elementHelper.append?this._elementHelper.append(l):this._elementHelper.appendChild(l);var h={img:l,assetData:o};return h}function n(o){var f=i(o,this.assetsPath,this.path),l=createTag("img");l.crossOrigin="anonymous",l.addEventListener("load",this._imageLoaded,!1),l.addEventListener("error",function(){h.img=e,this._imageLoaded()}.bind(this),!1),l.src=f;var h={img:l,assetData:o};return h}function p(o){var f={assetData:o},l=i(o,this.assetsPath,this.path);return dataManager.loadData(l,function(h){f.img=h,this._footageLoaded()}.bind(this),function(){f.img={},this._footageLoaded()}.bind(this)),f}function c(o,f){this.imagesLoadedCb=f;var l,h=o.length;for(l=0;l<h;l+=1)o[l].layers||(!o[l].t||o[l].t==="seq"?(this.totalImages+=1,this.images.push(this._createImageData(o[l]))):o[l].t===3&&(this.totalFootages+=1,this.images.push(this.createFootageData(o[l]))))}function m(o){this.path=o||""}function g(o){this.assetsPath=o||""}function x(o){for(var f=0,l=this.images.length;f<l;){if(this.images[f].assetData===o)return this.images[f].img;f+=1}return null}function u(){this.imagesLoadedCb=null,this.images.length=0}function P(){return this.totalImages===this.loadedAssets}function v(){return this.totalFootages===this.loadedFootagesCount}function d(o,f){o==="svg"?(this._elementHelper=f,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}function S(){this._imageLoaded=t.bind(this),this._footageLoaded=r.bind(this),this.testImageLoaded=s.bind(this),this.createFootageData=p.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return S.prototype={loadAssets:c,setAssetsPath:g,setPath:m,loadedImages:P,loadedFootages:v,destroy:u,getAsset:x,createImgData:n,createImageData:a,imageLoaded:t,footageLoaded:r,setCacheType:d},S}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,r){if(this._cbs[t])for(var i=this._cbs[t],s=0;s<i.length;s+=1)i[s](r)},addEventListener:function(t,r){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(r),function(){this.removeEventListener(t,r)}.bind(this)},removeEventListener:function(t,r){if(!r)this._cbs[t]=null;else if(this._cbs[t]){for(var i=0,s=this._cbs[t].length;i<s;)this._cbs[t][i]===r&&(this._cbs[t].splice(i,1),i-=1,s-=1),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}};var markerParser=function(){function e(t){for(var r=t.split(`\r
`),i={},s,a=0,n=0;n<r.length;n+=1)s=r[n].split(":"),s.length===2&&(i[s[0]]=s[1].trim(),a+=1);if(a===0)throw new Error;return i}return function(t){for(var r=[],i=0;i<t.length;i+=1){var s=t[i],a={time:s.tm,duration:s.dr};try{a.payload=JSON.parse(t[i].cm)}catch{try{a.payload=e(t[i].cm)}catch{a.payload={name:t[i].cm}}}r.push(a)}return r}}(),ProjectInterface=function(){function e(t){this.compositions.push(t)}return function(){function t(r){for(var i=0,s=this.compositions.length;i<s;){if(this.compositions[i].data&&this.compositions[i].data.nm===r)return this.compositions[i].prepareFrame&&this.compositions[i].data.xt&&this.compositions[i].prepareFrame(this.currentFrame),this.compositions[i].compInterface;i+=1}return null}return t.compositions=[],t.currentFrame=0,t.registerComposition=e,t}}(),renderers={},registerRenderer=function(t,r){renderers[t]=r};function getRenderer(e){return renderers[e]}function _typeof$4(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$4=function(r){return typeof r}:_typeof$4=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$4(e)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0)};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(e){(e.wrapper||e.container)&&(this.wrapper=e.wrapper||e.container);var t="svg";e.animType?t=e.animType:e.renderer&&(t=e.renderer);var r=getRenderer(t);this.renderer=new r(this,e.rendererSettings),this.imagePreloader.setCacheType(t,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=t,e.loop===""||e.loop===null||e.loop===void 0||e.loop===!0?this.loop=!0:e.loop===!1?this.loop=!1:this.loop=parseInt(e.loop,10),this.autoplay="autoplay"in e?e.autoplay:!0,this.name=e.name?e.name:"",this.autoloadSegments=Object.prototype.hasOwnProperty.call(e,"autoloadSegments")?e.autoloadSegments:!0,this.assetsPath=e.assetsPath,this.initialSegment=e.initialSegment,e.audioFactory&&this.audioController.setAudioFactory(e.audioFactory),e.animationData?this.setupAnimation(e.animationData):e.path&&(e.path.lastIndexOf("\\")!==-1?this.path=e.path.substr(0,e.path.lastIndexOf("\\")+1):this.path=e.path.substr(0,e.path.lastIndexOf("/")+1),this.fileName=e.path.substr(e.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(e.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(e){dataManager.completeAnimation(e,this.configAnimation)},AnimationItem.prototype.setData=function(e,t){t&&_typeof$4(t)!=="object"&&(t=JSON.parse(t));var r={wrapper:e,animationData:t},i=e.attributes;r.path=i.getNamedItem("data-animation-path")?i.getNamedItem("data-animation-path").value:i.getNamedItem("data-bm-path")?i.getNamedItem("data-bm-path").value:i.getNamedItem("bm-path")?i.getNamedItem("bm-path").value:"",r.animType=i.getNamedItem("data-anim-type")?i.getNamedItem("data-anim-type").value:i.getNamedItem("data-bm-type")?i.getNamedItem("data-bm-type").value:i.getNamedItem("bm-type")?i.getNamedItem("bm-type").value:i.getNamedItem("data-bm-renderer")?i.getNamedItem("data-bm-renderer").value:i.getNamedItem("bm-renderer")?i.getNamedItem("bm-renderer").value:"canvas";var s=i.getNamedItem("data-anim-loop")?i.getNamedItem("data-anim-loop").value:i.getNamedItem("data-bm-loop")?i.getNamedItem("data-bm-loop").value:i.getNamedItem("bm-loop")?i.getNamedItem("bm-loop").value:"";s==="false"?r.loop=!1:s==="true"?r.loop=!0:s!==""&&(r.loop=parseInt(s,10));var a=i.getNamedItem("data-anim-autoplay")?i.getNamedItem("data-anim-autoplay").value:i.getNamedItem("data-bm-autoplay")?i.getNamedItem("data-bm-autoplay").value:i.getNamedItem("bm-autoplay")?i.getNamedItem("bm-autoplay").value:!0;r.autoplay=a!=="false",r.name=i.getNamedItem("data-name")?i.getNamedItem("data-name").value:i.getNamedItem("data-bm-name")?i.getNamedItem("data-bm-name").value:i.getNamedItem("bm-name")?i.getNamedItem("bm-name").value:"";var n=i.getNamedItem("data-anim-prerender")?i.getNamedItem("data-anim-prerender").value:i.getNamedItem("data-bm-prerender")?i.getNamedItem("data-bm-prerender").value:i.getNamedItem("bm-prerender")?i.getNamedItem("bm-prerender").value:"";n==="false"&&(r.prerender=!1),this.setParams(r)},AnimationItem.prototype.includeLayers=function(e){e.op>this.animationData.op&&(this.animationData.op=e.op,this.totalFrames=Math.floor(e.op-this.animationData.ip));var t=this.animationData.layers,r,i=t.length,s=e.layers,a,n=s.length;for(a=0;a<n;a+=1)for(r=0;r<i;){if(t[r].id===s[a].id){t[r]=s[a];break}r+=1}if((e.chars||e.fonts)&&(this.renderer.globalData.fontManager.addChars(e.chars),this.renderer.globalData.fontManager.addFonts(e.fonts,this.renderer.globalData.defs)),e.assets)for(i=e.assets.length,r=0;r<i;r+=1)this.animationData.assets.push(e.assets[r]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(e){this.animationData=e;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var e=this.animationData.segments;if(!e||e.length===0||!this.autoloadSegments){this.trigger("data_ready"),this.timeCompleted=this.totalFrames;return}var t=e.shift();this.timeCompleted=t.time*this.frameRate;var r=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(r,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},AnimationItem.prototype.loadSegments=function(){var e=this.animationData.segments;e||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(e){if(!!this.renderer)try{this.animationData=e,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(e),e.assets||(e.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(e.assets),this.markers=markerParser(e.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){!this.renderer||(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||this.renderer.rendererType!=="canvas")&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(){this.renderer.updateContainerSize()},AnimationItem.prototype.setSubframe=function(e){this.isSubframeEnabled=!!e},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!(this.isLoaded===!1||!this.renderer))try{this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(e){this.triggerRenderFrameError(e)}},AnimationItem.prototype.play=function(e){e&&this.name!==e||this.isPaused===!0&&(this.isPaused=!1,this.trigger("_pause"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(e){e&&this.name!==e||this.isPaused===!1&&(this.isPaused=!0,this.trigger("_play"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(e){e&&this.name!==e||(this.isPaused===!0?this.play():this.pause())},AnimationItem.prototype.stop=function(e){e&&this.name!==e||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(e){for(var t,r=0;r<this.markers.length;r+=1)if(t=this.markers[r],t.payload&&t.payload.name===e)return t;return null},AnimationItem.prototype.goToAndStop=function(e,t,r){if(!(r&&this.name!==r)){var i=Number(e);if(isNaN(i)){var s=this.getMarkerData(e);s&&this.goToAndStop(s.time,!0)}else t?this.setCurrentRawFrameValue(e):this.setCurrentRawFrameValue(e*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(e,t,r){if(!(r&&this.name!==r)){var i=Number(e);if(isNaN(i)){var s=this.getMarkerData(e);s&&(s.duration?this.playSegments([s.time,s.time+s.duration],!0):this.goToAndStop(s.time,!0))}else this.goToAndStop(i,t,r);this.play()}},AnimationItem.prototype.advanceTime=function(e){if(!(this.isPaused===!0||this.isLoaded===!1)){var t=this.currentRawFrame+e*this.frameModifier,r=!1;t>=this.totalFrames-1&&this.frameModifier>0?!this.loop||this.playCount===this.loop?this.checkSegments(t>this.totalFrames?t%this.totalFrames:0)||(r=!0,t=this.totalFrames-1):t>=this.totalFrames?(this.playCount+=1,this.checkSegments(t%this.totalFrames)||(this.setCurrentRawFrameValue(t%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(t):t<0?this.checkSegments(t%this.totalFrames)||(this.loop&&!(this.playCount--<=0&&this.loop!==!0)?(this.setCurrentRawFrameValue(this.totalFrames+t%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0):(r=!0,t=0)):this.setCurrentRawFrameValue(t),r&&(this.setCurrentRawFrameValue(t),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(e,t){this.playCount=0,e[1]<e[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=e[0]-e[1],this.timeCompleted=this.totalFrames,this.firstFrame=e[1],this.setCurrentRawFrameValue(this.totalFrames-.001-t)):e[1]>e[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=e[1]-e[0],this.timeCompleted=this.totalFrames,this.firstFrame=e[0],this.setCurrentRawFrameValue(.001+t)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(e,t){var r=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<e?r=e:this.currentRawFrame+this.firstFrame>t&&(r=t-e)),this.firstFrame=e,this.totalFrames=t-e,this.timeCompleted=this.totalFrames,r!==-1&&this.goToAndStop(r,!0)},AnimationItem.prototype.playSegments=function(e,t){if(t&&(this.segments.length=0),_typeof$4(e[0])==="object"){var r,i=e.length;for(r=0;r<i;r+=1)this.segments.push(e[r])}else this.segments.push(e);this.segments.length&&t&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(e){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),e&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(e){return this.segments.length?(this.adjustSegment(this.segments.shift(),e),!0):!1},AnimationItem.prototype.destroy=function(e){e&&this.name!==e||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.renderer=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(e){this.currentRawFrame=e,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(e){this.playSpeed=e,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(e){this.playDirection=e<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setVolume=function(e,t){t&&this.name!==t||this.audioController.setVolume(e)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(e){e&&this.name!==e||this.audioController.mute()},AnimationItem.prototype.unmute=function(e){e&&this.name!==e||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(e){var t="";if(e.e)t=e.p;else if(this.assetsPath){var r=e.p;r.indexOf("images/")!==-1&&(r=r.split("/")[1]),t=this.assetsPath+r}else t=this.path,t+=e.u?e.u:"",t+=e.p;return t},AnimationItem.prototype.getAssetData=function(e){for(var t=0,r=this.assets.length;t<r;){if(e===this.assets[t].id)return this.assets[t];t+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(e){return e?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(e,t,r){try{var i=this.renderer.getElementByPath(e);i.updateDocumentData(t,r)}catch{}},AnimationItem.prototype.trigger=function(e){if(this._cbs&&this._cbs[e])switch(e){case"enterFrame":this.triggerEvent(e,new BMEnterFrameEvent(e,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(e,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(e,new BMCompleteLoopEvent(e,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(e,new BMCompleteEvent(e,this.frameMult));break;case"segmentStart":this.triggerEvent(e,new BMSegmentStartEvent(e,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(e,new BMDestroyEvent(e,this));break;default:this.triggerEvent(e)}e==="enterFrame"&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(e,this.currentFrame,this.totalFrames,this.frameMult)),e==="loopComplete"&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(e,this.loop,this.playCount,this.frameMult)),e==="complete"&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(e,this.frameMult)),e==="segmentStart"&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(e,this.firstFrame,this.totalFrames)),e==="destroy"&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(e,this))},AnimationItem.prototype.triggerRenderFrameError=function(e){var t=new BMRenderFrameErrorEvent(e,this.currentFrame);this.triggerEvent("error",t),this.onError&&this.onError.call(this,t)},AnimationItem.prototype.triggerConfigError=function(e){var t=new BMConfigErrorEvent(e,this.currentFrame);this.triggerEvent("error",t),this.onError&&this.onError.call(this,t)};var animationManager=function(){var e={},t=[],r=0,i=0,s=0,a=!0,n=!1;function p(k){for(var A=0,E=k.target;A<i;)t[A].animation===E&&(t.splice(A,1),A-=1,i-=1,E.isPaused||x()),A+=1}function c(k,A){if(!k)return null;for(var E=0;E<i;){if(t[E].elem===k&&t[E].elem!==null)return t[E].animation;E+=1}var T=new AnimationItem;return u(T,k),T.setData(k,A),T}function m(){var k,A=t.length,E=[];for(k=0;k<A;k+=1)E.push(t[k].animation);return E}function g(){s+=1,L()}function x(){s-=1}function u(k,A){k.addEventListener("destroy",p),k.addEventListener("_active",g),k.addEventListener("_idle",x),t.push({elem:A,animation:k}),i+=1}function P(k){var A=new AnimationItem;return u(A,null),A.setParams(k),A}function v(k,A){var E;for(E=0;E<i;E+=1)t[E].animation.setSpeed(k,A)}function d(k,A){var E;for(E=0;E<i;E+=1)t[E].animation.setDirection(k,A)}function S(k){var A;for(A=0;A<i;A+=1)t[A].animation.play(k)}function o(k){var A=k-r,E;for(E=0;E<i;E+=1)t[E].animation.advanceTime(A);r=k,s&&!n?window.requestAnimationFrame(o):a=!0}function f(k){r=k,window.requestAnimationFrame(o)}function l(k){var A;for(A=0;A<i;A+=1)t[A].animation.pause(k)}function h(k,A,E){var T;for(T=0;T<i;T+=1)t[T].animation.goToAndStop(k,A,E)}function y(k){var A;for(A=0;A<i;A+=1)t[A].animation.stop(k)}function b(k){var A;for(A=0;A<i;A+=1)t[A].animation.togglePause(k)}function _(k){var A;for(A=i-1;A>=0;A-=1)t[A].animation.destroy(k)}function C(k,A,E){var T=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),V,M=T.length;for(V=0;V<M;V+=1)E&&T[V].setAttribute("data-bm-type",E),c(T[V],k);if(A&&M===0){E||(E="svg");var G=document.getElementsByTagName("body")[0];G.innerText="";var N=createTag("div");N.style.width="100%",N.style.height="100%",N.setAttribute("data-bm-type",E),G.appendChild(N),c(N,k)}}function F(){var k;for(k=0;k<i;k+=1)t[k].animation.resize()}function L(){!n&&s&&a&&(window.requestAnimationFrame(f),a=!1)}function R(){n=!0}function I(){n=!1,L()}function D(k,A){var E;for(E=0;E<i;E+=1)t[E].animation.setVolume(k,A)}function B(k){var A;for(A=0;A<i;A+=1)t[A].animation.mute(k)}function w(k){var A;for(A=0;A<i;A+=1)t[A].animation.unmute(k)}return e.registerAnimation=c,e.loadAnimation=P,e.setSpeed=v,e.setDirection=d,e.play=S,e.pause=l,e.stop=y,e.togglePause=b,e.searchAnimations=C,e.resize=F,e.goToAndStop=h,e.destroy=_,e.freeze=R,e.unfreeze=I,e.setVolume=D,e.mute=B,e.unmute=w,e.getRegisteredAnimations=m,e}(),BezierFactory=function(){var e={};e.getBezierEasing=r;var t={};function r(f,l,h,y,b){var _=b||("bez_"+f+"_"+l+"_"+h+"_"+y).replace(/\./g,"p");if(t[_])return t[_];var C=new o([f,l,h,y]);return t[_]=C,C}var i=4,s=.001,a=1e-7,n=10,p=11,c=1/(p-1),m=typeof Float32Array=="function";function g(f,l){return 1-3*l+3*f}function x(f,l){return 3*l-6*f}function u(f){return 3*f}function P(f,l,h){return((g(l,h)*f+x(l,h))*f+u(l))*f}function v(f,l,h){return 3*g(l,h)*f*f+2*x(l,h)*f+u(l)}function d(f,l,h,y,b){var _,C,F=0;do C=l+(h-l)/2,_=P(C,y,b)-f,_>0?h=C:l=C;while(Math.abs(_)>a&&++F<n);return C}function S(f,l,h,y){for(var b=0;b<i;++b){var _=v(l,h,y);if(_===0)return l;var C=P(l,h,y)-f;l-=C/_}return l}function o(f){this._p=f,this._mSampleValues=m?new Float32Array(p):new Array(p),this._precomputed=!1,this.get=this.get.bind(this)}return o.prototype={get:function(l){var h=this._p[0],y=this._p[1],b=this._p[2],_=this._p[3];return this._precomputed||this._precompute(),h===y&&b===_?l:l===0?0:l===1?1:P(this._getTForX(l),y,_)},_precompute:function(){var l=this._p[0],h=this._p[1],y=this._p[2],b=this._p[3];this._precomputed=!0,(l!==h||y!==b)&&this._calcSampleValues()},_calcSampleValues:function(){for(var l=this._p[0],h=this._p[2],y=0;y<p;++y)this._mSampleValues[y]=P(y*c,l,h)},_getTForX:function(l){for(var h=this._p[0],y=this._p[2],b=this._mSampleValues,_=0,C=1,F=p-1;C!==F&&b[C]<=l;++C)_+=c;--C;var L=(l-b[C])/(b[C+1]-b[C]),R=_+L*c,I=v(R,h,y);return I>=s?S(l,R,h,y):I===0?R:d(l,_,_+c,h,y)}},e}(),pooling=function(){function e(t){return t.concat(createSizedArray(t.length))}return{double:e}}(),poolFactory=function(){return function(e,t,r){var i=0,s=e,a=createSizedArray(s),n={newElement:p,release:c};function p(){var m;return i?(i-=1,m=a[i]):m=t(),m}function c(m){i===s&&(a=pooling.double(a),s*=2),r&&r(m),a[i]=m,i+=1}return n}}(),bezierLengthPool=function(){function e(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}}return poolFactory(8,e)}(),segmentsLengthPool=function(){function e(){return{lengths:[],totalLength:0}}function t(r){var i,s=r.lengths.length;for(i=0;i<s;i+=1)bezierLengthPool.release(r.lengths[i]);r.lengths.length=0}return poolFactory(8,e,t)}();function bezFunction(){var e=Math;function t(u,P,v,d,S,o){var f=u*d+P*S+v*o-S*d-o*u-v*P;return f>-.001&&f<.001}function r(u,P,v,d,S,o,f,l,h){if(v===0&&o===0&&h===0)return t(u,P,d,S,f,l);var y=e.sqrt(e.pow(d-u,2)+e.pow(S-P,2)+e.pow(o-v,2)),b=e.sqrt(e.pow(f-u,2)+e.pow(l-P,2)+e.pow(h-v,2)),_=e.sqrt(e.pow(f-d,2)+e.pow(l-S,2)+e.pow(h-o,2)),C;return y>b?y>_?C=y-b-_:C=_-b-y:_>b?C=_-b-y:C=b-y-_,C>-1e-4&&C<1e-4}var i=function(){return function(u,P,v,d){var S=getDefaultCurveSegments(),o,f,l,h,y,b=0,_,C=[],F=[],L=bezierLengthPool.newElement();for(l=v.length,o=0;o<S;o+=1){for(y=o/(S-1),_=0,f=0;f<l;f+=1)h=bmPow(1-y,3)*u[f]+3*bmPow(1-y,2)*y*v[f]+3*(1-y)*bmPow(y,2)*d[f]+bmPow(y,3)*P[f],C[f]=h,F[f]!==null&&(_+=bmPow(C[f]-F[f],2)),F[f]=C[f];_&&(_=bmSqrt(_),b+=_),L.percents[o]=y,L.lengths[o]=b}return L.addedLength=b,L}}();function s(u){var P=segmentsLengthPool.newElement(),v=u.c,d=u.v,S=u.o,o=u.i,f,l=u._length,h=P.lengths,y=0;for(f=0;f<l-1;f+=1)h[f]=i(d[f],d[f+1],S[f],o[f+1]),y+=h[f].addedLength;return v&&l&&(h[f]=i(d[f],d[0],S[f],o[0]),y+=h[f].addedLength),P.totalLength=y,P}function a(u){this.segmentLength=0,this.points=new Array(u)}function n(u,P){this.partialLength=u,this.point=P}var p=function(){var u={};return function(P,v,d,S){var o=(P[0]+"_"+P[1]+"_"+v[0]+"_"+v[1]+"_"+d[0]+"_"+d[1]+"_"+S[0]+"_"+S[1]).replace(/\./g,"p");if(!u[o]){var f=getDefaultCurveSegments(),l,h,y,b,_,C=0,F,L,R=null;P.length===2&&(P[0]!==v[0]||P[1]!==v[1])&&t(P[0],P[1],v[0],v[1],P[0]+d[0],P[1]+d[1])&&t(P[0],P[1],v[0],v[1],v[0]+S[0],v[1]+S[1])&&(f=2);var I=new a(f);for(y=d.length,l=0;l<f;l+=1){for(L=createSizedArray(y),_=l/(f-1),F=0,h=0;h<y;h+=1)b=bmPow(1-_,3)*P[h]+3*bmPow(1-_,2)*_*(P[h]+d[h])+3*(1-_)*bmPow(_,2)*(v[h]+S[h])+bmPow(_,3)*v[h],L[h]=b,R!==null&&(F+=bmPow(L[h]-R[h],2));F=bmSqrt(F),C+=F,I.points[l]=new n(F,L),R=L}I.segmentLength=C,u[o]=I}return u[o]}}();function c(u,P){var v=P.percents,d=P.lengths,S=v.length,o=bmFloor((S-1)*u),f=u*P.addedLength,l=0;if(o===S-1||o===0||f===d[o])return v[o];for(var h=d[o]>f?-1:1,y=!0;y;)if(d[o]<=f&&d[o+1]>f?(l=(f-d[o])/(d[o+1]-d[o]),y=!1):o+=h,o<0||o>=S-1){if(o===S-1)return v[o];y=!1}return v[o]+(v[o+1]-v[o])*l}function m(u,P,v,d,S,o){var f=c(S,o),l=1-f,h=e.round((l*l*l*u[0]+(f*l*l+l*f*l+l*l*f)*v[0]+(f*f*l+l*f*f+f*l*f)*d[0]+f*f*f*P[0])*1e3)/1e3,y=e.round((l*l*l*u[1]+(f*l*l+l*f*l+l*l*f)*v[1]+(f*f*l+l*f*f+f*l*f)*d[1]+f*f*f*P[1])*1e3)/1e3;return[h,y]}var g=createTypedArray("float32",8);function x(u,P,v,d,S,o,f){S<0?S=0:S>1&&(S=1);var l=c(S,f);o=o>1?1:o;var h=c(o,f),y,b=u.length,_=1-l,C=1-h,F=_*_*_,L=l*_*_*3,R=l*l*_*3,I=l*l*l,D=_*_*C,B=l*_*C+_*l*C+_*_*h,w=l*l*C+_*l*h+l*_*h,k=l*l*h,A=_*C*C,E=l*C*C+_*h*C+_*C*h,T=l*h*C+_*h*h+l*C*h,V=l*h*h,M=C*C*C,G=h*C*C+C*h*C+C*C*h,N=h*h*C+C*h*h+h*C*h,z=h*h*h;for(y=0;y<b;y+=1)g[y*4]=e.round((F*u[y]+L*v[y]+R*d[y]+I*P[y])*1e3)/1e3,g[y*4+1]=e.round((D*u[y]+B*v[y]+w*d[y]+k*P[y])*1e3)/1e3,g[y*4+2]=e.round((A*u[y]+E*v[y]+T*d[y]+V*P[y])*1e3)/1e3,g[y*4+3]=e.round((M*u[y]+G*v[y]+N*d[y]+z*P[y])*1e3)/1e3;return g}return{getSegmentsLength:s,getNewSegment:x,getPointInSegment:m,buildBezierData:p,pointOnLine2D:t,pointOnLine3D:r}}var bez=bezFunction(),PropertyFactory=function(){var e=initialDefaultFrame,t=Math.abs;function r(S,o){var f=this.offsetTime,l;this.propType==="multidimensional"&&(l=createTypedArray("float32",this.pv.length));for(var h=o.lastIndex,y=h,b=this.keyframes.length-1,_=!0,C,F,L;_;){if(C=this.keyframes[y],F=this.keyframes[y+1],y===b-1&&S>=F.t-f){C.h&&(C=F),h=0;break}if(F.t-f>S){h=y;break}y<b-1?y+=1:(h=0,_=!1)}L=this.keyframesMetadata[y]||{};var R,I,D,B,w,k,A=F.t-f,E=C.t-f,T;if(C.to){L.bezierData||(L.bezierData=bez.buildBezierData(C.s,F.s||C.e,C.to,C.ti));var V=L.bezierData;if(S>=A||S<E){var M=S>=A?V.points.length-1:0;for(I=V.points[M].point.length,R=0;R<I;R+=1)l[R]=V.points[M].point[R]}else{L.__fnct?k=L.__fnct:(k=BezierFactory.getBezierEasing(C.o.x,C.o.y,C.i.x,C.i.y,C.n).get,L.__fnct=k),D=k((S-E)/(A-E));var G=V.segmentLength*D,N,z=o.lastFrame<S&&o._lastKeyframeIndex===y?o._lastAddedLength:0;for(w=o.lastFrame<S&&o._lastKeyframeIndex===y?o._lastPoint:0,_=!0,B=V.points.length;_;){if(z+=V.points[w].partialLength,G===0||D===0||w===V.points.length-1){for(I=V.points[w].point.length,R=0;R<I;R+=1)l[R]=V.points[w].point[R];break}else if(G>=z&&G<z+V.points[w+1].partialLength){for(N=(G-z)/V.points[w+1].partialLength,I=V.points[w].point.length,R=0;R<I;R+=1)l[R]=V.points[w].point[R]+(V.points[w+1].point[R]-V.points[w].point[R])*N;break}w<B-1?w+=1:_=!1}o._lastPoint=w,o._lastAddedLength=z-V.points[w].partialLength,o._lastKeyframeIndex=y}}else{var j,H,Y,K,U;if(b=C.s.length,T=F.s||C.e,this.sh&&C.h!==1)if(S>=A)l[0]=T[0],l[1]=T[1],l[2]=T[2];else if(S<=E)l[0]=C.s[0],l[1]=C.s[1],l[2]=C.s[2];else{var $=a(C.s),q=a(T),X=(S-E)/(A-E);s(l,i($,q,X))}else for(y=0;y<b;y+=1)C.h!==1&&(S>=A?D=1:S<E?D=0:(C.o.x.constructor===Array?(L.__fnct||(L.__fnct=[]),L.__fnct[y]?k=L.__fnct[y]:(j=C.o.x[y]===void 0?C.o.x[0]:C.o.x[y],H=C.o.y[y]===void 0?C.o.y[0]:C.o.y[y],Y=C.i.x[y]===void 0?C.i.x[0]:C.i.x[y],K=C.i.y[y]===void 0?C.i.y[0]:C.i.y[y],k=BezierFactory.getBezierEasing(j,H,Y,K).get,L.__fnct[y]=k)):L.__fnct?k=L.__fnct:(j=C.o.x,H=C.o.y,Y=C.i.x,K=C.i.y,k=BezierFactory.getBezierEasing(j,H,Y,K).get,C.keyframeMetadata=k),D=k((S-E)/(A-E)))),T=F.s||C.e,U=C.h===1?C.s[y]:C.s[y]+(T[y]-C.s[y])*D,this.propType==="multidimensional"?l[y]=U:l=U}return o.lastIndex=h,l}function i(S,o,f){var l=[],h=S[0],y=S[1],b=S[2],_=S[3],C=o[0],F=o[1],L=o[2],R=o[3],I,D,B,w,k;return D=h*C+y*F+b*L+_*R,D<0&&(D=-D,C=-C,F=-F,L=-L,R=-R),1-D>1e-6?(I=Math.acos(D),B=Math.sin(I),w=Math.sin((1-f)*I)/B,k=Math.sin(f*I)/B):(w=1-f,k=f),l[0]=w*h+k*C,l[1]=w*y+k*F,l[2]=w*b+k*L,l[3]=w*_+k*R,l}function s(S,o){var f=o[0],l=o[1],h=o[2],y=o[3],b=Math.atan2(2*l*y-2*f*h,1-2*l*l-2*h*h),_=Math.asin(2*f*l+2*h*y),C=Math.atan2(2*f*y-2*l*h,1-2*f*f-2*h*h);S[0]=b/degToRads,S[1]=_/degToRads,S[2]=C/degToRads}function a(S){var o=S[0]*degToRads,f=S[1]*degToRads,l=S[2]*degToRads,h=Math.cos(o/2),y=Math.cos(f/2),b=Math.cos(l/2),_=Math.sin(o/2),C=Math.sin(f/2),F=Math.sin(l/2),L=h*y*b-_*C*F,R=_*C*b+h*y*F,I=_*y*b+h*C*F,D=h*C*b-_*y*F;return[R,I,D,L]}function n(){var S=this.comp.renderedFrame-this.offsetTime,o=this.keyframes[0].t-this.offsetTime,f=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(S===this._caching.lastFrame||this._caching.lastFrame!==e&&(this._caching.lastFrame>=f&&S>=f||this._caching.lastFrame<o&&S<o))){this._caching.lastFrame>=S&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var l=this.interpolateValue(S,this._caching);this.pv=l}return this._caching.lastFrame=S,this.pv}function p(S){var o;if(this.propType==="unidimensional")o=S*this.mult,t(this.v-o)>1e-5&&(this.v=o,this._mdf=!0);else for(var f=0,l=this.v.length;f<l;)o=S[f]*this.mult,t(this.v[f]-o)>1e-5&&(this.v[f]=o,this._mdf=!0),f+=1}function c(){if(!(this.elem.globalData.frameId===this.frameId||!this.effectsSequence.length)){if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=this._isFirstFrame;var S,o=this.effectsSequence.length,f=this.kf?this.pv:this.data.k;for(S=0;S<o;S+=1)f=this.effectsSequence[S](f);this.setVValue(f),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function m(S){this.effectsSequence.push(S),this.container.addDynamicProperty(this)}function g(S,o,f,l){this.propType="unidimensional",this.mult=f||1,this.data=o,this.v=f?o.k*f:o.k,this.pv=o.k,this._mdf=!1,this.elem=S,this.container=l,this.comp=S.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=c,this.setVValue=p,this.addEffect=m}function x(S,o,f,l){this.propType="multidimensional",this.mult=f||1,this.data=o,this._mdf=!1,this.elem=S,this.container=l,this.comp=S.comp,this.k=!1,this.kf=!1,this.frameId=-1;var h,y=o.k.length;for(this.v=createTypedArray("float32",y),this.pv=createTypedArray("float32",y),this.vel=createTypedArray("float32",y),h=0;h<y;h+=1)this.v[h]=o.k[h]*this.mult,this.pv[h]=o.k[h];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=c,this.setVValue=p,this.addEffect=m}function u(S,o,f,l){this.propType="unidimensional",this.keyframes=o.k,this.keyframesMetadata=[],this.offsetTime=S.data.st,this.frameId=-1,this._caching={lastFrame:e,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=o,this.mult=f||1,this.elem=S,this.container=l,this.comp=S.comp,this.v=e,this.pv=e,this._isFirstFrame=!0,this.getValue=c,this.setVValue=p,this.interpolateValue=r,this.effectsSequence=[n.bind(this)],this.addEffect=m}function P(S,o,f,l){this.propType="multidimensional";var h,y=o.k.length,b,_,C,F;for(h=0;h<y-1;h+=1)o.k[h].to&&o.k[h].s&&o.k[h+1]&&o.k[h+1].s&&(b=o.k[h].s,_=o.k[h+1].s,C=o.k[h].to,F=o.k[h].ti,(b.length===2&&!(b[0]===_[0]&&b[1]===_[1])&&bez.pointOnLine2D(b[0],b[1],_[0],_[1],b[0]+C[0],b[1]+C[1])&&bez.pointOnLine2D(b[0],b[1],_[0],_[1],_[0]+F[0],_[1]+F[1])||b.length===3&&!(b[0]===_[0]&&b[1]===_[1]&&b[2]===_[2])&&bez.pointOnLine3D(b[0],b[1],b[2],_[0],_[1],_[2],b[0]+C[0],b[1]+C[1],b[2]+C[2])&&bez.pointOnLine3D(b[0],b[1],b[2],_[0],_[1],_[2],_[0]+F[0],_[1]+F[1],_[2]+F[2]))&&(o.k[h].to=null,o.k[h].ti=null),b[0]===_[0]&&b[1]===_[1]&&C[0]===0&&C[1]===0&&F[0]===0&&F[1]===0&&(b.length===2||b[2]===_[2]&&C[2]===0&&F[2]===0)&&(o.k[h].to=null,o.k[h].ti=null));this.effectsSequence=[n.bind(this)],this.data=o,this.keyframes=o.k,this.keyframesMetadata=[],this.offsetTime=S.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=f||1,this.elem=S,this.container=l,this.comp=S.comp,this.getValue=c,this.setVValue=p,this.interpolateValue=r,this.frameId=-1;var L=o.k[0].s.length;for(this.v=createTypedArray("float32",L),this.pv=createTypedArray("float32",L),h=0;h<L;h+=1)this.v[h]=e,this.pv[h]=e;this._caching={lastFrame:e,lastIndex:0,value:createTypedArray("float32",L)},this.addEffect=m}function v(S,o,f,l,h){var y;if(!o.k.length)y=new g(S,o,l,h);else if(typeof o.k[0]=="number")y=new x(S,o,l,h);else switch(f){case 0:y=new u(S,o,l,h);break;case 1:y=new P(S,o,l,h);break;default:break}return y.effectsSequence.length&&h.addDynamicProperty(y),y}var d={getProp:v};return d}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){this.dynamicProperties.indexOf(t)===-1&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){this._mdf=!1;var t,r=this.dynamicProperties.length;for(t=0;t<r;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=function(){function e(){return createTypedArray("float32",2)}return poolFactory(8,e)}();function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(e,t){this.c=e,this.setLength(t);for(var r=0;r<t;)this.v[r]=pointPool.newElement(),this.o[r]=pointPool.newElement(),this.i[r]=pointPool.newElement(),r+=1},ShapePath.prototype.setLength=function(e){for(;this._maxLength<e;)this.doubleArrayLength();this._length=e},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(e,t,r,i,s){var a;switch(this._length=Math.max(this._length,i+1),this._length>=this._maxLength&&this.doubleArrayLength(),r){case"v":a=this.v;break;case"i":a=this.i;break;case"o":a=this.o;break;default:a=[];break}(!a[i]||a[i]&&!s)&&(a[i]=pointPool.newElement()),a[i][0]=e,a[i][1]=t},ShapePath.prototype.setTripleAt=function(e,t,r,i,s,a,n,p){this.setXYAt(e,t,"v",n,p),this.setXYAt(r,i,"o",n,p),this.setXYAt(s,a,"i",n,p)},ShapePath.prototype.reverse=function(){var e=new ShapePath;e.setPathData(this.c,this._length);var t=this.v,r=this.o,i=this.i,s=0;this.c&&(e.setTripleAt(t[0][0],t[0][1],i[0][0],i[0][1],r[0][0],r[0][1],0,!1),s=1);var a=this._length-1,n=this._length,p;for(p=s;p<n;p+=1)e.setTripleAt(t[a][0],t[a][1],i[a][0],i[a][1],r[a][0],r[a][1],p,!1),a-=1;return e};var shapePool=function(){function e(){return new ShapePath}function t(s){var a=s._length,n;for(n=0;n<a;n+=1)pointPool.release(s.v[n]),pointPool.release(s.i[n]),pointPool.release(s.o[n]),s.v[n]=null,s.i[n]=null,s.o[n]=null;s._length=0,s.c=!1}function r(s){var a=i.newElement(),n,p=s._length===void 0?s.v.length:s._length;for(a.setLength(p),a.c=s.c,n=0;n<p;n+=1)a.setTripleAt(s.v[n][0],s.v[n][1],s.o[n][0],s.o[n][1],s.i[n][0],s.i[n][1],n);return a}var i=poolFactory(4,e,t);return i.clone=r,i}();function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(e){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=e,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var e;for(e=0;e<this._length;e+=1)shapePool.release(this.shapes[e]);this._length=0};var shapeCollectionPool=function(){var e={newShapeCollection:s,release:a},t=0,r=4,i=createSizedArray(r);function s(){var n;return t?(t-=1,n=i[t]):n=new ShapeCollection,n}function a(n){var p,c=n._length;for(p=0;p<c;p+=1)shapePool.release(n.shapes[p]);n._length=0,t===r&&(i=pooling.double(i),r*=2),i[t]=n,t+=1}return e}(),ShapePropertyFactory=function(){var e=-999999;function t(o,f,l){var h=l.lastIndex,y,b,_,C,F,L,R,I,D,B=this.keyframes;if(o<B[0].t-this.offsetTime)y=B[0].s[0],_=!0,h=0;else if(o>=B[B.length-1].t-this.offsetTime)y=B[B.length-1].s?B[B.length-1].s[0]:B[B.length-2].e[0],_=!0;else{for(var w=h,k=B.length-1,A=!0,E,T,V;A&&(E=B[w],T=B[w+1],!(T.t-this.offsetTime>o));)w<k-1?w+=1:A=!1;if(V=this.keyframesMetadata[w]||{},_=E.h===1,h=w,!_){if(o>=T.t-this.offsetTime)I=1;else if(o<E.t-this.offsetTime)I=0;else{var M;V.__fnct?M=V.__fnct:(M=BezierFactory.getBezierEasing(E.o.x,E.o.y,E.i.x,E.i.y).get,V.__fnct=M),I=M((o-(E.t-this.offsetTime))/(T.t-this.offsetTime-(E.t-this.offsetTime)))}b=T.s?T.s[0]:E.e[0]}y=E.s[0]}for(L=f._length,R=y.i[0].length,l.lastIndex=h,C=0;C<L;C+=1)for(F=0;F<R;F+=1)D=_?y.i[C][F]:y.i[C][F]+(b.i[C][F]-y.i[C][F])*I,f.i[C][F]=D,D=_?y.o[C][F]:y.o[C][F]+(b.o[C][F]-y.o[C][F])*I,f.o[C][F]=D,D=_?y.v[C][F]:y.v[C][F]+(b.v[C][F]-y.v[C][F])*I,f.v[C][F]=D}function r(){var o=this.comp.renderedFrame-this.offsetTime,f=this.keyframes[0].t-this.offsetTime,l=this.keyframes[this.keyframes.length-1].t-this.offsetTime,h=this._caching.lastFrame;return h!==e&&(h<f&&o<f||h>l&&o>l)||(this._caching.lastIndex=h<o?this._caching.lastIndex:0,this.interpolateShape(o,this.pv,this._caching)),this._caching.lastFrame=o,this.pv}function i(){this.paths=this.localShapeCollection}function s(o,f){if(o._length!==f._length||o.c!==f.c)return!1;var l,h=o._length;for(l=0;l<h;l+=1)if(o.v[l][0]!==f.v[l][0]||o.v[l][1]!==f.v[l][1]||o.o[l][0]!==f.o[l][0]||o.o[l][1]!==f.o[l][1]||o.i[l][0]!==f.i[l][0]||o.i[l][1]!==f.i[l][1])return!1;return!0}function a(o){s(this.v,o)||(this.v=shapePool.clone(o),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId){if(!this.effectsSequence.length){this._mdf=!1;return}if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=!1;var o;this.kf?o=this.pv:this.data.ks?o=this.data.ks.k:o=this.data.pt.k;var f,l=this.effectsSequence.length;for(f=0;f<l;f+=1)o=this.effectsSequence[f](o);this.setVValue(o),this.lock=!1,this.frameId=this.elem.globalData.frameId}}function p(o,f,l){this.propType="shape",this.comp=o.comp,this.container=o,this.elem=o,this.data=f,this.k=!1,this.kf=!1,this._mdf=!1;var h=l===3?f.pt.k:f.ks.k;this.v=shapePool.clone(h),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=i,this.effectsSequence=[]}function c(o){this.effectsSequence.push(o),this.container.addDynamicProperty(this)}p.prototype.interpolateShape=t,p.prototype.getValue=n,p.prototype.setVValue=a,p.prototype.addEffect=c;function m(o,f,l){this.propType="shape",this.comp=o.comp,this.elem=o,this.container=o,this.offsetTime=o.data.st,this.keyframes=l===3?f.pt.k:f.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var h=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,h),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=e,this.reset=i,this._caching={lastFrame:e,lastIndex:0},this.effectsSequence=[r.bind(this)]}m.prototype.getValue=n,m.prototype.interpolateShape=t,m.prototype.setVValue=a,m.prototype.addEffect=c;var g=function(){var o=roundCorner;function f(l,h){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=h.d,this.elem=l,this.comp=l.comp,this.frameId=-1,this.initDynamicPropertyContainer(l),this.p=PropertyFactory.getProp(l,h.p,1,0,this),this.s=PropertyFactory.getProp(l,h.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return f.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var h=this.p.v[0],y=this.p.v[1],b=this.s.v[0]/2,_=this.s.v[1]/2,C=this.d!==3,F=this.v;F.v[0][0]=h,F.v[0][1]=y-_,F.v[1][0]=C?h+b:h-b,F.v[1][1]=y,F.v[2][0]=h,F.v[2][1]=y+_,F.v[3][0]=C?h-b:h+b,F.v[3][1]=y,F.i[0][0]=C?h-b*o:h+b*o,F.i[0][1]=y-_,F.i[1][0]=C?h+b:h-b,F.i[1][1]=y-_*o,F.i[2][0]=C?h+b*o:h-b*o,F.i[2][1]=y+_,F.i[3][0]=C?h-b:h+b,F.i[3][1]=y+_*o,F.o[0][0]=C?h+b*o:h-b*o,F.o[0][1]=y-_,F.o[1][0]=C?h+b:h-b,F.o[1][1]=y+_*o,F.o[2][0]=C?h-b*o:h+b*o,F.o[2][1]=y+_,F.o[3][0]=C?h-b:h+b,F.o[3][1]=y-_*o}},extendPrototype([DynamicPropertyContainer],f),f}(),x=function(){function o(f,l){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=f,this.comp=f.comp,this.data=l,this.frameId=-1,this.d=l.d,this.initDynamicPropertyContainer(f),l.sy===1?(this.ir=PropertyFactory.getProp(f,l.ir,0,0,this),this.is=PropertyFactory.getProp(f,l.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(f,l.pt,0,0,this),this.p=PropertyFactory.getProp(f,l.p,1,0,this),this.r=PropertyFactory.getProp(f,l.r,0,degToRads,this),this.or=PropertyFactory.getProp(f,l.or,0,0,this),this.os=PropertyFactory.getProp(f,l.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return o.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var l=Math.floor(this.pt.v)*2,h=Math.PI*2/l,y=!0,b=this.or.v,_=this.ir.v,C=this.os.v,F=this.is.v,L=2*Math.PI*b/(l*2),R=2*Math.PI*_/(l*2),I,D,B,w,k=-Math.PI/2;k+=this.r.v;var A=this.data.d===3?-1:1;for(this.v._length=0,I=0;I<l;I+=1){D=y?b:_,B=y?C:F,w=y?L:R;var E=D*Math.cos(k),T=D*Math.sin(k),V=E===0&&T===0?0:T/Math.sqrt(E*E+T*T),M=E===0&&T===0?0:-E/Math.sqrt(E*E+T*T);E+=+this.p.v[0],T+=+this.p.v[1],this.v.setTripleAt(E,T,E-V*w*B*A,T-M*w*B*A,E+V*w*B*A,T+M*w*B*A,I,!0),y=!y,k+=h*A}},convertPolygonToPath:function(){var l=Math.floor(this.pt.v),h=Math.PI*2/l,y=this.or.v,b=this.os.v,_=2*Math.PI*y/(l*4),C,F=-Math.PI*.5,L=this.data.d===3?-1:1;for(F+=this.r.v,this.v._length=0,C=0;C<l;C+=1){var R=y*Math.cos(F),I=y*Math.sin(F),D=R===0&&I===0?0:I/Math.sqrt(R*R+I*I),B=R===0&&I===0?0:-R/Math.sqrt(R*R+I*I);R+=+this.p.v[0],I+=+this.p.v[1],this.v.setTripleAt(R,I,R-D*_*b*L,I-B*_*b*L,R+D*_*b*L,I+B*_*b*L,C,!0),F+=h*L}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],o),o}(),u=function(){function o(f,l){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=f,this.comp=f.comp,this.frameId=-1,this.d=l.d,this.initDynamicPropertyContainer(f),this.p=PropertyFactory.getProp(f,l.p,1,0,this),this.s=PropertyFactory.getProp(f,l.s,1,0,this),this.r=PropertyFactory.getProp(f,l.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return o.prototype={convertRectToPath:function(){var l=this.p.v[0],h=this.p.v[1],y=this.s.v[0]/2,b=this.s.v[1]/2,_=bmMin(y,b,this.r.v),C=_*(1-roundCorner);this.v._length=0,this.d===2||this.d===1?(this.v.setTripleAt(l+y,h-b+_,l+y,h-b+_,l+y,h-b+C,0,!0),this.v.setTripleAt(l+y,h+b-_,l+y,h+b-C,l+y,h+b-_,1,!0),_!==0?(this.v.setTripleAt(l+y-_,h+b,l+y-_,h+b,l+y-C,h+b,2,!0),this.v.setTripleAt(l-y+_,h+b,l-y+C,h+b,l-y+_,h+b,3,!0),this.v.setTripleAt(l-y,h+b-_,l-y,h+b-_,l-y,h+b-C,4,!0),this.v.setTripleAt(l-y,h-b+_,l-y,h-b+C,l-y,h-b+_,5,!0),this.v.setTripleAt(l-y+_,h-b,l-y+_,h-b,l-y+C,h-b,6,!0),this.v.setTripleAt(l+y-_,h-b,l+y-C,h-b,l+y-_,h-b,7,!0)):(this.v.setTripleAt(l-y,h+b,l-y+C,h+b,l-y,h+b,2),this.v.setTripleAt(l-y,h-b,l-y,h-b+C,l-y,h-b,3))):(this.v.setTripleAt(l+y,h-b+_,l+y,h-b+C,l+y,h-b+_,0,!0),_!==0?(this.v.setTripleAt(l+y-_,h-b,l+y-_,h-b,l+y-C,h-b,1,!0),this.v.setTripleAt(l-y+_,h-b,l-y+C,h-b,l-y+_,h-b,2,!0),this.v.setTripleAt(l-y,h-b+_,l-y,h-b+_,l-y,h-b+C,3,!0),this.v.setTripleAt(l-y,h+b-_,l-y,h+b-C,l-y,h+b-_,4,!0),this.v.setTripleAt(l-y+_,h+b,l-y+_,h+b,l-y+C,h+b,5,!0),this.v.setTripleAt(l+y-_,h+b,l+y-C,h+b,l+y-_,h+b,6,!0),this.v.setTripleAt(l+y,h+b-_,l+y,h+b-_,l+y,h+b-C,7,!0)):(this.v.setTripleAt(l-y,h-b,l-y+C,h-b,l-y,h-b,1,!0),this.v.setTripleAt(l-y,h+b,l-y,h+b-C,l-y,h+b,2,!0),this.v.setTripleAt(l+y,h+b,l+y-C,h+b,l+y,h+b,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:i},extendPrototype([DynamicPropertyContainer],o),o}();function P(o,f,l){var h;if(l===3||l===4){var y=l===3?f.pt:f.ks,b=y.k;b.length?h=new m(o,f,l):h=new p(o,f,l)}else l===5?h=new u(o,f):l===6?h=new g(o,f):l===7&&(h=new x(o,f));return h.k&&o.addDynamicProperty(h),h}function v(){return p}function d(){return m}var S={};return S.getShapeProp=P,S.getConstructorFunction=v,S.getKeyframedConstructorFunction=d,S}();var Matrix=function(){var e=Math.cos,t=Math.sin,r=Math.tan,i=Math.round;function s(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function a(A){if(A===0)return this;var E=e(A),T=t(A);return this._t(E,-T,0,0,T,E,0,0,0,0,1,0,0,0,0,1)}function n(A){if(A===0)return this;var E=e(A),T=t(A);return this._t(1,0,0,0,0,E,-T,0,0,T,E,0,0,0,0,1)}function p(A){if(A===0)return this;var E=e(A),T=t(A);return this._t(E,0,T,0,0,1,0,0,-T,0,E,0,0,0,0,1)}function c(A){if(A===0)return this;var E=e(A),T=t(A);return this._t(E,-T,0,0,T,E,0,0,0,0,1,0,0,0,0,1)}function m(A,E){return this._t(1,E,A,1,0,0)}function g(A,E){return this.shear(r(A),r(E))}function x(A,E){var T=e(E),V=t(E);return this._t(T,V,0,0,-V,T,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,r(A),1,0,0,0,0,1,0,0,0,0,1)._t(T,-V,0,0,V,T,0,0,0,0,1,0,0,0,0,1)}function u(A,E,T){return!T&&T!==0&&(T=1),A===1&&E===1&&T===1?this:this._t(A,0,0,0,0,E,0,0,0,0,T,0,0,0,0,1)}function P(A,E,T,V,M,G,N,z,j,H,Y,K,U,$,q,X){return this.props[0]=A,this.props[1]=E,this.props[2]=T,this.props[3]=V,this.props[4]=M,this.props[5]=G,this.props[6]=N,this.props[7]=z,this.props[8]=j,this.props[9]=H,this.props[10]=Y,this.props[11]=K,this.props[12]=U,this.props[13]=$,this.props[14]=q,this.props[15]=X,this}function v(A,E,T){return T=T||0,A!==0||E!==0||T!==0?this._t(1,0,0,0,0,1,0,0,0,0,1,0,A,E,T,1):this}function d(A,E,T,V,M,G,N,z,j,H,Y,K,U,$,q,X){var O=this.props;if(A===1&&E===0&&T===0&&V===0&&M===0&&G===1&&N===0&&z===0&&j===0&&H===0&&Y===1&&K===0)return O[12]=O[12]*A+O[15]*U,O[13]=O[13]*G+O[15]*$,O[14]=O[14]*Y+O[15]*q,O[15]*=X,this._identityCalculated=!1,this;var W=O[0],te=O[1],ae=O[2],re=O[3],ee=O[4],ie=O[5],se=O[6],Z=O[7],ne=O[8],oe=O[9],J=O[10],he=O[11],Q=O[12],le=O[13],fe=O[14],pe=O[15];return O[0]=W*A+te*M+ae*j+re*U,O[1]=W*E+te*G+ae*H+re*$,O[2]=W*T+te*N+ae*Y+re*q,O[3]=W*V+te*z+ae*K+re*X,O[4]=ee*A+ie*M+se*j+Z*U,O[5]=ee*E+ie*G+se*H+Z*$,O[6]=ee*T+ie*N+se*Y+Z*q,O[7]=ee*V+ie*z+se*K+Z*X,O[8]=ne*A+oe*M+J*j+he*U,O[9]=ne*E+oe*G+J*H+he*$,O[10]=ne*T+oe*N+J*Y+he*q,O[11]=ne*V+oe*z+J*K+he*X,O[12]=Q*A+le*M+fe*j+pe*U,O[13]=Q*E+le*G+fe*H+pe*$,O[14]=Q*T+le*N+fe*Y+pe*q,O[15]=Q*V+le*z+fe*K+pe*X,this._identityCalculated=!1,this}function S(){return this._identityCalculated||(this._identity=!(this.props[0]!==1||this.props[1]!==0||this.props[2]!==0||this.props[3]!==0||this.props[4]!==0||this.props[5]!==1||this.props[6]!==0||this.props[7]!==0||this.props[8]!==0||this.props[9]!==0||this.props[10]!==1||this.props[11]!==0||this.props[12]!==0||this.props[13]!==0||this.props[14]!==0||this.props[15]!==1),this._identityCalculated=!0),this._identity}function o(A){for(var E=0;E<16;){if(A.props[E]!==this.props[E])return!1;E+=1}return!0}function f(A){var E;for(E=0;E<16;E+=1)A.props[E]=this.props[E];return A}function l(A){var E;for(E=0;E<16;E+=1)this.props[E]=A[E]}function h(A,E,T){return{x:A*this.props[0]+E*this.props[4]+T*this.props[8]+this.props[12],y:A*this.props[1]+E*this.props[5]+T*this.props[9]+this.props[13],z:A*this.props[2]+E*this.props[6]+T*this.props[10]+this.props[14]}}function y(A,E,T){return A*this.props[0]+E*this.props[4]+T*this.props[8]+this.props[12]}function b(A,E,T){return A*this.props[1]+E*this.props[5]+T*this.props[9]+this.props[13]}function _(A,E,T){return A*this.props[2]+E*this.props[6]+T*this.props[10]+this.props[14]}function C(){var A=this.props[0]*this.props[5]-this.props[1]*this.props[4],E=this.props[5]/A,T=-this.props[1]/A,V=-this.props[4]/A,M=this.props[0]/A,G=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/A,N=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/A,z=new Matrix;return z.props[0]=E,z.props[1]=T,z.props[4]=V,z.props[5]=M,z.props[12]=G,z.props[13]=N,z}function F(A){var E=this.getInverseMatrix();return E.applyToPointArray(A[0],A[1],A[2]||0)}function L(A){var E,T=A.length,V=[];for(E=0;E<T;E+=1)V[E]=F(A[E]);return V}function R(A,E,T){var V=createTypedArray("float32",6);if(this.isIdentity())V[0]=A[0],V[1]=A[1],V[2]=E[0],V[3]=E[1],V[4]=T[0],V[5]=T[1];else{var M=this.props[0],G=this.props[1],N=this.props[4],z=this.props[5],j=this.props[12],H=this.props[13];V[0]=A[0]*M+A[1]*N+j,V[1]=A[0]*G+A[1]*z+H,V[2]=E[0]*M+E[1]*N+j,V[3]=E[0]*G+E[1]*z+H,V[4]=T[0]*M+T[1]*N+j,V[5]=T[0]*G+T[1]*z+H}return V}function I(A,E,T){var V;return this.isIdentity()?V=[A,E,T]:V=[A*this.props[0]+E*this.props[4]+T*this.props[8]+this.props[12],A*this.props[1]+E*this.props[5]+T*this.props[9]+this.props[13],A*this.props[2]+E*this.props[6]+T*this.props[10]+this.props[14]],V}function D(A,E){if(this.isIdentity())return A+","+E;var T=this.props;return Math.round((A*T[0]+E*T[4]+T[12])*100)/100+","+Math.round((A*T[1]+E*T[5]+T[13])*100)/100}function B(){for(var A=0,E=this.props,T="matrix3d(",V=1e4;A<16;)T+=i(E[A]*V)/V,T+=A===15?")":",",A+=1;return T}function w(A){var E=1e4;return A<1e-6&&A>0||A>-1e-6&&A<0?i(A*E)/E:A}function k(){var A=this.props,E=w(A[0]),T=w(A[1]),V=w(A[4]),M=w(A[5]),G=w(A[12]),N=w(A[13]);return"matrix("+E+","+T+","+V+","+M+","+G+","+N+")"}return function(){this.reset=s,this.rotate=a,this.rotateX=n,this.rotateY=p,this.rotateZ=c,this.skew=g,this.skewFromAxis=x,this.shear=m,this.scale=u,this.setTransform=P,this.translate=v,this.transform=d,this.applyToPoint=h,this.applyToX=y,this.applyToY=b,this.applyToZ=_,this.applyToPointArray=I,this.applyToTriplePoints=R,this.applyToPointStringified=D,this.toCSS=B,this.to2dCSS=k,this.clone=f,this.cloneFromProps=l,this.equals=o,this.inversePoints=L,this.inversePoint=F,this.getInverseMatrix=C,this._t=this.transform,this.isIdentity=S,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$3=function(r){return typeof r}:_typeof$3=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$3(e)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(e){setLocationHref(e)}function searchAnimations(){standalone===!0?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(e){setSubframeEnabled(e)}function setPrefix(e){setIdPrefix(e)}function loadAnimation(e){return standalone===!0&&(e.animationData=JSON.parse(animationData)),animationManager.loadAnimation(e)}function setQuality(e){if(typeof e=="string")switch(e){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10);break}else!isNaN(e)&&e>1&&setDefaultCurveSegments(e);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return typeof navigator!="undefined"}function installPlugin(e,t){e==="expressions"&&setExpressionsPlugin(t)}function getFactory(e){switch(e){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.9.6";function checkReady(){document.readyState==="complete"&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(e){for(var t=queryString.split("&"),r=0;r<t.length;r+=1){var i=t[r].split("=");if(decodeURIComponent(i[0])==e)return decodeURIComponent(i[1])}return null}var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{!((typeof exports=="undefined"?"undefined":_typeof$3(exports))==="object"&&typeof module!="undefined")&&!(typeof define=="function"&&define.amd)&&(window.bodymovin=lottie)}catch(e){}var ShapeModifiers=function(){var e={},t={};e.registerModifier=r,e.getModifier=i;function r(s,a){t[s]||(t[s]=a)}function i(s,a,n){return new t[s](a,n)}return e}();function ShapeModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(e){if(!this.closed){e.sh.container.addDynamicProperty(e.sh);var t={shape:e.sh,data:e,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(t),this.addShapeToModifier(t),this._isAnimated&&e.setAsAnimated()}},ShapeModifier.prototype.init=function(e,t){this.shapes=[],this.elem=e,this.initDynamicPropertyContainer(e),this.initModifierProperties(e,t),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier);function TrimModifier(){}extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(e,t){this.s=PropertyFactory.getProp(e,t.s,0,.01,this),this.e=PropertyFactory.getProp(e,t.e,0,.01,this),this.o=PropertyFactory.getProp(e,t.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=t.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(e){e.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(e,t,r,i,s){var a=[];t<=1?a.push({s:e,e:t}):e>=1?a.push({s:e-1,e:t-1}):(a.push({s:e,e:1}),a.push({s:0,e:t-1}));var n=[],p,c=a.length,m;for(p=0;p<c;p+=1)if(m=a[p],!(m.e*s<i||m.s*s>i+r)){var g,x;m.s*s<=i?g=0:g=(m.s*s-i)/r,m.e*s>=i+r?x=1:x=(m.e*s-i)/r,n.push([g,x])}return n.length||n.push([0,0]),n},TrimModifier.prototype.releasePathsData=function(e){var t,r=e.length;for(t=0;t<r;t+=1)segmentsLengthPool.release(e[t]);return e.length=0,e},TrimModifier.prototype.processShapes=function(e){var t,r;if(this._mdf||e){var i=this.o.v%360/360;if(i<0&&(i+=1),this.s.v>1?t=1+i:this.s.v<0?t=0+i:t=this.s.v+i,this.e.v>1?r=1+i:this.e.v<0?r=0+i:r=this.e.v+i,t>r){var s=t;t=r,r=s}t=Math.round(t*1e4)*1e-4,r=Math.round(r*1e4)*1e-4,this.sValue=t,this.eValue=r}else t=this.sValue,r=this.eValue;var a,n,p=this.shapes.length,c,m,g,x,u,P=0;if(r===t)for(n=0;n<p;n+=1)this.shapes[n].localShapeCollection.releaseShapes(),this.shapes[n].shape._mdf=!0,this.shapes[n].shape.paths=this.shapes[n].localShapeCollection,this._mdf&&(this.shapes[n].pathsData.length=0);else if(r===1&&t===0||r===0&&t===1){if(this._mdf)for(n=0;n<p;n+=1)this.shapes[n].pathsData.length=0,this.shapes[n].shape._mdf=!0}else{var v=[],d,S;for(n=0;n<p;n+=1)if(d=this.shapes[n],!d.shape._mdf&&!this._mdf&&!e&&this.m!==2)d.shape.paths=d.localShapeCollection;else{if(a=d.shape.paths,m=a._length,u=0,!d.shape._mdf&&d.pathsData.length)u=d.totalShapeLength;else{for(g=this.releasePathsData(d.pathsData),c=0;c<m;c+=1)x=bez.getSegmentsLength(a.shapes[c]),g.push(x),u+=x.totalLength;d.totalShapeLength=u,d.pathsData=g}P+=u,d.shape._mdf=!0}var o=t,f=r,l=0,h;for(n=p-1;n>=0;n-=1)if(d=this.shapes[n],d.shape._mdf){for(S=d.localShapeCollection,S.releaseShapes(),this.m===2&&p>1?(h=this.calculateShapeEdges(t,r,d.totalShapeLength,l,P),l+=d.totalShapeLength):h=[[o,f]],m=h.length,c=0;c<m;c+=1){o=h[c][0],f=h[c][1],v.length=0,f<=1?v.push({s:d.totalShapeLength*o,e:d.totalShapeLength*f}):o>=1?v.push({s:d.totalShapeLength*(o-1),e:d.totalShapeLength*(f-1)}):(v.push({s:d.totalShapeLength*o,e:d.totalShapeLength}),v.push({s:0,e:d.totalShapeLength*(f-1)}));var y=this.addShapes(d,v[0]);if(v[0].s!==v[0].e){if(v.length>1){var b=d.shape.paths.shapes[d.shape.paths._length-1];if(b.c){var _=y.pop();this.addPaths(y,S),y=this.addShapes(d,v[1],_)}else this.addPaths(y,S),y=this.addShapes(d,v[1])}this.addPaths(y,S)}}d.shape.paths=S}}},TrimModifier.prototype.addPaths=function(e,t){var r,i=e.length;for(r=0;r<i;r+=1)t.addShape(e[r])},TrimModifier.prototype.addSegment=function(e,t,r,i,s,a,n){s.setXYAt(t[0],t[1],"o",a),s.setXYAt(r[0],r[1],"i",a+1),n&&s.setXYAt(e[0],e[1],"v",a),s.setXYAt(i[0],i[1],"v",a+1)},TrimModifier.prototype.addSegmentFromArray=function(e,t,r,i){t.setXYAt(e[1],e[5],"o",r),t.setXYAt(e[2],e[6],"i",r+1),i&&t.setXYAt(e[0],e[4],"v",r),t.setXYAt(e[3],e[7],"v",r+1)},TrimModifier.prototype.addShapes=function(e,t,r){var i=e.pathsData,s=e.shape.paths.shapes,a,n=e.shape.paths._length,p,c,m=0,g,x,u,P,v=[],d,S=!0;for(r?(x=r._length,d=r._length):(r=shapePool.newElement(),x=0,d=0),v.push(r),a=0;a<n;a+=1){for(u=i[a].lengths,r.c=s[a].c,c=s[a].c?u.length:u.length+1,p=1;p<c;p+=1)if(g=u[p-1],m+g.addedLength<t.s)m+=g.addedLength,r.c=!1;else if(m>t.e){r.c=!1;break}else t.s<=m&&t.e>=m+g.addedLength?(this.addSegment(s[a].v[p-1],s[a].o[p-1],s[a].i[p],s[a].v[p],r,x,S),S=!1):(P=bez.getNewSegment(s[a].v[p-1],s[a].v[p],s[a].o[p-1],s[a].i[p],(t.s-m)/g.addedLength,(t.e-m)/g.addedLength,u[p-1]),this.addSegmentFromArray(P,r,x,S),S=!1,r.c=!1),m+=g.addedLength,x+=1;if(s[a].c&&u.length){if(g=u[p-1],m<=t.e){var o=u[p-1].addedLength;t.s<=m&&t.e>=m+o?(this.addSegment(s[a].v[p-1],s[a].o[p-1],s[a].i[0],s[a].v[0],r,x,S),S=!1):(P=bez.getNewSegment(s[a].v[p-1],s[a].v[0],s[a].o[p-1],s[a].i[0],(t.s-m)/o,(t.e-m)/o,u[p-1]),this.addSegmentFromArray(P,r,x,S),S=!1,r.c=!1)}else r.c=!1;m+=g.addedLength,x+=1}if(r._length&&(r.setXYAt(r.v[d][0],r.v[d][1],"i",d),r.setXYAt(r.v[r._length-1][0],r.v[r._length-1][1],"o",r._length-1)),m>t.e)break;a<n-1&&(r=shapePool.newElement(),S=!0,v.push(r),x=0)}return v};function PuckerAndBloatModifier(){}extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(e,t){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(e,t.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(e,t){var r=t/100,i=[0,0],s=e._length,a=0;for(a=0;a<s;a+=1)i[0]+=e.v[a][0],i[1]+=e.v[a][1];i[0]/=s,i[1]/=s;var n=shapePool.newElement();n.c=e.c;var p,c,m,g,x,u;for(a=0;a<s;a+=1)p=e.v[a][0]+(i[0]-e.v[a][0])*r,c=e.v[a][1]+(i[1]-e.v[a][1])*r,m=e.o[a][0]+(i[0]-e.o[a][0])*-r,g=e.o[a][1]+(i[1]-e.o[a][1])*-r,x=e.i[a][0]+(i[0]-e.i[a][0])*-r,u=e.i[a][1]+(i[1]-e.i[a][1])*-r,n.setTripleAt(p,c,m,g,x,u,a);return n},PuckerAndBloatModifier.prototype.processShapes=function(e){var t,r,i=this.shapes.length,s,a,n=this.amount.v;if(n!==0){var p,c;for(r=0;r<i;r+=1){if(p=this.shapes[r],c=p.localShapeCollection,!(!p.shape._mdf&&!this._mdf&&!e))for(c.releaseShapes(),p.shape._mdf=!0,t=p.shape.paths.shapes,a=p.shape.paths._length,s=0;s<a;s+=1)c.addShape(this.processPath(t[s],n));p.shape.paths=p.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var e=[0,0];function t(c){var m=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||m,this.a&&c.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&c.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&c.skewFromAxis(-this.sk.v,this.sa.v),this.r?c.rotate(-this.r.v):c.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?c.translate(this.px.v,this.py.v,-this.pz.v):c.translate(this.px.v,this.py.v,0):c.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}function r(c){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||c){var m;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var g,x;if(m=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(g=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/m,0),x=this.p.getValueAtTime(this.p.keyframes[0].t/m,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(g=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/m,0),x=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/m,0)):(g=this.p.pv,x=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/m,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){g=[],x=[];var u=this.px,P=this.py;u._caching.lastFrame+u.offsetTime<=u.keyframes[0].t?(g[0]=u.getValueAtTime((u.keyframes[0].t+.01)/m,0),g[1]=P.getValueAtTime((P.keyframes[0].t+.01)/m,0),x[0]=u.getValueAtTime(u.keyframes[0].t/m,0),x[1]=P.getValueAtTime(P.keyframes[0].t/m,0)):u._caching.lastFrame+u.offsetTime>=u.keyframes[u.keyframes.length-1].t?(g[0]=u.getValueAtTime(u.keyframes[u.keyframes.length-1].t/m,0),g[1]=P.getValueAtTime(P.keyframes[P.keyframes.length-1].t/m,0),x[0]=u.getValueAtTime((u.keyframes[u.keyframes.length-1].t-.01)/m,0),x[1]=P.getValueAtTime((P.keyframes[P.keyframes.length-1].t-.01)/m,0)):(g=[u.pv,P.pv],x[0]=u.getValueAtTime((u._caching.lastFrame+u.offsetTime-.01)/m,u.offsetTime),x[1]=P.getValueAtTime((P._caching.lastFrame+P.offsetTime-.01)/m,P.offsetTime))}else x=e,g=x;this.v.rotate(-Math.atan2(g[1]-x[1],g[0]-x[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}}function i(){if(!this.a.k)this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1;else return;if(!this.s.effectsSequence.length)this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2;else return;if(this.sk)if(!this.sk.effectsSequence.length&&!this.sa.effectsSequence.length)this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3;else return;this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):!this.rz.effectsSequence.length&&!this.ry.effectsSequence.length&&!this.rx.effectsSequence.length&&!this.or.effectsSequence.length&&(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}function s(){}function a(c){this._addDynamicProperty(c),this.elem.addDynamicProperty(c),this._isDirty=!0}function n(c,m,g){if(this.elem=c,this.frameId=-1,this.propType="transform",this.data=m,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(g||c),m.p&&m.p.s?(this.px=PropertyFactory.getProp(c,m.p.x,0,0,this),this.py=PropertyFactory.getProp(c,m.p.y,0,0,this),m.p.z&&(this.pz=PropertyFactory.getProp(c,m.p.z,0,0,this))):this.p=PropertyFactory.getProp(c,m.p||{k:[0,0,0]},1,0,this),m.rx){if(this.rx=PropertyFactory.getProp(c,m.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(c,m.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(c,m.rz,0,degToRads,this),m.or.k[0].ti){var x,u=m.or.k.length;for(x=0;x<u;x+=1)m.or.k[x].to=null,m.or.k[x].ti=null}this.or=PropertyFactory.getProp(c,m.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(c,m.r||{k:0},0,degToRads,this);m.sk&&(this.sk=PropertyFactory.getProp(c,m.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(c,m.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(c,m.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(c,m.s||{k:[100,100,100]},1,.01,this),m.o?this.o=PropertyFactory.getProp(c,m.o,0,.01,c):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}n.prototype={applyToMatrix:t,getValue:r,precalculateMatrix:i,autoOrient:s},extendPrototype([DynamicPropertyContainer],n),n.prototype.addDynamicProperty=a,n.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty;function p(c,m,g){return new n(c,m,g)}return{getTransformProperty:p}}();function RepeaterModifier(){}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(e,t){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(e,t.c,0,null,this),this.o=PropertyFactory.getProp(e,t.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(e,t.tr,this),this.so=PropertyFactory.getProp(e,t.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(e,t.tr.eo,0,.01,this),this.data=t,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(e,t,r,i,s,a){var n=a?-1:1,p=i.s.v[0]+(1-i.s.v[0])*(1-s),c=i.s.v[1]+(1-i.s.v[1])*(1-s);e.translate(i.p.v[0]*n*s,i.p.v[1]*n*s,i.p.v[2]),t.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),t.rotate(-i.r.v*n*s),t.translate(i.a.v[0],i.a.v[1],i.a.v[2]),r.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),r.scale(a?1/p:p,a?1/c:c),r.translate(i.a.v[0],i.a.v[1],i.a.v[2])},RepeaterModifier.prototype.init=function(e,t,r,i){for(this.elem=e,this.arr=t,this.pos=r,this.elemsData=i,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(e),this.initModifierProperties(e,t[r]);r>0;)r-=1,this._elements.unshift(t[r]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(e){var t,r=e.length;for(t=0;t<r;t+=1)e[t]._processed=!1,e[t].ty==="gr"&&this.resetElements(e[t].it)},RepeaterModifier.prototype.cloneElements=function(e){var t=JSON.parse(JSON.stringify(e));return this.resetElements(t),t},RepeaterModifier.prototype.changeGroupRender=function(e,t){var r,i=e.length;for(r=0;r<i;r+=1)e[r]._render=t,e[r].ty==="gr"&&this.changeGroupRender(e[r].it,t)},RepeaterModifier.prototype.processShapes=function(e){var t,r,i,s,a,n=!1;if(this._mdf||e){var p=Math.ceil(this.c.v);if(this._groups.length<p){for(;this._groups.length<p;){var c={it:this.cloneElements(this._elements),ty:"gr"};c.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,c),this._groups.splice(0,0,c),this._currentCopies+=1}this.elem.reloadShapes(),n=!0}a=0;var m;for(i=0;i<=this._groups.length-1;i+=1){if(m=a<p,this._groups[i]._render=m,this.changeGroupRender(this._groups[i].it,m),!m){var g=this.elemsData[i].it,x=g[g.length-1];x.transform.op.v!==0?(x.transform.op._mdf=!0,x.transform.op.v=0):x.transform.op._mdf=!1}a+=1}this._currentCopies=p;var u=this.o.v,P=u%1,v=u>0?Math.floor(u):Math.ceil(u),d=this.pMatrix.props,S=this.rMatrix.props,o=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var f=0;if(u>0){for(;f<v;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),f+=1;P&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,P,!1),f+=P)}else if(u<0){for(;f>v;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),f-=1;P&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-P,!0),f-=P)}i=this.data.m===1?0:this._currentCopies-1,s=this.data.m===1?1:-1,a=this._currentCopies;for(var l,h;a;){if(t=this.elemsData[i].it,r=t[t.length-1].transform.mProps.v.props,h=r.length,t[t.length-1].transform.mProps._mdf=!0,t[t.length-1].transform.op._mdf=!0,t[t.length-1].transform.op.v=this._currentCopies===1?this.so.v:this.so.v+(this.eo.v-this.so.v)*(i/(this._currentCopies-1)),f!==0){for((i!==0&&s===1||i!==this._currentCopies-1&&s===-1)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(S[0],S[1],S[2],S[3],S[4],S[5],S[6],S[7],S[8],S[9],S[10],S[11],S[12],S[13],S[14],S[15]),this.matrix.transform(o[0],o[1],o[2],o[3],o[4],o[5],o[6],o[7],o[8],o[9],o[10],o[11],o[12],o[13],o[14],o[15]),this.matrix.transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6],d[7],d[8],d[9],d[10],d[11],d[12],d[13],d[14],d[15]),l=0;l<h;l+=1)r[l]=this.matrix.props[l];this.matrix.reset()}else for(this.matrix.reset(),l=0;l<h;l+=1)r[l]=this.matrix.props[l];f+=1,a-=1,i+=s}}else for(a=this._currentCopies,i=0,s=1;a;)t=this.elemsData[i].it,r=t[t.length-1].transform.mProps.v.props,t[t.length-1].transform.mProps._mdf=!1,t[t.length-1].transform.op._mdf=!1,a-=1,i+=s;return n},RepeaterModifier.prototype.addShape=function(){};function RoundCornersModifier(){}extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(e,t){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(e,t.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(e,t){var r=shapePool.newElement();r.c=e.c;var i,s=e._length,a,n,p,c,m,g,x=0,u,P,v,d,S,o;for(i=0;i<s;i+=1)a=e.v[i],p=e.o[i],n=e.i[i],a[0]===p[0]&&a[1]===p[1]&&a[0]===n[0]&&a[1]===n[1]?(i===0||i===s-1)&&!e.c?(r.setTripleAt(a[0],a[1],p[0],p[1],n[0],n[1],x),x+=1):(i===0?c=e.v[s-1]:c=e.v[i-1],m=Math.sqrt(Math.pow(a[0]-c[0],2)+Math.pow(a[1]-c[1],2)),g=m?Math.min(m/2,t)/m:0,S=a[0]+(c[0]-a[0])*g,u=S,o=a[1]-(a[1]-c[1])*g,P=o,v=u-(u-a[0])*roundCorner,d=P-(P-a[1])*roundCorner,r.setTripleAt(u,P,v,d,S,o,x),x+=1,i===s-1?c=e.v[0]:c=e.v[i+1],m=Math.sqrt(Math.pow(a[0]-c[0],2)+Math.pow(a[1]-c[1],2)),g=m?Math.min(m/2,t)/m:0,v=a[0]+(c[0]-a[0])*g,u=v,d=a[1]+(c[1]-a[1])*g,P=d,S=u-(u-a[0])*roundCorner,o=P-(P-a[1])*roundCorner,r.setTripleAt(u,P,v,d,S,o,x),x+=1):(r.setTripleAt(e.v[i][0],e.v[i][1],e.o[i][0],e.o[i][1],e.i[i][0],e.i[i][1],x),x+=1);return r},RoundCornersModifier.prototype.processShapes=function(e){var t,r,i=this.shapes.length,s,a,n=this.rd.v;if(n!==0){var p,c;for(r=0;r<i;r+=1){if(p=this.shapes[r],c=p.localShapeCollection,!(!p.shape._mdf&&!this._mdf&&!e))for(c.releaseShapes(),p.shape._mdf=!0,t=p.shape.paths.shapes,a=p.shape.paths._length,s=0;s<a;s+=1)c.addShape(this.processPath(t[s],n));p.shape.paths=p.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};function getFontProperties(e){for(var t=e.fStyle?e.fStyle.split(" "):[],r="normal",i="normal",s=t.length,a,n=0;n<s;n+=1)switch(a=t[n].toLowerCase(),a){case"italic":i="italic";break;case"bold":r="700";break;case"black":r="900";break;case"medium":r="500";break;case"regular":case"normal":r="400";break;case"light":case"thin":r="200";break;default:break}return{style:i,weight:e.fWeight||r}}var FontManager=function(){var e=5e3,t={w:0,size:0,shapes:[],data:{shapes:[]}},r=[];r=r.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"],s=[65039,8205];function a(h){var y=h.split(","),b,_=y.length,C=[];for(b=0;b<_;b+=1)y[b]!=="sans-serif"&&y[b]!=="monospace"&&C.push(y[b]);return C.join(",")}function n(h,y){var b=createTag("span");b.setAttribute("aria-hidden",!0),b.style.fontFamily=y;var _=createTag("span");_.innerText="giItT1WQy@!-/#",b.style.position="absolute",b.style.left="-10000px",b.style.top="-10000px",b.style.fontSize="300px",b.style.fontVariant="normal",b.style.fontStyle="normal",b.style.fontWeight="normal",b.style.letterSpacing="0",b.appendChild(_),document.body.appendChild(b);var C=_.offsetWidth;return _.style.fontFamily=a(h)+", "+y,{node:_,w:C,parent:b}}function p(){var h,y=this.fonts.length,b,_,C=y;for(h=0;h<y;h+=1)this.fonts[h].loaded?C-=1:this.fonts[h].fOrigin==="n"||this.fonts[h].origin===0?this.fonts[h].loaded=!0:(b=this.fonts[h].monoCase.node,_=this.fonts[h].monoCase.w,b.offsetWidth!==_?(C-=1,this.fonts[h].loaded=!0):(b=this.fonts[h].sansCase.node,_=this.fonts[h].sansCase.w,b.offsetWidth!==_&&(C-=1,this.fonts[h].loaded=!0)),this.fonts[h].loaded&&(this.fonts[h].sansCase.parent.parentNode.removeChild(this.fonts[h].sansCase.parent),this.fonts[h].monoCase.parent.parentNode.removeChild(this.fonts[h].monoCase.parent)));C!==0&&Date.now()-this.initTime<e?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)}function c(h,y){var b=document.body&&y?"svg":"canvas",_,C=getFontProperties(h);if(b==="svg"){var F=createNS("text");F.style.fontSize="100px",F.setAttribute("font-family",h.fFamily),F.setAttribute("font-style",C.style),F.setAttribute("font-weight",C.weight),F.textContent="1",h.fClass?(F.style.fontFamily="inherit",F.setAttribute("class",h.fClass)):F.style.fontFamily=h.fFamily,y.appendChild(F),_=F}else{var L=new OffscreenCanvas(500,500).getContext("2d");L.font=C.style+" "+C.weight+" 100px "+h.fFamily,_=L}function R(I){return b==="svg"?(_.textContent=I,_.getComputedTextLength()):_.measureText(I).width}return{measureText:R}}function m(h,y){if(!h){this.isLoaded=!0;return}if(this.chars){this.isLoaded=!0,this.fonts=h.list;return}if(!document.body){this.isLoaded=!0,h.list.forEach(function(k){k.helper=c(k),k.cache={}}),this.fonts=h.list;return}var b=h.list,_,C=b.length,F=C;for(_=0;_<C;_+=1){var L=!0,R,I;if(b[_].loaded=!1,b[_].monoCase=n(b[_].fFamily,"monospace"),b[_].sansCase=n(b[_].fFamily,"sans-serif"),!b[_].fPath)b[_].loaded=!0,F-=1;else if(b[_].fOrigin==="p"||b[_].origin===3){if(R=document.querySelectorAll('style[f-forigin="p"][f-family="'+b[_].fFamily+'"], style[f-origin="3"][f-family="'+b[_].fFamily+'"]'),R.length>0&&(L=!1),L){var D=createTag("style");D.setAttribute("f-forigin",b[_].fOrigin),D.setAttribute("f-origin",b[_].origin),D.setAttribute("f-family",b[_].fFamily),D.type="text/css",D.innerText="@font-face {font-family: "+b[_].fFamily+"; font-style: normal; src: url('"+b[_].fPath+"');}",y.appendChild(D)}}else if(b[_].fOrigin==="g"||b[_].origin===1){for(R=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),I=0;I<R.length;I+=1)R[I].href.indexOf(b[_].fPath)!==-1&&(L=!1);if(L){var B=createTag("link");B.setAttribute("f-forigin",b[_].fOrigin),B.setAttribute("f-origin",b[_].origin),B.type="text/css",B.rel="stylesheet",B.href=b[_].fPath,document.body.appendChild(B)}}else if(b[_].fOrigin==="t"||b[_].origin===2){for(R=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),I=0;I<R.length;I+=1)b[_].fPath===R[I].src&&(L=!1);if(L){var w=createTag("link");w.setAttribute("f-forigin",b[_].fOrigin),w.setAttribute("f-origin",b[_].origin),w.setAttribute("rel","stylesheet"),w.setAttribute("href",b[_].fPath),y.appendChild(w)}}b[_].helper=c(b[_],y),b[_].cache={},this.fonts.push(b[_])}F===0?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}function g(h){if(!!h){this.chars||(this.chars=[]);var y,b=h.length,_,C=this.chars.length,F;for(y=0;y<b;y+=1){for(_=0,F=!1;_<C;)this.chars[_].style===h[y].style&&this.chars[_].fFamily===h[y].fFamily&&this.chars[_].ch===h[y].ch&&(F=!0),_+=1;F||(this.chars.push(h[y]),C+=1)}}}function x(h,y,b){for(var _=0,C=this.chars.length;_<C;){if(this.chars[_].ch===h&&this.chars[_].style===y&&this.chars[_].fFamily===b)return this.chars[_];_+=1}return(typeof h=="string"&&h.charCodeAt(0)!==13||!h)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",h,y,b)),t}function u(h,y,b){var _=this.getFontByName(y),C=h.charCodeAt(0);if(!_.cache[C+1]){var F=_.helper;if(h===" "){var L=F.measureText("|"+h+"|"),R=F.measureText("||");_.cache[C+1]=(L-R)/100}else _.cache[C+1]=F.measureText(h)/100}return _.cache[C+1]*b}function P(h){for(var y=0,b=this.fonts.length;y<b;){if(this.fonts[y].fName===h)return this.fonts[y];y+=1}return this.fonts[0]}function v(h,y){var b=h.toString(16)+y.toString(16);return i.indexOf(b)!==-1}function d(h,y){return y?h===s[0]&&y===s[1]:h===s[1]}function S(h){return r.indexOf(h)!==-1}function o(){this.isLoaded=!0}var f=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};f.isModifier=v,f.isZeroWidthJoiner=d,f.isCombinedCharacter=S;var l={addChars:g,addFonts:m,getCharData:x,getFontByName:P,measureText:u,checkLoadedFonts:p,setIsLoaded:o};return f.prototype=l,f}();function RenderableElement(){}RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){this.renderableComponents.indexOf(t)===-1&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){this.renderableComponents.indexOf(t)!==-1&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?this.isInRange!==!0&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):this.isInRange!==!1&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,r=this.renderableComponents.length;for(t=0;t<r;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return this.data.ty===5?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var MaskManagerInterface=function(){function e(r,i){this._mask=r,this._data=i}Object.defineProperty(e.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(e.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),this._mask.op.v*100}});var t=function(i){var s=createSizedArray(i.viewData.length),a,n=i.viewData.length;for(a=0;a<n;a+=1)s[a]=new e(i.viewData[a],i.masksProperties[a]);var p=function(m){for(a=0;a<n;){if(i.masksProperties[a].nm===m)return s[a];a+=1}return null};return p};return t}(),ExpressionPropertyInterface=function(){var e={pv:0,v:0,mult:1},t={pv:[0,0,0],v:[0,0,0],mult:1};function r(n,p,c){Object.defineProperty(n,"velocity",{get:function(){return p.getVelocityAtTime(p.comp.currentFrame)}}),n.numKeys=p.keyframes?p.keyframes.length:0,n.key=function(m){if(!n.numKeys)return 0;var g="";"s"in p.keyframes[m-1]?g=p.keyframes[m-1].s:"e"in p.keyframes[m-2]?g=p.keyframes[m-2].e:g=p.keyframes[m-2].s;var x=c==="unidimensional"?new Number(g):Object.assign({},g);return x.time=p.keyframes[m-1].t/p.elem.comp.globalData.frameRate,x.value=c==="unidimensional"?g[0]:g,x},n.valueAtTime=p.getValueAtTime,n.speedAtTime=p.getSpeedAtTime,n.velocityAtTime=p.getVelocityAtTime,n.propertyGroup=p.propertyGroup}function i(n){(!n||!("pv"in n))&&(n=e);var p=1/n.mult,c=n.pv*p,m=new Number(c);return m.value=c,r(m,n,"unidimensional"),function(){return n.k&&n.getValue(),c=n.v*p,m.value!==c&&(m=new Number(c),m.value=c,r(m,n,"unidimensional")),m}}function s(n){(!n||!("pv"in n))&&(n=t);var p=1/n.mult,c=n.data&&n.data.l||n.pv.length,m=createTypedArray("float32",c),g=createTypedArray("float32",c);return m.value=g,r(m,n,"multidimensional"),function(){n.k&&n.getValue();for(var x=0;x<c;x+=1)g[x]=n.v[x]*p,m[x]=g[x];return m}}function a(){return e}return function(n){return n?n.propType==="unidimensional"?i(n):s(n):a}}(),TransformExpressionInterface=function(){return function(e){function t(n){switch(n){case"scale":case"Scale":case"ADBE Scale":case 6:return t.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return t.rotation;case"ADBE Rotate X":return t.xRotation;case"ADBE Rotate Y":return t.yRotation;case"position":case"Position":case"ADBE Position":case 2:return t.position;case"ADBE Position_0":return t.xPosition;case"ADBE Position_1":return t.yPosition;case"ADBE Position_2":return t.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return t.anchorPoint;case"opacity":case"Opacity":case 11:return t.opacity;default:return null}}Object.defineProperty(t,"rotation",{get:ExpressionPropertyInterface(e.r||e.rz)}),Object.defineProperty(t,"zRotation",{get:ExpressionPropertyInterface(e.rz||e.r)}),Object.defineProperty(t,"xRotation",{get:ExpressionPropertyInterface(e.rx)}),Object.defineProperty(t,"yRotation",{get:ExpressionPropertyInterface(e.ry)}),Object.defineProperty(t,"scale",{get:ExpressionPropertyInterface(e.s)});var r,i,s,a;return e.p?a=ExpressionPropertyInterface(e.p):(r=ExpressionPropertyInterface(e.px),i=ExpressionPropertyInterface(e.py),e.pz&&(s=ExpressionPropertyInterface(e.pz))),Object.defineProperty(t,"position",{get:function(){return e.p?a():[r(),i(),s?s():0]}}),Object.defineProperty(t,"xPosition",{get:ExpressionPropertyInterface(e.px)}),Object.defineProperty(t,"yPosition",{get:ExpressionPropertyInterface(e.py)}),Object.defineProperty(t,"zPosition",{get:ExpressionPropertyInterface(e.pz)}),Object.defineProperty(t,"anchorPoint",{get:ExpressionPropertyInterface(e.a)}),Object.defineProperty(t,"opacity",{get:ExpressionPropertyInterface(e.o)}),Object.defineProperty(t,"skew",{get:ExpressionPropertyInterface(e.sk)}),Object.defineProperty(t,"skewAxis",{get:ExpressionPropertyInterface(e.sa)}),Object.defineProperty(t,"orientation",{get:ExpressionPropertyInterface(e.or)}),t}}(),LayerExpressionInterface=function(){function e(m){var g=new Matrix;if(m!==void 0){var x=this._elem.finalTransform.mProp.getValueAtTime(m);x.clone(g)}else{var u=this._elem.finalTransform.mProp;u.applyToMatrix(g)}return g}function t(m,g){var x=this.getMatrix(g);return x.props[12]=0,x.props[13]=0,x.props[14]=0,this.applyPoint(x,m)}function r(m,g){var x=this.getMatrix(g);return this.applyPoint(x,m)}function i(m,g){var x=this.getMatrix(g);return x.props[12]=0,x.props[13]=0,x.props[14]=0,this.invertPoint(x,m)}function s(m,g){var x=this.getMatrix(g);return this.invertPoint(x,m)}function a(m,g){if(this._elem.hierarchy&&this._elem.hierarchy.length){var x,u=this._elem.hierarchy.length;for(x=0;x<u;x+=1)this._elem.hierarchy[x].finalTransform.mProp.applyToMatrix(m)}return m.applyToPointArray(g[0],g[1],g[2]||0)}function n(m,g){if(this._elem.hierarchy&&this._elem.hierarchy.length){var x,u=this._elem.hierarchy.length;for(x=0;x<u;x+=1)this._elem.hierarchy[x].finalTransform.mProp.applyToMatrix(m)}return m.inversePoint(g)}function p(m){var g=new Matrix;if(g.reset(),this._elem.finalTransform.mProp.applyToMatrix(g),this._elem.hierarchy&&this._elem.hierarchy.length){var x,u=this._elem.hierarchy.length;for(x=0;x<u;x+=1)this._elem.hierarchy[x].finalTransform.mProp.applyToMatrix(g);return g.inversePoint(m)}return g.inversePoint(m)}function c(){return[1,1,1,1]}return function(m){var g;function x(d){P.mask=new MaskManagerInterface(d,m)}function u(d){P.effect=d}function P(d){switch(d){case"ADBE Root Vectors Group":case"Contents":case 2:return P.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return g;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return P.effect;case"ADBE Text Properties":return P.textInterface;default:return null}}P.getMatrix=e,P.invertPoint=n,P.applyPoint=a,P.toWorld=r,P.toWorldVec=t,P.fromWorld=s,P.fromWorldVec=i,P.toComp=r,P.fromComp=p,P.sampleImage=c,P.sourceRectAtTime=m.sourceRectAtTime.bind(m),P._elem=m,g=TransformExpressionInterface(m.finalTransform.mProp);var v=getDescriptor(g,"anchorPoint");return Object.defineProperties(P,{hasParent:{get:function(){return m.hierarchy.length}},parent:{get:function(){return m.hierarchy[0].layerInterface}},rotation:getDescriptor(g,"rotation"),scale:getDescriptor(g,"scale"),position:getDescriptor(g,"position"),opacity:getDescriptor(g,"opacity"),anchorPoint:v,anchor_point:v,transform:{get:function(){return g}},active:{get:function(){return m.isInRange}}}),P.startTime=m.data.st,P.index=m.data.ind,P.source=m.data.refId,P.height=m.data.ty===0?m.data.h:100,P.width=m.data.ty===0?m.data.w:100,P.inPoint=m.data.ip/m.comp.globalData.frameRate,P.outPoint=m.data.op/m.comp.globalData.frameRate,P._name=m.data.nm,P.registerMaskInterface=x,P.registerEffectsInterface=u,P}}(),propertyGroupFactory=function(){return function(e,t){return function(r){return r=r===void 0?1:r,r<=0?e:t(r-1)}}}(),PropertyInterface=function(){return function(e,t){var r={_name:e};function i(s){return s=s===void 0?1:s,s<=0?r:t(s-1)}return i}}(),EffectsExpressionInterface=function(){var e={createEffectsInterface:t};function t(s,a){if(s.effectsManager){var n=[],p=s.data.ef,c,m=s.effectsManager.effectElements.length;for(c=0;c<m;c+=1)n.push(r(p[c],s.effectsManager.effectElements[c],a,s));var g=s.data.ef||[],x=function(P){for(c=0,m=g.length;c<m;){if(P===g[c].nm||P===g[c].mn||P===g[c].ix)return n[c];c+=1}return null};return Object.defineProperty(x,"numProperties",{get:function(){return g.length}}),x}return null}function r(s,a,n,p){function c(P){for(var v=s.ef,d=0,S=v.length;d<S;){if(P===v[d].nm||P===v[d].mn||P===v[d].ix)return v[d].ty===5?g[d]:g[d]();d+=1}throw new Error}var m=propertyGroupFactory(c,n),g=[],x,u=s.ef.length;for(x=0;x<u;x+=1)s.ef[x].ty===5?g.push(r(s.ef[x],a.effectElements[x],a.effectElements[x].propertyGroup,p)):g.push(i(a.effectElements[x],s.ef[x].ty,p,m));return s.mn==="ADBE Color Control"&&Object.defineProperty(c,"color",{get:function(){return g[0]()}}),Object.defineProperties(c,{numProperties:{get:function(){return s.np}},_name:{value:s.nm},propertyGroup:{value:m}}),c.enabled=s.en!==0,c.active=c.enabled,c}function i(s,a,n,p){var c=ExpressionPropertyInterface(s.p);function m(){return a===10?n.comp.compInterface(s.p.v):c()}return s.p.setGroupProperty&&s.p.setGroupProperty(PropertyInterface("",p)),m}return e}(),CompExpressionInterface=function(){return function(e){function t(r){for(var i=0,s=e.layers.length;i<s;){if(e.layers[i].nm===r||e.layers[i].ind===r)return e.elements[i].layerInterface;i+=1}return null}return Object.defineProperty(t,"_name",{value:e.data.nm}),t.layer=t,t.pixelAspect=1,t.height=e.data.h||e.globalData.compSize.h,t.width=e.data.w||e.globalData.compSize.w,t.pixelAspect=1,t.frameDuration=1/e.globalData.frameRate,t.displayStartTime=0,t.numLayers=e.layers.length,t}}(),ShapePathInterface=function(){return function(t,r,i){var s=r.sh;function a(p){return p==="Shape"||p==="shape"||p==="Path"||p==="path"||p==="ADBE Vector Shape"||p===2?a.path:null}var n=propertyGroupFactory(a,i);return s.setGroupProperty(PropertyInterface("Path",n)),Object.defineProperties(a,{path:{get:function(){return s.k&&s.getValue(),s}},shape:{get:function(){return s.k&&s.getValue(),s}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:i}}),a}}(),ShapeExpressionInterface=function(){function e(v,d,S){var o=[],f,l=v?v.length:0;for(f=0;f<l;f+=1)v[f].ty==="gr"?o.push(r(v[f],d[f],S)):v[f].ty==="fl"?o.push(i(v[f],d[f],S)):v[f].ty==="st"?o.push(n(v[f],d[f],S)):v[f].ty==="tm"?o.push(p(v[f],d[f],S)):v[f].ty==="tr"||(v[f].ty==="el"?o.push(m(v[f],d[f],S)):v[f].ty==="sr"?o.push(g(v[f],d[f],S)):v[f].ty==="sh"?o.push(ShapePathInterface(v[f],d[f],S)):v[f].ty==="rc"?o.push(x(v[f],d[f],S)):v[f].ty==="rd"?o.push(u(v[f],d[f],S)):v[f].ty==="rp"?o.push(P(v[f],d[f],S)):v[f].ty==="gf"?o.push(s(v[f],d[f],S)):o.push(a(v[f],d[f],S)));return o}function t(v,d,S){var o,f=function(y){for(var b=0,_=o.length;b<_;){if(o[b]._name===y||o[b].mn===y||o[b].propertyIndex===y||o[b].ix===y||o[b].ind===y)return o[b];b+=1}return typeof y=="number"?o[y-1]:null};f.propertyGroup=propertyGroupFactory(f,S),o=e(v.it,d.it,f.propertyGroup),f.numProperties=o.length;var l=c(v.it[v.it.length-1],d.it[d.it.length-1],f.propertyGroup);return f.transform=l,f.propertyIndex=v.cix,f._name=v.nm,f}function r(v,d,S){var o=function(y){switch(y){case"ADBE Vectors Group":case"Contents":case 2:return o.content;default:return o.transform}};o.propertyGroup=propertyGroupFactory(o,S);var f=t(v,d,o.propertyGroup),l=c(v.it[v.it.length-1],d.it[d.it.length-1],o.propertyGroup);return o.content=f,o.transform=l,Object.defineProperty(o,"_name",{get:function(){return v.nm}}),o.numProperties=v.np,o.propertyIndex=v.ix,o.nm=v.nm,o.mn=v.mn,o}function i(v,d,S){function o(f){return f==="Color"||f==="color"?o.color:f==="Opacity"||f==="opacity"?o.opacity:null}return Object.defineProperties(o,{color:{get:ExpressionPropertyInterface(d.c)},opacity:{get:ExpressionPropertyInterface(d.o)},_name:{value:v.nm},mn:{value:v.mn}}),d.c.setGroupProperty(PropertyInterface("Color",S)),d.o.setGroupProperty(PropertyInterface("Opacity",S)),o}function s(v,d,S){function o(f){return f==="Start Point"||f==="start point"?o.startPoint:f==="End Point"||f==="end point"?o.endPoint:f==="Opacity"||f==="opacity"?o.opacity:null}return Object.defineProperties(o,{startPoint:{get:ExpressionPropertyInterface(d.s)},endPoint:{get:ExpressionPropertyInterface(d.e)},opacity:{get:ExpressionPropertyInterface(d.o)},type:{get:function(){return"a"}},_name:{value:v.nm},mn:{value:v.mn}}),d.s.setGroupProperty(PropertyInterface("Start Point",S)),d.e.setGroupProperty(PropertyInterface("End Point",S)),d.o.setGroupProperty(PropertyInterface("Opacity",S)),o}function a(){function v(){return null}return v}function n(v,d,S){var o=propertyGroupFactory(_,S),f=propertyGroupFactory(b,o);function l(C){Object.defineProperty(b,v.d[C].nm,{get:ExpressionPropertyInterface(d.d.dataProps[C].p)})}var h,y=v.d?v.d.length:0,b={};for(h=0;h<y;h+=1)l(h),d.d.dataProps[h].p.setGroupProperty(f);function _(C){return C==="Color"||C==="color"?_.color:C==="Opacity"||C==="opacity"?_.opacity:C==="Stroke Width"||C==="stroke width"?_.strokeWidth:null}return Object.defineProperties(_,{color:{get:ExpressionPropertyInterface(d.c)},opacity:{get:ExpressionPropertyInterface(d.o)},strokeWidth:{get:ExpressionPropertyInterface(d.w)},dash:{get:function(){return b}},_name:{value:v.nm},mn:{value:v.mn}}),d.c.setGroupProperty(PropertyInterface("Color",o)),d.o.setGroupProperty(PropertyInterface("Opacity",o)),d.w.setGroupProperty(PropertyInterface("Stroke Width",o)),_}function p(v,d,S){function o(l){return l===v.e.ix||l==="End"||l==="end"?o.end:l===v.s.ix?o.start:l===v.o.ix?o.offset:null}var f=propertyGroupFactory(o,S);return o.propertyIndex=v.ix,d.s.setGroupProperty(PropertyInterface("Start",f)),d.e.setGroupProperty(PropertyInterface("End",f)),d.o.setGroupProperty(PropertyInterface("Offset",f)),o.propertyIndex=v.ix,o.propertyGroup=S,Object.defineProperties(o,{start:{get:ExpressionPropertyInterface(d.s)},end:{get:ExpressionPropertyInterface(d.e)},offset:{get:ExpressionPropertyInterface(d.o)},_name:{value:v.nm}}),o.mn=v.mn,o}function c(v,d,S){function o(l){return v.a.ix===l||l==="Anchor Point"?o.anchorPoint:v.o.ix===l||l==="Opacity"?o.opacity:v.p.ix===l||l==="Position"?o.position:v.r.ix===l||l==="Rotation"||l==="ADBE Vector Rotation"?o.rotation:v.s.ix===l||l==="Scale"?o.scale:v.sk&&v.sk.ix===l||l==="Skew"?o.skew:v.sa&&v.sa.ix===l||l==="Skew Axis"?o.skewAxis:null}var f=propertyGroupFactory(o,S);return d.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",f)),d.transform.mProps.p.setGroupProperty(PropertyInterface("Position",f)),d.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",f)),d.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",f)),d.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",f)),d.transform.mProps.sk&&(d.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",f)),d.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",f))),d.transform.op.setGroupProperty(PropertyInterface("Opacity",f)),Object.defineProperties(o,{opacity:{get:ExpressionPropertyInterface(d.transform.mProps.o)},position:{get:ExpressionPropertyInterface(d.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(d.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(d.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(d.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(d.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(d.transform.mProps.sa)},_name:{value:v.nm}}),o.ty="tr",o.mn=v.mn,o.propertyGroup=S,o}function m(v,d,S){function o(h){return v.p.ix===h?o.position:v.s.ix===h?o.size:null}var f=propertyGroupFactory(o,S);o.propertyIndex=v.ix;var l=d.sh.ty==="tm"?d.sh.prop:d.sh;return l.s.setGroupProperty(PropertyInterface("Size",f)),l.p.setGroupProperty(PropertyInterface("Position",f)),Object.defineProperties(o,{size:{get:ExpressionPropertyInterface(l.s)},position:{get:ExpressionPropertyInterface(l.p)},_name:{value:v.nm}}),o.mn=v.mn,o}function g(v,d,S){function o(h){return v.p.ix===h?o.position:v.r.ix===h?o.rotation:v.pt.ix===h?o.points:v.or.ix===h||h==="ADBE Vector Star Outer Radius"?o.outerRadius:v.os.ix===h?o.outerRoundness:v.ir&&(v.ir.ix===h||h==="ADBE Vector Star Inner Radius")?o.innerRadius:v.is&&v.is.ix===h?o.innerRoundness:null}var f=propertyGroupFactory(o,S),l=d.sh.ty==="tm"?d.sh.prop:d.sh;return o.propertyIndex=v.ix,l.or.setGroupProperty(PropertyInterface("Outer Radius",f)),l.os.setGroupProperty(PropertyInterface("Outer Roundness",f)),l.pt.setGroupProperty(PropertyInterface("Points",f)),l.p.setGroupProperty(PropertyInterface("Position",f)),l.r.setGroupProperty(PropertyInterface("Rotation",f)),v.ir&&(l.ir.setGroupProperty(PropertyInterface("Inner Radius",f)),l.is.setGroupProperty(PropertyInterface("Inner Roundness",f))),Object.defineProperties(o,{position:{get:ExpressionPropertyInterface(l.p)},rotation:{get:ExpressionPropertyInterface(l.r)},points:{get:ExpressionPropertyInterface(l.pt)},outerRadius:{get:ExpressionPropertyInterface(l.or)},outerRoundness:{get:ExpressionPropertyInterface(l.os)},innerRadius:{get:ExpressionPropertyInterface(l.ir)},innerRoundness:{get:ExpressionPropertyInterface(l.is)},_name:{value:v.nm}}),o.mn=v.mn,o}function x(v,d,S){function o(h){return v.p.ix===h?o.position:v.r.ix===h?o.roundness:v.s.ix===h||h==="Size"||h==="ADBE Vector Rect Size"?o.size:null}var f=propertyGroupFactory(o,S),l=d.sh.ty==="tm"?d.sh.prop:d.sh;return o.propertyIndex=v.ix,l.p.setGroupProperty(PropertyInterface("Position",f)),l.s.setGroupProperty(PropertyInterface("Size",f)),l.r.setGroupProperty(PropertyInterface("Rotation",f)),Object.defineProperties(o,{position:{get:ExpressionPropertyInterface(l.p)},roundness:{get:ExpressionPropertyInterface(l.r)},size:{get:ExpressionPropertyInterface(l.s)},_name:{value:v.nm}}),o.mn=v.mn,o}function u(v,d,S){function o(h){return v.r.ix===h||h==="Round Corners 1"?o.radius:null}var f=propertyGroupFactory(o,S),l=d;return o.propertyIndex=v.ix,l.rd.setGroupProperty(PropertyInterface("Radius",f)),Object.defineProperties(o,{radius:{get:ExpressionPropertyInterface(l.rd)},_name:{value:v.nm}}),o.mn=v.mn,o}function P(v,d,S){function o(h){return v.c.ix===h||h==="Copies"?o.copies:v.o.ix===h||h==="Offset"?o.offset:null}var f=propertyGroupFactory(o,S),l=d;return o.propertyIndex=v.ix,l.c.setGroupProperty(PropertyInterface("Copies",f)),l.o.setGroupProperty(PropertyInterface("Offset",f)),Object.defineProperties(o,{copies:{get:ExpressionPropertyInterface(l.c)},offset:{get:ExpressionPropertyInterface(l.o)},_name:{value:v.nm}}),o.mn=v.mn,o}return function(v,d,S){var o;function f(h){if(typeof h=="number")return h=h===void 0?1:h,h===0?S:o[h-1];for(var y=0,b=o.length;y<b;){if(o[y]._name===h)return o[y];y+=1}return null}function l(){return S}return f.propertyGroup=propertyGroupFactory(f,l),o=e(v,d,f.propertyGroup),f.numProperties=o.length,f._name="Contents",f}}(),TextExpressionInterface=function(){return function(e){var t,r;function i(s){switch(s){case"ADBE Text Document":return i.sourceText;default:return null}}return Object.defineProperty(i,"sourceText",{get:function(){e.textProperty.getValue();var a=e.textProperty.currentData.t;return a!==t&&(e.textProperty.currentData.t=t,r=new String(a),r.value=a||new String(a)),r}}),i}}(),getBlendMode=function(){var e={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"};return function(t){return e[t]||""}}();function SliderEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,0,0,r)}function AngleEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,0,0,r)}function ColorEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,1,0,r)}function PointEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,1,0,r)}function LayerIndexEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,0,0,r)}function MaskIndexEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,0,0,r)}function CheckboxEffect(e,t,r){this.p=PropertyFactory.getProp(t,e.v,0,0,r)}function NoValueEffect(){this.p={}}function EffectsManager(e,t){var r=e.ef||[];this.effectElements=[];var i,s=r.length,a;for(i=0;i<s;i+=1)a=new GroupEffect(r[i],t),this.effectElements.push(a)}function GroupEffect(e,t){this.init(e,t)}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(e,t){this.data=e,this.effectElements=[],this.initDynamicPropertyContainer(t);var r,i=this.data.ef.length,s,a=this.data.ef;for(r=0;r<i;r+=1){switch(s=null,a[r].ty){case 0:s=new SliderEffect(a[r],t,this);break;case 1:s=new AngleEffect(a[r],t,this);break;case 2:s=new ColorEffect(a[r],t,this);break;case 3:s=new PointEffect(a[r],t,this);break;case 4:case 7:s=new CheckboxEffect(a[r],t,this);break;case 10:s=new LayerIndexEffect(a[r],t,this);break;case 11:s=new MaskIndexEffect(a[r],t,this);break;case 5:s=new EffectsManager(a[r],t,this);break;default:s=new NoValueEffect(a[r],t,this);break}s&&this.effectElements.push(s)}};function BaseElement(){}BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,r=this.data.masksProperties.length;t<r;){if(this.data.masksProperties[t].mode!=="n"&&this.data.masksProperties[t].cl!==!1)return!0;t+=1}return!1},initExpressions:function(){this.layerInterface=LayerExpressionInterface(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var t=EffectsExpressionInterface.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(t),this.data.ty===0||this.data.xt?this.compInterface=CompExpressionInterface(this):this.data.ty===4?(this.layerInterface.shapeInterface=ShapeExpressionInterface(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):this.data.ty===5&&(this.layerInterface.textInterface=TextExpressionInterface(this),this.layerInterface.text=this.layerInterface.textInterface)},setBlendMode:function(){var t=getBlendMode(this.data.bm),r=this.baseElement||this.layerElement;r.style["mix-blend-mode"]=t},initBaseData:function(t,r,i){this.globalData=r,this.comp=i,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}};function FrameElement(){}FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,r){var i,s=this.dynamicProperties.length;for(i=0;i<s;i+=1)(r||this._isParent&&this.dynamicProperties[i].propType==="transform")&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){this.dynamicProperties.indexOf(t)===-1&&this.dynamicProperties.push(t)}};function _typeof$2(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$2=function(r){return typeof r}:_typeof$2=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$2(e)}var FootageInterface=function(){var e=function(i){var s="",a=i.getFootageData();function n(){return s="",a=i.getFootageData(),p}function p(c){if(a[c])return s=c,a=a[c],_typeof$2(a)==="object"?p:a;var m=c.indexOf(s);if(m!==-1){var g=parseInt(c.substr(m+s.length),10);return a=a[g],_typeof$2(a)==="object"?p:a}return""}return n},t=function(i){function s(a){return a==="Outline"?s.outlineInterface():null}return s._name="Outline",s.outlineInterface=e(i),s};return function(r){function i(s){return s==="Data"?i.dataInterface:null}return i._name="Data",i.dataInterface=t(r),i}}();function FootageElement(e,t,r){this.initFrame(),this.initRenderable(),this.assetData=t.getAssetData(e.refId),this.footageData=t.imageLoader.getAsset(this.assetData),this.initBaseData(e,t,r)}FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){this.layerInterface=FootageInterface(this)},FootageElement.prototype.getFootageData=function(){return this.footageData};function AudioElement(e,t,r){this.initFrame(),this.initRenderable(),this.assetData=t.getAssetData(e.refId),this.initBaseData(e,t,r),this._isPlaying=!1,this._canPlay=!1;var i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=e.tm?PropertyFactory.getProp(this,e.tm,0,t.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,e.au&&e.au.lv?e.au.lv:{k:[100]},1,.01,this)}AudioElement.prototype.prepareFrame=function(e){if(this.prepareRenderableFrame(e,!0),this.prepareProperties(e,!0),this.tm._placeholder)this._currentTime=e/this.data.sr;else{var t=this.tm.v;this._currentTime=t}this._volume=this.lv.v[0];var r=this._volume*this._volumeMultiplier;this._previousVolume!==r&&(this._previousVolume=r,this.audio.volume(r))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(e){this.audio.rate(e)},AudioElement.prototype.volume=function(e){this._volumeMultiplier=e,this._previousVolume=e*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){};function BaseRenderer(){}BaseRenderer.prototype.checkLayers=function(e){var t,r=this.layers.length,i;for(this.completeLayers=!0,t=r-1;t>=0;t-=1)this.elements[t]||(i=this.layers[t],i.ip-i.st<=e-this.layers[t].st&&i.op-i.st>e-this.layers[t].st&&this.buildItem(t)),this.completeLayers=this.elements[t]?this.completeLayers:!1;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(e){switch(e.ty){case 2:return this.createImage(e);case 0:return this.createComp(e);case 1:return this.createSolid(e);case 3:return this.createNull(e);case 4:return this.createShape(e);case 5:return this.createText(e);case 6:return this.createAudio(e);case 13:return this.createCamera(e);case 15:return this.createFootage(e);default:return this.createNull(e)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(e){return new AudioElement(e,this.globalData,this)},BaseRenderer.prototype.createFootage=function(e){return new FootageElement(e,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var e,t=this.layers.length;for(e=0;e<t;e+=1)this.buildItem(e);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(e){this.completeLayers=!1;var t,r=e.length,i,s=this.layers.length;for(t=0;t<r;t+=1)for(i=0;i<s;){if(this.layers[i].id===e[t].id){this.layers[i]=e[t];break}i+=1}},BaseRenderer.prototype.setProjectInterface=function(e){this.globalData.projectInterface=e},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(e,t,r){for(var i=this.elements,s=this.layers,a=0,n=s.length;a<n;)s[a].ind==t&&(!i[a]||i[a]===!0?(this.buildItem(a),this.addPendingElement(e)):(r.push(i[a]),i[a].setAsParent(),s[a].parent!==void 0?this.buildElementParenting(e,s[a].parent,r):e.setHierarchy(r))),a+=1},BaseRenderer.prototype.addPendingElement=function(e){this.pendingElements.push(e)},BaseRenderer.prototype.searchExtraCompositions=function(e){var t,r=e.length;for(t=0;t<r;t+=1)if(e[t].xt){var i=this.createComp(e[t]);i.initExpressions(),this.globalData.projectInterface.registerComposition(i)}},BaseRenderer.prototype.getElementByPath=function(e){var t=e.shift(),r;if(typeof t=="number")r=this.elements[t];else{var i,s=this.elements.length;for(i=0;i<s;i+=1)if(this.elements[i].data.nm===t){r=this.elements[i];break}}return e.length===0?r:r.getElementByPath(e)},BaseRenderer.prototype.setupGlobalData=function(e,t){this.globalData.fontManager=new FontManager,this.globalData.fontManager.addChars(e.chars),this.globalData.fontManager.addFonts(e.fonts,t),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=e.fr,this.globalData.nm=e.nm,this.globalData.compSize={w:e.w,h:e.h}};function TransformElement(){}TransformElement.prototype={initTransform:function(){this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_opMdf:!1,mat:new Matrix},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty!==11},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,r=this.finalTransform.mat,i=0,s=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<s;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,r.cloneFromProps(t),i=0;i<s;i+=1)t=this.hierarchy[i].finalTransform.mProp.v.props,r.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}},globalToLocal:function(t){var r=[];r.push(this.finalTransform);for(var i=!0,s=this.comp;i;)s.finalTransform?(s.data.hasMask&&r.splice(0,0,s.finalTransform),s=s.comp):i=!1;var a,n=r.length,p;for(a=0;a<n;a+=1)p=r[a].mat.applyToPointArray(0,0,0),t=[t[0]-p[0],t[1]-p[1],0];return t},mHelper:new Matrix};function MaskElement(e,t,r){this.data=e,this.element=t,this.globalData=r,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var i=this.globalData.defs,s,a=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(a),this.solidPath="";var n,p=this.masksProperties,c=0,m=[],g,x,u=createElementID(),P,v,d,S,o="clipPath",f="clip-path";for(s=0;s<a;s+=1)if((p[s].mode!=="a"&&p[s].mode!=="n"||p[s].inv||p[s].o.k!==100||p[s].o.x)&&(o="mask",f="mask"),(p[s].mode==="s"||p[s].mode==="i")&&c===0?(P=createNS("rect"),P.setAttribute("fill","#ffffff"),P.setAttribute("width",this.element.comp.data.w||0),P.setAttribute("height",this.element.comp.data.h||0),m.push(P)):P=null,n=createNS("path"),p[s].mode==="n")this.viewData[s]={op:PropertyFactory.getProp(this.element,p[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,p[s],3),elem:n,lastPath:""},i.appendChild(n);else{c+=1,n.setAttribute("fill",p[s].mode==="s"?"#000000":"#ffffff"),n.setAttribute("clip-rule","nonzero");var l;if(p[s].x.k!==0?(o="mask",f="mask",S=PropertyFactory.getProp(this.element,p[s].x,0,null,this.element),l=createElementID(),v=createNS("filter"),v.setAttribute("id",l),d=createNS("feMorphology"),d.setAttribute("operator","erode"),d.setAttribute("in","SourceGraphic"),d.setAttribute("radius","0"),v.appendChild(d),i.appendChild(v),n.setAttribute("stroke",p[s].mode==="s"?"#000000":"#ffffff")):(d=null,S=null),this.storedData[s]={elem:n,x:S,expan:d,lastPath:"",lastOperator:"",filterId:l,lastRadius:0},p[s].mode==="i"){x=m.length;var h=createNS("g");for(g=0;g<x;g+=1)h.appendChild(m[g]);var y=createNS("mask");y.setAttribute("mask-type","alpha"),y.setAttribute("id",u+"_"+c),y.appendChild(n),i.appendChild(y),h.setAttribute("mask","url("+getLocationHref()+"#"+u+"_"+c+")"),m.length=0,m.push(h)}else m.push(n);p[s].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[s]={elem:n,lastPath:"",op:PropertyFactory.getProp(this.element,p[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,p[s],3),invRect:P},this.viewData[s].prop.k||this.drawPath(p[s],this.viewData[s].prop.v,this.viewData[s])}for(this.maskElement=createNS(o),a=m.length,s=0;s<a;s+=1)this.maskElement.appendChild(m[s]);c>0&&(this.maskElement.setAttribute("id",u),this.element.maskedElement.setAttribute(f,"url("+getLocationHref()+"#"+u+")"),i.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}MaskElement.prototype.getMaskProperty=function(e){return this.viewData[e].prop},MaskElement.prototype.renderFrame=function(e){var t=this.element.finalTransform.mat,r,i=this.masksProperties.length;for(r=0;r<i;r+=1)if((this.viewData[r].prop._mdf||e)&&this.drawPath(this.masksProperties[r],this.viewData[r].prop.v,this.viewData[r]),(this.viewData[r].op._mdf||e)&&this.viewData[r].elem.setAttribute("fill-opacity",this.viewData[r].op.v),this.masksProperties[r].mode!=="n"&&(this.viewData[r].invRect&&(this.element.finalTransform.mProp._mdf||e)&&this.viewData[r].invRect.setAttribute("transform",t.getInverseMatrix().to2dCSS()),this.storedData[r].x&&(this.storedData[r].x._mdf||e))){var s=this.storedData[r].expan;this.storedData[r].x.v<0?(this.storedData[r].lastOperator!=="erode"&&(this.storedData[r].lastOperator="erode",this.storedData[r].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[r].filterId+")")),s.setAttribute("radius",-this.storedData[r].x.v)):(this.storedData[r].lastOperator!=="dilate"&&(this.storedData[r].lastOperator="dilate",this.storedData[r].elem.setAttribute("filter",null)),this.storedData[r].elem.setAttribute("stroke-width",this.storedData[r].x.v*2))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var e="M0,0 ";return e+=" h"+this.globalData.compSize.w,e+=" v"+this.globalData.compSize.h,e+=" h-"+this.globalData.compSize.w,e+=" v-"+this.globalData.compSize.h+" ",e},MaskElement.prototype.drawPath=function(e,t,r){var i=" M"+t.v[0][0]+","+t.v[0][1],s,a;for(a=t._length,s=1;s<a;s+=1)i+=" C"+t.o[s-1][0]+","+t.o[s-1][1]+" "+t.i[s][0]+","+t.i[s][1]+" "+t.v[s][0]+","+t.v[s][1];if(t.c&&a>1&&(i+=" C"+t.o[s-1][0]+","+t.o[s-1][1]+" "+t.i[0][0]+","+t.i[0][1]+" "+t.v[0][0]+","+t.v[0][1]),r.lastPath!==i){var n="";r.elem&&(t.c&&(n=e.inv?this.solidPath+i:i),r.elem.setAttribute("d",n)),r.lastPath=i}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var e={};e.createFilter=t,e.createAlphaToLuminanceFilter=r;function t(i,s){var a=createNS("filter");return a.setAttribute("id",i),s!==!0&&(a.setAttribute("filterUnits","objectBoundingBox"),a.setAttribute("x","0%"),a.setAttribute("y","0%"),a.setAttribute("width","100%"),a.setAttribute("height","100%")),a}function r(){var i=createNS("feColorMatrix");return i.setAttribute("type","matrix"),i.setAttribute("color-interpolation-filters","sRGB"),i.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),i}return e}(),featureSupport=function(){var e={maskType:!0};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(e.maskType=!1),e}(),registeredEffects={},idPrefix="filter_result_";function SVGEffects(e){var t,r="SourceGraphic",i=e.data.ef?e.data.ef.length:0,s=createElementID(),a=filtersFactory.createFilter(s,!0),n=0;this.filters=[];var p;for(t=0;t<i;t+=1){p=null;var c=e.data.ef[t].ty;if(registeredEffects[c]){var m=registeredEffects[c].effect;p=new m(a,e.effectsManager.effectElements[t],e,idPrefix+n,r),r=idPrefix+n,registeredEffects[c].countsAsEffect&&(n+=1)}p&&this.filters.push(p)}n&&(e.globalData.defs.appendChild(a),e.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+s+")")),this.filters.length&&e.addRenderableComponent(this)}SVGEffects.prototype.renderFrame=function(e){var t,r=this.filters.length;for(t=0;t<r;t+=1)this.filters[t].renderFrame(e)};function registerEffect(e,t,r){registeredEffects[e]={effect:t,countsAsEffect:r}}function SVGBaseElement(){}SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null,r,i,s;if(this.data.td){if(this.data.td==3||this.data.td==1){var a=createNS("mask");a.setAttribute("id",this.layerId),a.setAttribute("mask-type",this.data.td==3?"luminance":"alpha"),a.appendChild(this.layerElement),t=a,this.globalData.defs.appendChild(a),!featureSupport.maskType&&this.data.td==1&&(a.setAttribute("mask-type","luminance"),r=createElementID(),i=filtersFactory.createFilter(r),this.globalData.defs.appendChild(i),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),s=createNS("g"),s.appendChild(this.layerElement),t=s,a.appendChild(s),s.setAttribute("filter","url("+getLocationHref()+"#"+r+")"))}else if(this.data.td==2){var n=createNS("mask");n.setAttribute("id",this.layerId),n.setAttribute("mask-type","alpha");var p=createNS("g");n.appendChild(p),r=createElementID(),i=filtersFactory.createFilter(r);var c=createNS("feComponentTransfer");c.setAttribute("in","SourceGraphic"),i.appendChild(c);var m=createNS("feFuncA");m.setAttribute("type","table"),m.setAttribute("tableValues","1.0 0.0"),c.appendChild(m),this.globalData.defs.appendChild(i);var g=createNS("rect");g.setAttribute("width",this.comp.data.w),g.setAttribute("height",this.comp.data.h),g.setAttribute("x","0"),g.setAttribute("y","0"),g.setAttribute("fill","#ffffff"),g.setAttribute("opacity","0"),p.setAttribute("filter","url("+getLocationHref()+"#"+r+")"),p.appendChild(g),p.appendChild(this.layerElement),t=p,featureSupport.maskType||(n.setAttribute("mask-type","luminance"),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),s=createNS("g"),p.appendChild(g),s.appendChild(this.layerElement),t=s,p.appendChild(s)),this.globalData.defs.appendChild(n)}}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),this.data.ty===0&&!this.data.hd){var x=createNS("clipPath"),u=createNS("path");u.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var P=createElementID();if(x.setAttribute("id",P),x.appendChild(u),this.globalData.defs.appendChild(x),this.checkMasks()){var v=createNS("g");v.setAttribute("clip-path","url("+getLocationHref()+"#"+P+")"),v.appendChild(this.layerElement),this.transformedElement=v,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+P+")")}this.data.bm!==0&&this.setBlendMode()},renderElement:function(){this.finalTransform._matMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.mat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.mProp.o.v)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this)},setMatte:function(t){!this.matteElement||this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}};function HierarchyElement(){}HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){this.data.parent!==void 0&&this.comp.buildElementParenting(this,this.data.parent,[])}};function RenderableDOMElement(){}(function(){var e={initElement:function(r,i,s){this.initFrame(),this.initBaseData(r,i,s),this.initTransform(r,i,s),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){if(!this.hidden&&(!this.isInRange||this.isTransparent)){var r=this.baseElement||this.layerElement;r.style.display="none",this.hidden=!0}},show:function(){if(this.isInRange&&!this.isTransparent){if(!this.data.hd){var r=this.baseElement||this.layerElement;r.style.display="block"}this.hidden=!1,this._isFirstFrame=!0}},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(r){this._mdf=!1,this.prepareRenderableFrame(r),this.prepareProperties(r,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}};extendPrototype([RenderableElement,createProxyFunction(e)],RenderableDOMElement)})();function IImageElement(e,t,r){this.assetData=t.getAssetData(e.refId),this.initElement(e,t,r),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var e=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",e),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect};function ProcessedElement(e,t){this.elem=e,this.pos=t}function IShapeElement(){}IShapeElement.prototype={addShapeToModifiers:function(t){var r,i=this.shapeModifiers.length;for(r=0;r<i;r+=1)this.shapeModifiers[r].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var r=0,i=this.shapeModifiers.length;r<i;)if(this.shapeModifiers[r].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(!!this.shapeModifiers.length){var t,r=this.shapes.length;for(t=0;t<r;t+=1)this.shapes[t].sh.reset();r=this.shapeModifiers.length;var i;for(t=r-1;t>=0&&(i=this.shapeModifiers[t].processShapes(this._isFirstFrame),!i);t-=1);}},searchProcessedElement:function(t){for(var r=this.processedElements,i=0,s=r.length;i<s;){if(r[i].elem===t)return r[i].pos;i+=1}return 0},addProcessedElement:function(t,r){for(var i=this.processedElements,s=i.length;s;)if(s-=1,i[s].elem===t){i[s].pos=r;return}i.push(new ProcessedElement(t,r))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(e,t,r){this.caches=[],this.styles=[],this.transformers=e,this.lStr="",this.sh=r,this.lvl=t,this._isAnimated=!!r.k;for(var i=0,s=e.length;i<s;){if(e[i].mProps.dynamicProperties.length){this._isAnimated=!0;break}i+=1}}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0};function SVGStyleData(e,t){this.data=e,this.type=e.ty,this.d="",this.lvl=t,this._mdf=!1,this.closed=e.hd===!0,this.pElem=createNS("path"),this.msElem=null}SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1};function DashProperty(e,t,r,i){this.elem=e,this.frameId=-1,this.dataProps=createSizedArray(t.length),this.renderer=r,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",t.length?t.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(i);var s,a=t.length||0,n;for(s=0;s<a;s+=1)n=PropertyFactory.getProp(e,t[s].v,0,0,this),this.k=n.k||this.k,this.dataProps[s]={n:t[s].n,p:n};this.k||this.getValue(!0),this._isAnimated=this.k}DashProperty.prototype.getValue=function(e){if(!(this.elem.globalData.frameId===this.frameId&&!e)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||e,this._mdf)){var t=0,r=this.dataProps.length;for(this.renderer==="svg"&&(this.dashStr=""),t=0;t<r;t+=1)this.dataProps[t].n!=="o"?this.renderer==="svg"?this.dashStr+=" "+this.dataProps[t].p.v:this.dashArray[t]=this.dataProps[t].p.v:this.dashoffset[0]=this.dataProps[t].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty);function SVGStrokeStyleData(e,t,r){this.initDynamicPropertyContainer(e),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(e,t.o,0,.01,this),this.w=PropertyFactory.getProp(e,t.w,0,null,this),this.d=new DashProperty(e,t.d||{},"svg",this),this.c=PropertyFactory.getProp(e,t.c,1,255,this),this.style=r,this._isAnimated=!!this._isAnimated}extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData);function SVGFillStyleData(e,t,r){this.initDynamicPropertyContainer(e),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(e,t.o,0,.01,this),this.c=PropertyFactory.getProp(e,t.c,1,255,this),this.style=r}extendPrototype([DynamicPropertyContainer],SVGFillStyleData);function SVGNoStyleData(e,t,r){this.initDynamicPropertyContainer(e),this.getValue=this.iterateDynamicProperties,this.style=r}extendPrototype([DynamicPropertyContainer],SVGNoStyleData);function GradientProperty(e,t,r){this.data=t,this.c=createTypedArray("uint8c",t.p*4);var i=t.k.k[0].s?t.k.k[0].s.length-t.p*4:t.k.k.length-t.p*4;this.o=createTypedArray("float32",i),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=i,this.initDynamicPropertyContainer(r),this.prop=PropertyFactory.getProp(e,t.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}GradientProperty.prototype.comparePoints=function(e,t){for(var r=0,i=this.o.length/2,s;r<i;){if(s=Math.abs(e[r*4]-e[t*4+r*2]),s>.01)return!1;r+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!==this.c.length/4)return!1;if(this.data.k.k[0].s)for(var e=0,t=this.data.k.k.length;e<t;){if(!this.comparePoints(this.data.k.k[e].s,this.data.p))return!1;e+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(e){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||e){var t,r=this.data.p*4,i,s;for(t=0;t<r;t+=1)i=t%4===0?100:255,s=Math.round(this.prop.v[t]*i),this.c[t]!==s&&(this.c[t]=s,this._cmdf=!e);if(this.o.length)for(r=this.prop.v.length,t=this.data.p*4;t<r;t+=1)i=t%2===0?100:1,s=t%2===0?Math.round(this.prop.v[t]*100):this.prop.v[t],this.o[t-this.data.p*4]!==s&&(this.o[t-this.data.p*4]=s,this._omdf=!e);this._mdf=!e}},extendPrototype([DynamicPropertyContainer],GradientProperty);function SVGGradientFillStyleData(e,t,r){this.initDynamicPropertyContainer(e),this.getValue=this.iterateDynamicProperties,this.initGradientData(e,t,r)}SVGGradientFillStyleData.prototype.initGradientData=function(e,t,r){this.o=PropertyFactory.getProp(e,t.o,0,.01,this),this.s=PropertyFactory.getProp(e,t.s,1,null,this),this.e=PropertyFactory.getProp(e,t.e,1,null,this),this.h=PropertyFactory.getProp(e,t.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(e,t.a||{k:0},0,degToRads,this),this.g=new GradientProperty(e,t.g,this),this.style=r,this.stops=[],this.setGradientData(r.pElem,t),this.setGradientOpacity(t,r),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(e,t){var r=createElementID(),i=createNS(t.t===1?"linearGradient":"radialGradient");i.setAttribute("id",r),i.setAttribute("spreadMethod","pad"),i.setAttribute("gradientUnits","userSpaceOnUse");var s=[],a,n,p;for(p=t.g.p*4,n=0;n<p;n+=4)a=createNS("stop"),i.appendChild(a),s.push(a);e.setAttribute(t.ty==="gf"?"fill":"stroke","url("+getLocationHref()+"#"+r+")"),this.gf=i,this.cst=s},SVGGradientFillStyleData.prototype.setGradientOpacity=function(e,t){if(this.g._hasOpacity&&!this.g._collapsable){var r,i,s,a=createNS("mask"),n=createNS("path");a.appendChild(n);var p=createElementID(),c=createElementID();a.setAttribute("id",c);var m=createNS(e.t===1?"linearGradient":"radialGradient");m.setAttribute("id",p),m.setAttribute("spreadMethod","pad"),m.setAttribute("gradientUnits","userSpaceOnUse"),s=e.g.k.k[0].s?e.g.k.k[0].s.length:e.g.k.k.length;var g=this.stops;for(i=e.g.p*4;i<s;i+=2)r=createNS("stop"),r.setAttribute("stop-color","rgb(255,255,255)"),m.appendChild(r),g.push(r);n.setAttribute(e.ty==="gf"?"fill":"stroke","url("+getLocationHref()+"#"+p+")"),e.ty==="gs"&&(n.setAttribute("stroke-linecap",lineCapEnum[e.lc||2]),n.setAttribute("stroke-linejoin",lineJoinEnum[e.lj||2]),e.lj===1&&n.setAttribute("stroke-miterlimit",e.ml)),this.of=m,this.ms=a,this.ost=g,this.maskId=c,t.msElem=n}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData);function SVGGradientStrokeStyleData(e,t,r){this.initDynamicPropertyContainer(e),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(e,t.w,0,null,this),this.d=new DashProperty(e,t.d||{},"svg",this),this.initGradientData(e,t,r),this._isAnimated=!!this._isAnimated}extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(e,t,r){this.transform={mProps:e,op:t,container:r},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}var buildShapeString=function(t,r,i,s){if(r===0)return"";var a=t.o,n=t.i,p=t.v,c,m=" M"+s.applyToPointStringified(p[0][0],p[0][1]);for(c=1;c<r;c+=1)m+=" C"+s.applyToPointStringified(a[c-1][0],a[c-1][1])+" "+s.applyToPointStringified(n[c][0],n[c][1])+" "+s.applyToPointStringified(p[c][0],p[c][1]);return i&&r&&(m+=" C"+s.applyToPointStringified(a[c-1][0],a[c-1][1])+" "+s.applyToPointStringified(n[0][0],n[0][1])+" "+s.applyToPointStringified(p[0][0],p[0][1]),m+="z"),m},SVGElementsRenderer=function(){var e=new Matrix,t=new Matrix,r={createRenderFunction:i};function i(x){switch(x.ty){case"fl":return p;case"gf":return m;case"gs":return c;case"st":return g;case"sh":case"el":case"rc":case"sr":return n;case"tr":return s;case"no":return a;default:return null}}function s(x,u,P){(P||u.transform.op._mdf)&&u.transform.container.setAttribute("opacity",u.transform.op.v),(P||u.transform.mProps._mdf)&&u.transform.container.setAttribute("transform",u.transform.mProps.v.to2dCSS())}function a(){}function n(x,u,P){var v,d,S,o,f,l,h=u.styles.length,y=u.lvl,b,_,C,F,L;for(l=0;l<h;l+=1){if(o=u.sh._mdf||P,u.styles[l].lvl<y){for(_=t.reset(),F=y-u.styles[l].lvl,L=u.transformers.length-1;!o&&F>0;)o=u.transformers[L].mProps._mdf||o,F-=1,L-=1;if(o)for(F=y-u.styles[l].lvl,L=u.transformers.length-1;F>0;)C=u.transformers[L].mProps.v.props,_.transform(C[0],C[1],C[2],C[3],C[4],C[5],C[6],C[7],C[8],C[9],C[10],C[11],C[12],C[13],C[14],C[15]),F-=1,L-=1}else _=e;if(b=u.sh.paths,d=b._length,o){for(S="",v=0;v<d;v+=1)f=b.shapes[v],f&&f._length&&(S+=buildShapeString(f,f._length,f.c,_));u.caches[l]=S}else S=u.caches[l];u.styles[l].d+=x.hd===!0?"":S,u.styles[l]._mdf=o||u.styles[l]._mdf}}function p(x,u,P){var v=u.style;(u.c._mdf||P)&&v.pElem.setAttribute("fill","rgb("+bmFloor(u.c.v[0])+","+bmFloor(u.c.v[1])+","+bmFloor(u.c.v[2])+")"),(u.o._mdf||P)&&v.pElem.setAttribute("fill-opacity",u.o.v)}function c(x,u,P){m(x,u,P),g(x,u,P)}function m(x,u,P){var v=u.gf,d=u.g._hasOpacity,S=u.s.v,o=u.e.v;if(u.o._mdf||P){var f=x.ty==="gf"?"fill-opacity":"stroke-opacity";u.style.pElem.setAttribute(f,u.o.v)}if(u.s._mdf||P){var l=x.t===1?"x1":"cx",h=l==="x1"?"y1":"cy";v.setAttribute(l,S[0]),v.setAttribute(h,S[1]),d&&!u.g._collapsable&&(u.of.setAttribute(l,S[0]),u.of.setAttribute(h,S[1]))}var y,b,_,C;if(u.g._cmdf||P){y=u.cst;var F=u.g.c;for(_=y.length,b=0;b<_;b+=1)C=y[b],C.setAttribute("offset",F[b*4]+"%"),C.setAttribute("stop-color","rgb("+F[b*4+1]+","+F[b*4+2]+","+F[b*4+3]+")")}if(d&&(u.g._omdf||P)){var L=u.g.o;for(u.g._collapsable?y=u.cst:y=u.ost,_=y.length,b=0;b<_;b+=1)C=y[b],u.g._collapsable||C.setAttribute("offset",L[b*2]+"%"),C.setAttribute("stop-opacity",L[b*2+1])}if(x.t===1)(u.e._mdf||P)&&(v.setAttribute("x2",o[0]),v.setAttribute("y2",o[1]),d&&!u.g._collapsable&&(u.of.setAttribute("x2",o[0]),u.of.setAttribute("y2",o[1])));else{var R;if((u.s._mdf||u.e._mdf||P)&&(R=Math.sqrt(Math.pow(S[0]-o[0],2)+Math.pow(S[1]-o[1],2)),v.setAttribute("r",R),d&&!u.g._collapsable&&u.of.setAttribute("r",R)),u.e._mdf||u.h._mdf||u.a._mdf||P){R||(R=Math.sqrt(Math.pow(S[0]-o[0],2)+Math.pow(S[1]-o[1],2)));var I=Math.atan2(o[1]-S[1],o[0]-S[0]),D=u.h.v;D>=1?D=.99:D<=-1&&(D=-.99);var B=R*D,w=Math.cos(I+u.a.v)*B+S[0],k=Math.sin(I+u.a.v)*B+S[1];v.setAttribute("fx",w),v.setAttribute("fy",k),d&&!u.g._collapsable&&(u.of.setAttribute("fx",w),u.of.setAttribute("fy",k))}}}function g(x,u,P){var v=u.style,d=u.d;d&&(d._mdf||P)&&d.dashStr&&(v.pElem.setAttribute("stroke-dasharray",d.dashStr),v.pElem.setAttribute("stroke-dashoffset",d.dashoffset[0])),u.c&&(u.c._mdf||P)&&v.pElem.setAttribute("stroke","rgb("+bmFloor(u.c.v[0])+","+bmFloor(u.c.v[1])+","+bmFloor(u.c.v[2])+")"),(u.o._mdf||P)&&v.pElem.setAttribute("stroke-opacity",u.o.v),(u.w._mdf||P)&&(v.pElem.setAttribute("stroke-width",u.w.v),v.msElem&&v.msElem.setAttribute("stroke-width",u.w.v))}return r}();function SVGShapeElement(e,t,r){this.shapes=[],this.shapesData=e.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(e,t,r),this.prevViewData=[]}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var e,t=this.shapes.length,r,i,s=this.stylesList.length,a,n=[],p=!1;for(i=0;i<s;i+=1){for(a=this.stylesList[i],p=!1,n.length=0,e=0;e<t;e+=1)r=this.shapes[e],r.styles.indexOf(a)!==-1&&(n.push(r),p=r._isAnimated||p);n.length>1&&p&&this.setShapesAsAnimated(n)}},SVGShapeElement.prototype.setShapesAsAnimated=function(e){var t,r=e.length;for(t=0;t<r;t+=1)e[t].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(e,t){var r,i=new SVGStyleData(e,t),s=i.pElem;if(e.ty==="st")r=new SVGStrokeStyleData(this,e,i);else if(e.ty==="fl")r=new SVGFillStyleData(this,e,i);else if(e.ty==="gf"||e.ty==="gs"){var a=e.ty==="gf"?SVGGradientFillStyleData:SVGGradientStrokeStyleData;r=new a(this,e,i),this.globalData.defs.appendChild(r.gf),r.maskId&&(this.globalData.defs.appendChild(r.ms),this.globalData.defs.appendChild(r.of),s.setAttribute("mask","url("+getLocationHref()+"#"+r.maskId+")"))}else e.ty==="no"&&(r=new SVGNoStyleData(this,e,i));return(e.ty==="st"||e.ty==="gs")&&(s.setAttribute("stroke-linecap",lineCapEnum[e.lc||2]),s.setAttribute("stroke-linejoin",lineJoinEnum[e.lj||2]),s.setAttribute("fill-opacity","0"),e.lj===1&&s.setAttribute("stroke-miterlimit",e.ml)),e.r===2&&s.setAttribute("fill-rule","evenodd"),e.ln&&s.setAttribute("id",e.ln),e.cl&&s.setAttribute("class",e.cl),e.bm&&(s.style["mix-blend-mode"]=getBlendMode(e.bm)),this.stylesList.push(i),this.addToAnimatedContents(e,r),r},SVGShapeElement.prototype.createGroupElement=function(e){var t=new ShapeGroupData;return e.ln&&t.gr.setAttribute("id",e.ln),e.cl&&t.gr.setAttribute("class",e.cl),e.bm&&(t.gr.style["mix-blend-mode"]=getBlendMode(e.bm)),t},SVGShapeElement.prototype.createTransformElement=function(e,t){var r=TransformPropertyFactory.getTransformProperty(this,e,this),i=new SVGTransformData(r,r.o,t);return this.addToAnimatedContents(e,i),i},SVGShapeElement.prototype.createShapeElement=function(e,t,r){var i=4;e.ty==="rc"?i=5:e.ty==="el"?i=6:e.ty==="sr"&&(i=7);var s=ShapePropertyFactory.getShapeProp(this,e,i,this),a=new SVGShapeData(t,r,s);return this.shapes.push(a),this.addShapeToModifiers(a),this.addToAnimatedContents(e,a),a},SVGShapeElement.prototype.addToAnimatedContents=function(e,t){for(var r=0,i=this.animatedContents.length;r<i;){if(this.animatedContents[r].element===t)return;r+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(e),element:t,data:e})},SVGShapeElement.prototype.setElementStyles=function(e){var t=e.styles,r,i=this.stylesList.length;for(r=0;r<i;r+=1)this.stylesList[r].closed||t.push(this.stylesList[r])},SVGShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var e,t=this.itemsData.length;for(e=0;e<t;e+=1)this.prevViewData[e]=this.itemsData[e];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),t=this.dynamicProperties.length,e=0;e<t;e+=1)this.dynamicProperties[e].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(e,t,r,i,s,a,n){var p=[].concat(a),c,m=e.length-1,g,x,u=[],P=[],v,d,S;for(c=m;c>=0;c-=1){if(S=this.searchProcessedElement(e[c]),S?t[c]=r[S-1]:e[c]._render=n,e[c].ty==="fl"||e[c].ty==="st"||e[c].ty==="gf"||e[c].ty==="gs"||e[c].ty==="no")S?t[c].style.closed=!1:t[c]=this.createStyleElement(e[c],s),e[c]._render&&t[c].style.pElem.parentNode!==i&&i.appendChild(t[c].style.pElem),u.push(t[c].style);else if(e[c].ty==="gr"){if(!S)t[c]=this.createGroupElement(e[c]);else for(x=t[c].it.length,g=0;g<x;g+=1)t[c].prevViewData[g]=t[c].it[g];this.searchShapes(e[c].it,t[c].it,t[c].prevViewData,t[c].gr,s+1,p,n),e[c]._render&&t[c].gr.parentNode!==i&&i.appendChild(t[c].gr)}else e[c].ty==="tr"?(S||(t[c]=this.createTransformElement(e[c],i)),v=t[c].transform,p.push(v)):e[c].ty==="sh"||e[c].ty==="rc"||e[c].ty==="el"||e[c].ty==="sr"?(S||(t[c]=this.createShapeElement(e[c],p,s)),this.setElementStyles(t[c])):e[c].ty==="tm"||e[c].ty==="rd"||e[c].ty==="ms"||e[c].ty==="pb"?(S?(d=t[c],d.closed=!1):(d=ShapeModifiers.getModifier(e[c].ty),d.init(this,e[c]),t[c]=d,this.shapeModifiers.push(d)),P.push(d)):e[c].ty==="rp"&&(S?(d=t[c],d.closed=!0):(d=ShapeModifiers.getModifier(e[c].ty),t[c]=d,d.init(this,e,c,t),this.shapeModifiers.push(d),n=!1),P.push(d));this.addProcessedElement(e[c],c+1)}for(m=u.length,c=0;c<m;c+=1)u[c].closed=!0;for(m=P.length,c=0;c<m;c+=1)P[c].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){this.renderModifiers();var e,t=this.stylesList.length;for(e=0;e<t;e+=1)this.stylesList[e].reset();for(this.renderShape(),e=0;e<t;e+=1)(this.stylesList[e]._mdf||this._isFirstFrame)&&(this.stylesList[e].msElem&&(this.stylesList[e].msElem.setAttribute("d",this.stylesList[e].d),this.stylesList[e].d="M0 0"+this.stylesList[e].d),this.stylesList[e].pElem.setAttribute("d",this.stylesList[e].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var e,t=this.animatedContents.length,r;for(e=0;e<t;e+=1)r=this.animatedContents[e],(this._isFirstFrame||r.element._isAnimated)&&r.data!==!0&&r.fn(r.data,r.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null};function LetterProps(e,t,r,i,s,a){this.o=e,this.sw=t,this.sc=r,this.fc=i,this.m=s,this.p=a,this._mdf={o:!0,sw:!!t,sc:!!r,fc:!!i,m:!0,p:!0}}LetterProps.prototype.update=function(e,t,r,i,s,a){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var n=!1;return this.o!==e&&(this.o=e,this._mdf.o=!0,n=!0),this.sw!==t&&(this.sw=t,this._mdf.sw=!0,n=!0),this.sc!==r&&(this.sc=r,this._mdf.sc=!0,n=!0),this.fc!==i&&(this.fc=i,this._mdf.fc=!0,n=!0),this.m!==s&&(this.m=s,this._mdf.m=!0,n=!0),a.length&&(this.p[0]!==a[0]||this.p[1]!==a[1]||this.p[4]!==a[4]||this.p[5]!==a[5]||this.p[12]!==a[12]||this.p[13]!==a[13])&&(this.p=a,this._mdf.p=!0,n=!0),n};function TextProperty(e,t){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,this.data=t,this.elem=e,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},TextProperty.prototype.setCurrentData=function(e){e.__complete||this.completeTextData(e),this.currentData=e,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(e){this.effectsSequence.push(e),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(e){if(!((this.elem.globalData.frameId===this.frameId||!this.effectsSequence.length)&&!e)){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var t=this.currentData,r=this.keysIndex;if(this.lock){this.setCurrentData(this.currentData);return}this.lock=!0,this._mdf=!1;var i,s=this.effectsSequence.length,a=e||this.data.d.k[this.keysIndex].s;for(i=0;i<s;i+=1)r!==this.keysIndex?a=this.effectsSequence[i](a,a.t):a=this.effectsSequence[i](this.currentData,a.t);t!==a&&this.setCurrentData(a),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}},TextProperty.prototype.getKeyframeValue=function(){for(var e=this.data.d.k,t=this.elem.comp.renderedFrame,r=0,i=e.length;r<=i-1&&!(r===i-1||e[r+1].t>t);)r+=1;return this.keysIndex!==r&&(this.keysIndex=r),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(e){for(var t=[],r=0,i=e.length,s,a,n=!1;r<i;)s=e.charCodeAt(r),FontManager.isCombinedCharacter(s)?t[t.length-1]+=e.charAt(r):s>=55296&&s<=56319?(a=e.charCodeAt(r+1),a>=56320&&a<=57343?(n||FontManager.isModifier(s,a)?(t[t.length-1]+=e.substr(r,2),n=!1):t.push(e.substr(r,2)),r+=1):t.push(e.charAt(r))):s>56319?(a=e.charCodeAt(r+1),FontManager.isZeroWidthJoiner(s,a)?(n=!0,t[t.length-1]+=e.substr(r,2),r+=1):t.push(e.charAt(r))):FontManager.isZeroWidthJoiner(s)?(t[t.length-1]+=e.charAt(r),n=!0):t.push(e.charAt(r)),r+=1;return t},TextProperty.prototype.completeTextData=function(e){e.__complete=!0;var t=this.elem.globalData.fontManager,r=this.data,i=[],s,a,n,p=0,c,m=r.m.g,g=0,x=0,u=0,P=[],v=0,d=0,S,o,f=t.getFontByName(e.f),l,h=0,y=getFontProperties(f);e.fWeight=y.weight,e.fStyle=y.style,e.finalSize=e.s,e.finalText=this.buildFinalText(e.t),a=e.finalText.length,e.finalLineHeight=e.lh;var b=e.tr/1e3*e.finalSize,_;if(e.sz)for(var C=!0,F=e.sz[0],L=e.sz[1],R,I;C;){I=this.buildFinalText(e.t),R=0,v=0,a=I.length,b=e.tr/1e3*e.finalSize;var D=-1;for(s=0;s<a;s+=1)_=I[s].charCodeAt(0),n=!1,I[s]===" "?D=s:(_===13||_===3)&&(v=0,n=!0,R+=e.finalLineHeight||e.finalSize*1.2),t.chars?(l=t.getCharData(I[s],f.fStyle,f.fFamily),h=n?0:l.w*e.finalSize/100):h=t.measureText(I[s],e.f,e.finalSize),v+h>F&&I[s]!==" "?(D===-1?a+=1:s=D,R+=e.finalLineHeight||e.finalSize*1.2,I.splice(s,D===s?1:0,"\r"),D=-1,v=0):(v+=h,v+=b);R+=f.ascent*e.finalSize/100,this.canResize&&e.finalSize>this.minimumFontSize&&L<R?(e.finalSize-=1,e.finalLineHeight=e.finalSize*e.lh/e.s):(e.finalText=I,a=e.finalText.length,C=!1)}v=-b,h=0;var B=0,w;for(s=0;s<a;s+=1)if(n=!1,w=e.finalText[s],_=w.charCodeAt(0),_===13||_===3?(B=0,P.push(v),d=v>d?v:d,v=-2*b,c="",n=!0,u+=1):c=w,t.chars?(l=t.getCharData(w,f.fStyle,t.getFontByName(e.f).fFamily),h=n?0:l.w*e.finalSize/100):h=t.measureText(c,e.f,e.finalSize),w===" "?B+=h+b:(v+=h+b+B,B=0),i.push({l:h,an:h,add:g,n,anIndexes:[],val:c,line:u,animatorJustifyOffset:0}),m==2){if(g+=h,c===""||c===" "||s===a-1){for((c===""||c===" ")&&(g-=h);x<=s;)i[x].an=g,i[x].ind=p,i[x].extra=h,x+=1;p+=1,g=0}}else if(m==3){if(g+=h,c===""||s===a-1){for(c===""&&(g-=h);x<=s;)i[x].an=g,i[x].ind=p,i[x].extra=h,x+=1;g=0,p+=1}}else i[p].ind=p,i[p].extra=0,p+=1;if(e.l=i,d=v>d?v:d,P.push(v),e.sz)e.boxWidth=e.sz[0],e.justifyOffset=0;else switch(e.boxWidth=d,e.j){case 1:e.justifyOffset=-e.boxWidth;break;case 2:e.justifyOffset=-e.boxWidth/2;break;default:e.justifyOffset=0}e.lineWidths=P;var k=r.a,A,E;o=k.length;var T,V,M=[];for(S=0;S<o;S+=1){for(A=k[S],A.a.sc&&(e.strokeColorAnim=!0),A.a.sw&&(e.strokeWidthAnim=!0),(A.a.fc||A.a.fh||A.a.fs||A.a.fb)&&(e.fillColorAnim=!0),V=0,T=A.s.b,s=0;s<a;s+=1)E=i[s],E.anIndexes[S]=V,(T==1&&E.val!==""||T==2&&E.val!==""&&E.val!==" "||T==3&&(E.n||E.val==" "||s==a-1)||T==4&&(E.n||s==a-1))&&(A.s.rn===1&&M.push(V),V+=1);r.a[S].s.totalChars=V;var G=-1,N;if(A.s.rn===1)for(s=0;s<a;s+=1)E=i[s],G!=E.anIndexes[S]&&(G=E.anIndexes[S],N=M.splice(Math.floor(Math.random()*M.length),1)[0]),E.anIndexes[S]=N}e.yOffset=e.finalLineHeight||e.finalSize*1.2,e.ls=e.ls||0,e.ascent=f.ascent*e.finalSize/100},TextProperty.prototype.updateDocumentData=function(e,t){t=t===void 0?this.keysIndex:t;var r=this.copyData({},this.data.d.k[t].s);r=this.copyData(r,e),this.data.d.k[t].s=r,this.recalculate(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(e){var t=this.data.d.k[e].s;t.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(t)},TextProperty.prototype.canResizeFont=function(e){this.canResize=e,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(e){this.minimumFontSize=Math.floor(e)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var e=Math.max,t=Math.min,r=Math.floor;function i(a,n){this._currentTextLength=-1,this.k=!1,this.data=n,this.elem=a,this.comp=a.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(a),this.s=PropertyFactory.getProp(a,n.s||{k:0},0,0,this),"e"in n?this.e=PropertyFactory.getProp(a,n.e,0,0,this):this.e={v:100},this.o=PropertyFactory.getProp(a,n.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(a,n.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(a,n.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(a,n.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(a,n.a,0,.01,this),this.dynamicProperties.length||this.getValue()}i.prototype={getMult:function(n){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var p=0,c=0,m=1,g=1;this.ne.v>0?p=this.ne.v/100:c=-this.ne.v/100,this.xe.v>0?m=1-this.xe.v/100:g=1+this.xe.v/100;var x=BezierFactory.getBezierEasing(p,c,m,g).get,u=0,P=this.finalS,v=this.finalE,d=this.data.sh;if(d===2)v===P?u=n>=v?1:0:u=e(0,t(.5/(v-P)+(n-P)/(v-P),1)),u=x(u);else if(d===3)v===P?u=n>=v?0:1:u=1-e(0,t(.5/(v-P)+(n-P)/(v-P),1)),u=x(u);else if(d===4)v===P?u=0:(u=e(0,t(.5/(v-P)+(n-P)/(v-P),1)),u<.5?u*=2:u=1-2*(u-.5)),u=x(u);else if(d===5){if(v===P)u=0;else{var S=v-P;n=t(e(0,n+.5-P),v-P);var o=-S/2+n,f=S/2;u=Math.sqrt(1-o*o/(f*f))}u=x(u)}else d===6?(v===P?u=0:(n=t(e(0,n+.5-P),v-P),u=(1+Math.cos(Math.PI+Math.PI*2*n/(v-P)))/2),u=x(u)):(n>=r(P)&&(n-P<0?u=e(0,t(t(v,1)-(P-n),1)):u=e(0,t(v-n,1))),u=x(u));if(this.sm.v!==100){var l=this.sm.v*.01;l===0&&(l=1e-8);var h=.5-l*.5;u<h?u=0:(u=(u-h)/l,u>1&&(u=1))}return u*this.a.v},getValue:function(n){this.iterateDynamicProperties(),this._mdf=n||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,n&&this.data.r===2&&(this.e.v=this._currentTextLength);var p=this.data.r===2?1:100/this.data.totalChars,c=this.o.v/p,m=this.s.v/p+c,g=this.e.v/p+c;if(m>g){var x=m;m=g,g=x}this.finalS=m,this.finalE=g}},extendPrototype([DynamicPropertyContainer],i);function s(a,n,p){return new i(a,n,p)}return{getTextSelectorProp:s}}();function TextAnimatorDataProperty(e,t,r){var i={propType:!1},s=PropertyFactory.getProp,a=t.a;this.a={r:a.r?s(e,a.r,0,degToRads,r):i,rx:a.rx?s(e,a.rx,0,degToRads,r):i,ry:a.ry?s(e,a.ry,0,degToRads,r):i,sk:a.sk?s(e,a.sk,0,degToRads,r):i,sa:a.sa?s(e,a.sa,0,degToRads,r):i,s:a.s?s(e,a.s,1,.01,r):i,a:a.a?s(e,a.a,1,0,r):i,o:a.o?s(e,a.o,0,.01,r):i,p:a.p?s(e,a.p,1,0,r):i,sw:a.sw?s(e,a.sw,0,0,r):i,sc:a.sc?s(e,a.sc,1,0,r):i,fc:a.fc?s(e,a.fc,1,0,r):i,fh:a.fh?s(e,a.fh,0,0,r):i,fs:a.fs?s(e,a.fs,0,.01,r):i,fb:a.fb?s(e,a.fb,0,.01,r):i,t:a.t?s(e,a.t,0,0,r):i},this.s=TextSelectorProp.getTextSelectorProp(e,t.s,r),this.s.t=t.s.t}function TextAnimatorProperty(e,t,r){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=e,this._renderType=t,this._elem=r,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(r)}TextAnimatorProperty.prototype.searchProperties=function(){var e,t=this._textData.a.length,r,i=PropertyFactory.getProp;for(e=0;e<t;e+=1)r=this._textData.a[e],this._animatorsData[e]=new TextAnimatorDataProperty(this._elem,r,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(e,t){if(this.lettersChangedFlag=t,!(!this._mdf&&!this._isFirstFrame&&!t&&(!this._hasMaskedPath||!this._pathData.m._mdf))){this._isFirstFrame=!1;var r=this._moreOptions.alignment.v,i=this._animatorsData,s=this._textData,a=this.mHelper,n=this._renderType,p=this.renderedLetters.length,c,m,g,x,u=e.l,P,v,d,S,o,f,l,h,y,b,_,C,F,L,R;if(this._hasMaskedPath){if(R=this._pathData.m,!this._pathData.n||this._pathData._mdf){var I=R.v;this._pathData.r.v&&(I=I.reverse()),P={tLength:0,segments:[]},x=I._length-1;var D;for(C=0,g=0;g<x;g+=1)D=bez.buildBezierData(I.v[g],I.v[g+1],[I.o[g][0]-I.v[g][0],I.o[g][1]-I.v[g][1]],[I.i[g+1][0]-I.v[g+1][0],I.i[g+1][1]-I.v[g+1][1]]),P.tLength+=D.segmentLength,P.segments.push(D),C+=D.segmentLength;g=x,R.v.c&&(D=bez.buildBezierData(I.v[g],I.v[0],[I.o[g][0]-I.v[g][0],I.o[g][1]-I.v[g][1]],[I.i[0][0]-I.v[0][0],I.i[0][1]-I.v[0][1]]),P.tLength+=D.segmentLength,P.segments.push(D),C+=D.segmentLength),this._pathData.pi=P}if(P=this._pathData.pi,v=this._pathData.f.v,l=0,f=1,S=0,o=!0,b=P.segments,v<0&&R.v.c)for(P.tLength<Math.abs(v)&&(v=-Math.abs(v)%P.tLength),l=b.length-1,y=b[l].points,f=y.length-1;v<0;)v+=y[f].partialLength,f-=1,f<0&&(l-=1,y=b[l].points,f=y.length-1);y=b[l].points,h=y[f-1],d=y[f],_=d.partialLength}x=u.length,c=0,m=0;var B=e.finalSize*1.2*.714,w=!0,k,A,E,T,V;T=i.length;var M,G=-1,N,z,j,H=v,Y=l,K=f,U=-1,$,q,X,O,W,te,ae,re,ee="",ie=this.defaultPropsArray,se;if(e.j===2||e.j===1){var Z=0,ne=0,oe=e.j===2?-.5:-1,J=0,he=!0;for(g=0;g<x;g+=1)if(u[g].n){for(Z&&(Z+=ne);J<g;)u[J].animatorJustifyOffset=Z,J+=1;Z=0,he=!0}else{for(E=0;E<T;E+=1)k=i[E].a,k.t.propType&&(he&&e.j===2&&(ne+=k.t.v*oe),A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),M.length?Z+=k.t.v*M[0]*oe:Z+=k.t.v*M*oe);he=!1}for(Z&&(Z+=ne);J<g;)u[J].animatorJustifyOffset=Z,J+=1}for(g=0;g<x;g+=1){if(a.reset(),$=1,u[g].n)c=0,m+=e.yOffset,m+=w?1:0,v=H,w=!1,this._hasMaskedPath&&(l=Y,f=K,y=b[l].points,h=y[f-1],d=y[f],_=d.partialLength,S=0),ee="",re="",te="",se="",ie=this.defaultPropsArray;else{if(this._hasMaskedPath){if(U!==u[g].line){switch(e.j){case 1:v+=C-e.lineWidths[u[g].line];break;case 2:v+=(C-e.lineWidths[u[g].line])/2;break;default:break}U=u[g].line}G!==u[g].ind&&(u[G]&&(v+=u[G].extra),v+=u[g].an/2,G=u[g].ind),v+=r[0]*u[g].an*.005;var Q=0;for(E=0;E<T;E+=1)k=i[E].a,k.p.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),M.length?Q+=k.p.v[0]*M[0]:Q+=k.p.v[0]*M),k.a.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),M.length?Q+=k.a.v[0]*M[0]:Q+=k.a.v[0]*M);for(o=!0,this._pathData.a.v&&(v=u[0].an*.5+(C-this._pathData.f.v-u[0].an*.5-u[u.length-1].an*.5)*G/(x-1),v+=this._pathData.f.v);o;)S+_>=v+Q||!y?(F=(v+Q-S)/d.partialLength,z=h.point[0]+(d.point[0]-h.point[0])*F,j=h.point[1]+(d.point[1]-h.point[1])*F,a.translate(-r[0]*u[g].an*.005,-(r[1]*B)*.01),o=!1):y&&(S+=d.partialLength,f+=1,f>=y.length&&(f=0,l+=1,b[l]?y=b[l].points:R.v.c?(f=0,l=0,y=b[l].points):(S-=d.partialLength,y=null)),y&&(h=d,d=y[f],_=d.partialLength));N=u[g].an/2-u[g].add,a.translate(-N,0,0)}else N=u[g].an/2-u[g].add,a.translate(-N,0,0),a.translate(-r[0]*u[g].an*.005,-r[1]*B*.01,0);for(E=0;E<T;E+=1)k=i[E].a,k.t.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),(c!==0||e.j!==0)&&(this._hasMaskedPath?M.length?v+=k.t.v*M[0]:v+=k.t.v*M:M.length?c+=k.t.v*M[0]:c+=k.t.v*M));for(e.strokeWidthAnim&&(X=e.sw||0),e.strokeColorAnim&&(e.sc?q=[e.sc[0],e.sc[1],e.sc[2]]:q=[0,0,0]),e.fillColorAnim&&e.fc&&(O=[e.fc[0],e.fc[1],e.fc[2]]),E=0;E<T;E+=1)k=i[E].a,k.a.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),M.length?a.translate(-k.a.v[0]*M[0],-k.a.v[1]*M[1],k.a.v[2]*M[2]):a.translate(-k.a.v[0]*M,-k.a.v[1]*M,k.a.v[2]*M));for(E=0;E<T;E+=1)k=i[E].a,k.s.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),M.length?a.scale(1+(k.s.v[0]-1)*M[0],1+(k.s.v[1]-1)*M[1],1):a.scale(1+(k.s.v[0]-1)*M,1+(k.s.v[1]-1)*M,1));for(E=0;E<T;E+=1){if(k=i[E].a,A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),k.sk.propType&&(M.length?a.skewFromAxis(-k.sk.v*M[0],k.sa.v*M[1]):a.skewFromAxis(-k.sk.v*M,k.sa.v*M)),k.r.propType&&(M.length?a.rotateZ(-k.r.v*M[2]):a.rotateZ(-k.r.v*M)),k.ry.propType&&(M.length?a.rotateY(k.ry.v*M[1]):a.rotateY(k.ry.v*M)),k.rx.propType&&(M.length?a.rotateX(k.rx.v*M[0]):a.rotateX(k.rx.v*M)),k.o.propType&&(M.length?$+=(k.o.v*M[0]-$)*M[0]:$+=(k.o.v*M-$)*M),e.strokeWidthAnim&&k.sw.propType&&(M.length?X+=k.sw.v*M[0]:X+=k.sw.v*M),e.strokeColorAnim&&k.sc.propType)for(W=0;W<3;W+=1)M.length?q[W]+=(k.sc.v[W]-q[W])*M[0]:q[W]+=(k.sc.v[W]-q[W])*M;if(e.fillColorAnim&&e.fc){if(k.fc.propType)for(W=0;W<3;W+=1)M.length?O[W]+=(k.fc.v[W]-O[W])*M[0]:O[W]+=(k.fc.v[W]-O[W])*M;k.fh.propType&&(M.length?O=addHueToRGB(O,k.fh.v*M[0]):O=addHueToRGB(O,k.fh.v*M)),k.fs.propType&&(M.length?O=addSaturationToRGB(O,k.fs.v*M[0]):O=addSaturationToRGB(O,k.fs.v*M)),k.fb.propType&&(M.length?O=addBrightnessToRGB(O,k.fb.v*M[0]):O=addBrightnessToRGB(O,k.fb.v*M))}}for(E=0;E<T;E+=1)k=i[E].a,k.p.propType&&(A=i[E].s,M=A.getMult(u[g].anIndexes[E],s.a[E].s.totalChars),this._hasMaskedPath?M.length?a.translate(0,k.p.v[1]*M[0],-k.p.v[2]*M[1]):a.translate(0,k.p.v[1]*M,-k.p.v[2]*M):M.length?a.translate(k.p.v[0]*M[0],k.p.v[1]*M[1],-k.p.v[2]*M[2]):a.translate(k.p.v[0]*M,k.p.v[1]*M,-k.p.v[2]*M));if(e.strokeWidthAnim&&(te=X<0?0:X),e.strokeColorAnim&&(ae="rgb("+Math.round(q[0]*255)+","+Math.round(q[1]*255)+","+Math.round(q[2]*255)+")"),e.fillColorAnim&&e.fc&&(re="rgb("+Math.round(O[0]*255)+","+Math.round(O[1]*255)+","+Math.round(O[2]*255)+")"),this._hasMaskedPath){if(a.translate(0,-e.ls),a.translate(0,r[1]*B*.01+m,0),this._pathData.p.v){L=(d.point[1]-h.point[1])/(d.point[0]-h.point[0]);var le=Math.atan(L)*180/Math.PI;d.point[0]<h.point[0]&&(le+=180),a.rotate(-le*Math.PI/180)}a.translate(z,j,0),v-=r[0]*u[g].an*.005,u[g+1]&&G!==u[g+1].ind&&(v+=u[g].an/2,v+=e.tr*.001*e.finalSize)}else{switch(a.translate(c,m,0),e.ps&&a.translate(e.ps[0],e.ps[1]+e.ascent,0),e.j){case 1:a.translate(u[g].animatorJustifyOffset+e.justifyOffset+(e.boxWidth-e.lineWidths[u[g].line]),0,0);break;case 2:a.translate(u[g].animatorJustifyOffset+e.justifyOffset+(e.boxWidth-e.lineWidths[u[g].line])/2,0,0);break;default:break}a.translate(0,-e.ls),a.translate(N,0,0),a.translate(r[0]*u[g].an*.005,r[1]*B*.01,0),c+=u[g].l+e.tr*.001*e.finalSize}n==="html"?ee=a.toCSS():n==="svg"?ee=a.to2dCSS():ie=[a.props[0],a.props[1],a.props[2],a.props[3],a.props[4],a.props[5],a.props[6],a.props[7],a.props[8],a.props[9],a.props[10],a.props[11],a.props[12],a.props[13],a.props[14],a.props[15]],se=$}p<=g?(V=new LetterProps(se,te,ae,re,ee,ie),this.renderedLetters.push(V),p+=1,this.lettersChangedFlag=!0):(V=this.renderedLetters[g],this.lettersChangedFlag=V.update(se,te,ae,re,ee,ie)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty);function ITextElement(){}ITextElement.prototype.initElement=function(e,t,r){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(e,t,r),this.textProperty=new TextProperty(this,e.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(e.t,this.renderType,this),this.initTransform(e,t,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(e){this._mdf=!1,this.prepareRenderableFrame(e),this.prepareProperties(e,this.isInRange),(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)},ITextElement.prototype.createPathShape=function(e,t){var r,i=t.length,s,a="";for(r=0;r<i;r+=1)t[r].ty==="sh"&&(s=t[r].ks.k,a+=buildShapeString(s,s.i.length,!0,e));return a},ITextElement.prototype.updateDocumentData=function(e,t){this.textProperty.updateDocumentData(e,t)},ITextElement.prototype.canResizeFont=function(e){this.textProperty.canResizeFont(e)},ITextElement.prototype.setMinimumFontSize=function(e){this.textProperty.setMinimumFontSize(e)},ITextElement.prototype.applyTextPropertiesToMatrix=function(e,t,r,i,s){switch(e.ps&&t.translate(e.ps[0],e.ps[1]+e.ascent,0),t.translate(0,-e.ls,0),e.j){case 1:t.translate(e.justifyOffset+(e.boxWidth-e.lineWidths[r]),0,0);break;case 2:t.translate(e.justifyOffset+(e.boxWidth-e.lineWidths[r])/2,0,0);break;default:break}t.translate(i,s,0)},ITextElement.prototype.buildColor=function(e){return"rgb("+Math.round(e[0]*255)+","+Math.round(e[1]*255)+","+Math.round(e[2]*255)+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){};var emptyShapeData={shapes:[]};function SVGTextLottieElement(e,t,r){this.textSpans=[],this.renderType="svg",this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(e){for(var t=0,r=e.length,i=[],s="";t<r;)e[t]===String.fromCharCode(13)||e[t]===String.fromCharCode(3)?(i.push(s),s=""):s+=e[t],t+=1;return i.push(s),i},SVGTextLottieElement.prototype.buildShapeData=function(e,t){if(e.shapes&&e.shapes.length){var r=e.shapes[0];if(r.it){var i=r.it[r.it.length-1];i.s&&(i.s.k[0]=t,i.s.k[1]=t)}}return e},SVGTextLottieElement.prototype.buildNewText=function(){this.addDynamicProperty(this);var e,t,r=this.textProperty.currentData;this.renderedLetters=createSizedArray(r?r.l.length:0),r.fc?this.layerElement.setAttribute("fill",this.buildColor(r.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),r.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(r.sc)),this.layerElement.setAttribute("stroke-width",r.sw)),this.layerElement.setAttribute("font-size",r.finalSize);var i=this.globalData.fontManager.getFontByName(r.f);if(i.fClass)this.layerElement.setAttribute("class",i.fClass);else{this.layerElement.setAttribute("font-family",i.fFamily);var s=r.fWeight,a=r.fStyle;this.layerElement.setAttribute("font-style",a),this.layerElement.setAttribute("font-weight",s)}this.layerElement.setAttribute("aria-label",r.t);var n=r.l||[],p=!!this.globalData.fontManager.chars;t=n.length;var c,m=this.mHelper,g="",x=this.data.singleShape,u=0,P=0,v=!0,d=r.tr*.001*r.finalSize;if(x&&!p&&!r.sz){var S=this.textContainer,o="start";switch(r.j){case 1:o="end";break;case 2:o="middle";break;default:o="start";break}S.setAttribute("text-anchor",o),S.setAttribute("letter-spacing",d);var f=this.buildTextContents(r.finalText);for(t=f.length,P=r.ps?r.ps[1]+r.ascent:0,e=0;e<t;e+=1)c=this.textSpans[e].span||createNS("tspan"),c.textContent=f[e],c.setAttribute("x",0),c.setAttribute("y",P),c.style.display="inherit",S.appendChild(c),this.textSpans[e]||(this.textSpans[e]={span:null,glyph:null}),this.textSpans[e].span=c,P+=r.finalLineHeight;this.layerElement.appendChild(S)}else{var l=this.textSpans.length,h;for(e=0;e<t;e+=1){if(this.textSpans[e]||(this.textSpans[e]={span:null,childSpan:null,glyph:null}),!p||!x||e===0){if(c=l>e?this.textSpans[e].span:createNS(p?"g":"text"),l<=e){if(c.setAttribute("stroke-linecap","butt"),c.setAttribute("stroke-linejoin","round"),c.setAttribute("stroke-miterlimit","4"),this.textSpans[e].span=c,p){var y=createNS("g");c.appendChild(y),this.textSpans[e].childSpan=y}this.textSpans[e].span=c,this.layerElement.appendChild(c)}c.style.display="inherit"}if(m.reset(),x&&(n[e].n&&(u=-d,P+=r.yOffset,P+=v?1:0,v=!1),this.applyTextPropertiesToMatrix(r,m,n[e].line,u,P),u+=n[e].l||0,u+=d),p){h=this.globalData.fontManager.getCharData(r.finalText[e],i.fStyle,this.globalData.fontManager.getFontByName(r.f).fFamily);var b;if(h.t===1)b=new SVGCompElement(h.data,this.globalData,this);else{var _=emptyShapeData;h.data&&h.data.shapes&&(_=this.buildShapeData(h.data,r.finalSize)),b=new SVGShapeElement(_,this.globalData,this)}if(this.textSpans[e].glyph){var C=this.textSpans[e].glyph;this.textSpans[e].childSpan.removeChild(C.layerElement),C.destroy()}this.textSpans[e].glyph=b,b._debug=!0,b.prepareFrame(0),b.renderFrame(),this.textSpans[e].childSpan.appendChild(b.layerElement),h.t===1&&this.textSpans[e].childSpan.setAttribute("transform","scale("+r.finalSize/100+","+r.finalSize/100+")")}else x&&c.setAttribute("transform","translate("+m.props[12]+","+m.props[13]+")"),c.textContent=n[e].val,c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}x&&c&&c.setAttribute("d",g)}for(;e<this.textSpans.length;)this.textSpans[e].span.style.display="none",e+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var e=this.layerElement.getBBox();this.bbox={top:e.y,left:e.x,width:e.width,height:e.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var e,t=this.textSpans.length,r;for(this.renderedFrame=this.comp.renderedFrame,e=0;e<t;e+=1)r=this.textSpans[e].glyph,r&&(r.prepareFrame(this.comp.renderedFrame-this.data.st),r._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if((!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){this._sizeChanged=!0;var e,t,r=this.textAnimator.renderedLetters,i=this.textProperty.currentData.l;t=i.length;var s,a,n;for(e=0;e<t;e+=1)i[e].n||(s=r[e],a=this.textSpans[e].span,n=this.textSpans[e].glyph,n&&n.renderFrame(),s._mdf.m&&a.setAttribute("transform",s.m),s._mdf.o&&a.setAttribute("opacity",s.o),s._mdf.sw&&a.setAttribute("stroke-width",s.sw),s._mdf.sc&&a.setAttribute("stroke",s.sc),s._mdf.fc&&a.setAttribute("fill",s.fc))}};function ISolidElement(e,t,r){this.initElement(e,t,r)}extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var e=createNS("rect");e.setAttribute("width",this.data.sw),e.setAttribute("height",this.data.sh),e.setAttribute("fill",this.data.sc),this.layerElement.appendChild(e)};function NullElement(e,t,r){this.initFrame(),this.initBaseData(e,t,r),this.initFrame(),this.initTransform(e,t,r),this.initHierarchy()}NullElement.prototype.prepareFrame=function(e){this.prepareProperties(e,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement);function SVGRendererBase(){}extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(e){return new NullElement(e,this.globalData,this)},SVGRendererBase.prototype.createShape=function(e){return new SVGShapeElement(e,this.globalData,this)},SVGRendererBase.prototype.createText=function(e){return new SVGTextLottieElement(e,this.globalData,this)},SVGRendererBase.prototype.createImage=function(e){return new IImageElement(e,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(e){return new ISolidElement(e,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(e){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+e.w+" "+e.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",e.w),this.svgElement.setAttribute("height",e.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),this.renderConfig.focusable!==void 0&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var t=this.globalData.defs;this.setupGlobalData(e,t),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=e;var r=createNS("clipPath"),i=createNS("rect");i.setAttribute("width",e.w),i.setAttribute("height",e.h),i.setAttribute("x",0),i.setAttribute("y",0);var s=createElementID();r.setAttribute("id",s),r.appendChild(i),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),t.appendChild(r),this.layers=e.layers,this.elements=createSizedArray(e.layers.length)},SVGRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e,t=this.layers?this.layers.length:0;for(e=0;e<t;e+=1)this.elements[e]&&this.elements[e].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.buildItem=function(e){var t=this.elements;if(!(t[e]||this.layers[e].ty===99)){t[e]=!0;var r=this.createItem(this.layers[e]);t[e]=r,getExpressionsPlugin()&&(this.layers[e].ty===0&&this.globalData.projectInterface.registerComposition(r),r.initExpressions()),this.appendElementInPos(r,e),this.layers[e].tt&&(!this.elements[e-1]||this.elements[e-1]===!0?(this.buildItem(e-1),this.addPendingElement(r)):r.setMatte(t[e-1].layerId))}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var e=this.pendingElements.pop();if(e.checkParenting(),e.data.tt)for(var t=0,r=this.elements.length;t<r;){if(this.elements[t]===e){e.setMatte(this.elements[t-1].layerId);break}t+=1}}},SVGRendererBase.prototype.renderFrame=function(e){if(!(this.renderedFrame===e||this.destroyed)){e===null?e=this.renderedFrame:this.renderedFrame=e,this.globalData.frameNum=e,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=e,this.globalData._mdf=!1;var t,r=this.layers.length;for(this.completeLayers||this.checkLayers(e),t=r-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].prepareFrame(e-this.layers[t].st);if(this.globalData._mdf)for(t=0;t<r;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(e,t){var r=e.getBaseElement();if(!!r){for(var i=0,s;i<t;)this.elements[i]&&this.elements[i]!==!0&&this.elements[i].getBaseElement()&&(s=this.elements[i].getBaseElement()),i+=1;s?this.layerElement.insertBefore(r,s):this.layerElement.appendChild(r)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"};function ICompElement(){}extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(e,t,r){this.initFrame(),this.initBaseData(e,t,r),this.initTransform(e,t,r),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),(this.data.xt||!t.progressiveLoad)&&this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(e){if(this._mdf=!1,this.prepareRenderableFrame(e),this.prepareProperties(e,this.isInRange),!(!this.isInRange&&!this.data.xt)){if(this.tm._placeholder)this.renderedFrame=e/this.data.sr;else{var t=this.tm.v;t===this.data.op&&(t=this.data.op-1),this.renderedFrame=t}var r,i=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&(this.elements[r].prepareFrame(this.renderedFrame-this.layers[r].st),this.elements[r]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var e,t=this.layers.length;for(e=0;e<t;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()},ICompElement.prototype.setElements=function(e){this.elements=e},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var e,t=this.layers.length;for(e=0;e<t;e+=1)this.elements[e]&&this.elements[e].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()};function SVGCompElement(e,t,r){this.layers=e.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(e,t,r),this.tm=e.tm?PropertyFactory.getProp(this,e.tm,0,t.frameRate,this):{_placeholder:!0}}extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(e){return new SVGCompElement(e,this.globalData,this)};function SVGRenderer(e,t){this.animationItem=e,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var r="";if(t&&t.title){var i=createNS("title"),s=createElementID();i.setAttribute("id",s),i.textContent=t.title,this.svgElement.appendChild(i),r+=s}if(t&&t.description){var a=createNS("desc"),n=createElementID();a.setAttribute("id",n),a.textContent=t.description,this.svgElement.appendChild(a),r+=" "+n}r&&this.svgElement.setAttribute("aria-labelledby",r);var p=createNS("defs");this.svgElement.appendChild(p);var c=createNS("g");this.svgElement.appendChild(c),this.layerElement=c,this.renderConfig={preserveAspectRatio:t&&t.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:t&&t.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:t&&t.contentVisibility||"visible",progressiveLoad:t&&t.progressiveLoad||!1,hideOnTransparent:!(t&&t.hideOnTransparent===!1),viewBoxOnly:t&&t.viewBoxOnly||!1,viewBoxSize:t&&t.viewBoxSize||!1,className:t&&t.className||"",id:t&&t.id||"",focusable:t&&t.focusable,filterSize:{width:t&&t.filterSize&&t.filterSize.width||"100%",height:t&&t.filterSize&&t.filterSize.height||"100%",x:t&&t.filterSize&&t.filterSize.x||"0%",y:t&&t.filterSize&&t.filterSize.y||"0%"},width:t&&t.width,height:t&&t.height},this.globalData={_mdf:!1,frameNum:-1,defs:p,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(e){return new SVGCompElement(e,this.globalData,this)};function CVContextData(){this.saved=[],this.cArrPos=0,this.cTr=new Matrix,this.cO=1;var e,t=15;for(this.savedOp=createTypedArray("float32",t),e=0;e<t;e+=1)this.saved[e]=createTypedArray("float32",16);this._length=t}CVContextData.prototype.duplicate=function(){var e=this._length*2,t=this.savedOp;this.savedOp=createTypedArray("float32",e),this.savedOp.set(t);var r=0;for(r=this._length;r<e;r+=1)this.saved[r]=createTypedArray("float32",16);this._length=e},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.cO=1};function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}ShapeTransformManager.prototype={addTransformSequence:function(t){var r,i=t.length,s="_";for(r=0;r<i;r+=1)s+=t[r].transform.key+"_";var a=this.sequences[s];return a||(a={transforms:[].concat(t),finalTransform:new Matrix,_mdf:!1},this.sequences[s]=a,this.sequenceList.push(a)),a},processSequence:function(t,r){for(var i=0,s=t.transforms.length,a=r;i<s&&!r;){if(t.transforms[i].transform.mProps._mdf){a=!0;break}i+=1}if(a){var n;for(t.finalTransform.reset(),i=s-1;i>=0;i-=1)n=t.transforms[i].transform.mProps.v.props,t.finalTransform.transform(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])}t._mdf=a},processSequences:function(t){var r,i=this.sequenceList.length;for(r=0;r<i;r+=1)this.processSequence(this.sequenceList[r],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};function CVEffects(){}CVEffects.prototype.renderFrame=function(){};function CVMaskElement(e,t){this.data=e,this.element=t,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var r,i=this.masksProperties.length,s=!1;for(r=0;r<i;r+=1)this.masksProperties[r].mode!=="n"&&(s=!0),this.viewData[r]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[r],3);this.hasMasks=s,s&&this.element.addRenderableComponent(this)}CVMaskElement.prototype.renderFrame=function(){if(!!this.hasMasks){var e=this.element.finalTransform.mat,t=this.element.canvasContext,r,i=this.masksProperties.length,s,a,n;for(t.beginPath(),r=0;r<i;r+=1)if(this.masksProperties[r].mode!=="n"){this.masksProperties[r].inv&&(t.moveTo(0,0),t.lineTo(this.element.globalData.compSize.w,0),t.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),t.lineTo(0,this.element.globalData.compSize.h),t.lineTo(0,0)),n=this.viewData[r].v,s=e.applyToPointArray(n.v[0][0],n.v[0][1],0),t.moveTo(s[0],s[1]);var p,c=n._length;for(p=1;p<c;p+=1)a=e.applyToTriplePoints(n.o[p-1],n.i[p],n.v[p]),t.bezierCurveTo(a[0],a[1],a[2],a[3],a[4],a[5]);a=e.applyToTriplePoints(n.o[p-1],n.i[0],n.v[0]),t.bezierCurveTo(a[0],a[1],a[2],a[3],a[4],a[5])}this.element.globalData.renderer.save(!0),t.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null};function CVBaseElement(){}CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){this.canvasContext=this.globalData.canvasContext,this.renderableEffectsManager=new CVEffects(this)},createContent:function(){},setBlendMode:function(){var t=this.globalData;if(t.blendMode!==this.data.bm){t.blendMode=this.data.bm;var r=getBlendMode(this.data.bm);t.canvasContext.globalCompositeOperation=r}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this)},hideElement:function(){!this.hidden&&(!this.isInRange||this.isTransparent)&&(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},renderFrame:function(){if(!(this.hidden||this.data.hd)){this.renderTransform(),this.renderRenderable(),this.setBlendMode();var t=this.data.ty===0;this.globalData.renderer.save(t),this.globalData.renderer.ctxTransform(this.finalTransform.mat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.mProp.o.v),this.renderInnerContent(),this.globalData.renderer.restore(t),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement;function CVShapeData(e,t,r,i){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var s=4;t.ty==="rc"?s=5:t.ty==="el"?s=6:t.ty==="sr"&&(s=7),this.sh=ShapePropertyFactory.getShapeProp(e,t,s,e);var a,n=r.length,p;for(a=0;a<n;a+=1)r[a].closed||(p={transforms:i.addTransformSequence(r[a].transforms),trNodes:[]},this.styledShapes.push(p),r[a].elements.push(p))}CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated;function CVShapeElement(e,t,r){this.shapes=[],this.shapesData=e.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(e,t){var r={data:e,type:e.ty,preTransforms:this.transformsManager.addTransformSequence(t),transforms:[],elements:[],closed:e.hd===!0},i={};if(e.ty==="fl"||e.ty==="st"?(i.c=PropertyFactory.getProp(this,e.c,1,255,this),i.c.k||(r.co="rgb("+bmFloor(i.c.v[0])+","+bmFloor(i.c.v[1])+","+bmFloor(i.c.v[2])+")")):(e.ty==="gf"||e.ty==="gs")&&(i.s=PropertyFactory.getProp(this,e.s,1,null,this),i.e=PropertyFactory.getProp(this,e.e,1,null,this),i.h=PropertyFactory.getProp(this,e.h||{k:0},0,.01,this),i.a=PropertyFactory.getProp(this,e.a||{k:0},0,degToRads,this),i.g=new GradientProperty(this,e.g,this)),i.o=PropertyFactory.getProp(this,e.o,0,.01,this),e.ty==="st"||e.ty==="gs"){if(r.lc=lineCapEnum[e.lc||2],r.lj=lineJoinEnum[e.lj||2],e.lj==1&&(r.ml=e.ml),i.w=PropertyFactory.getProp(this,e.w,0,null,this),i.w.k||(r.wi=i.w.v),e.d){var s=new DashProperty(this,e.d,"canvas",this);i.d=s,i.d.k||(r.da=i.d.dashArray,r.do=i.d.dashoffset[0])}}else r.r=e.r===2?"evenodd":"nonzero";return this.stylesList.push(r),i.style=r,i},CVShapeElement.prototype.createGroupElement=function(){var e={it:[],prevViewData:[]};return e},CVShapeElement.prototype.createTransformElement=function(e){var t={transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,e.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,e,this)}};return t},CVShapeElement.prototype.createShapeElement=function(e){var t=new CVShapeData(this,e,this.stylesList,this.transformsManager);return this.shapes.push(t),this.addShapeToModifiers(t),t},CVShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var e,t=this.itemsData.length;for(e=0;e<t;e+=1)this.prevViewData[e]=this.itemsData[e];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),t=this.dynamicProperties.length,e=0;e<t;e+=1)this.dynamicProperties[e].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(e){var t,r=this.stylesList.length;for(t=0;t<r;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.push(e)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var e,t=this.stylesList.length;for(e=0;e<t;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.pop()},CVShapeElement.prototype.closeStyles=function(e){var t,r=e.length;for(t=0;t<r;t+=1)e[t].closed=!0},CVShapeElement.prototype.searchShapes=function(e,t,r,i,s){var a,n=e.length-1,p,c,m=[],g=[],x,u,P,v=[].concat(s);for(a=n;a>=0;a-=1){if(x=this.searchProcessedElement(e[a]),x?t[a]=r[x-1]:e[a]._shouldRender=i,e[a].ty==="fl"||e[a].ty==="st"||e[a].ty==="gf"||e[a].ty==="gs")x?t[a].style.closed=!1:t[a]=this.createStyleElement(e[a],v),m.push(t[a].style);else if(e[a].ty==="gr"){if(!x)t[a]=this.createGroupElement(e[a]);else for(c=t[a].it.length,p=0;p<c;p+=1)t[a].prevViewData[p]=t[a].it[p];this.searchShapes(e[a].it,t[a].it,t[a].prevViewData,i,v)}else e[a].ty==="tr"?(x||(P=this.createTransformElement(e[a]),t[a]=P),v.push(t[a]),this.addTransformToStyleList(t[a])):e[a].ty==="sh"||e[a].ty==="rc"||e[a].ty==="el"||e[a].ty==="sr"?x||(t[a]=this.createShapeElement(e[a])):e[a].ty==="tm"||e[a].ty==="rd"||e[a].ty==="pb"?(x?(u=t[a],u.closed=!1):(u=ShapeModifiers.getModifier(e[a].ty),u.init(this,e[a]),t[a]=u,this.shapeModifiers.push(u)),g.push(u)):e[a].ty==="rp"&&(x?(u=t[a],u.closed=!0):(u=ShapeModifiers.getModifier(e[a].ty),t[a]=u,u.init(this,e,a,t),this.shapeModifiers.push(u),i=!1),g.push(u));this.addProcessedElement(e[a],a+1)}for(this.removeTransformFromStyleList(),this.closeStyles(m),n=g.length,a=0;a<n;a+=1)g[a].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(e,t){(e._opMdf||t.op._mdf||this._isFirstFrame)&&(t.opacity=e.opacity,t.opacity*=t.op.v,t._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var e,t=this.stylesList.length,r,i,s,a,n,p,c=this.globalData.renderer,m=this.globalData.canvasContext,g,x;for(e=0;e<t;e+=1)if(x=this.stylesList[e],g=x.type,!((g==="st"||g==="gs")&&x.wi===0||!x.data._shouldRender||x.coOp===0||this.globalData.currentGlobalAlpha===0)){for(c.save(),n=x.elements,g==="st"||g==="gs"?(m.strokeStyle=g==="st"?x.co:x.grd,m.lineWidth=x.wi,m.lineCap=x.lc,m.lineJoin=x.lj,m.miterLimit=x.ml||0):m.fillStyle=g==="fl"?x.co:x.grd,c.ctxOpacity(x.coOp),g!=="st"&&g!=="gs"&&m.beginPath(),c.ctxTransform(x.preTransforms.finalTransform.props),i=n.length,r=0;r<i;r+=1){for((g==="st"||g==="gs")&&(m.beginPath(),x.da&&(m.setLineDash(x.da),m.lineDashOffset=x.do)),p=n[r].trNodes,a=p.length,s=0;s<a;s+=1)p[s].t==="m"?m.moveTo(p[s].p[0],p[s].p[1]):p[s].t==="c"?m.bezierCurveTo(p[s].pts[0],p[s].pts[1],p[s].pts[2],p[s].pts[3],p[s].pts[4],p[s].pts[5]):m.closePath();(g==="st"||g==="gs")&&(m.stroke(),x.da&&m.setLineDash(this.dashResetter))}g!=="st"&&g!=="gs"&&m.fill(x.r),c.restore()}},CVShapeElement.prototype.renderShape=function(e,t,r,i){var s,a=t.length-1,n;for(n=e,s=a;s>=0;s-=1)t[s].ty==="tr"?(n=r[s].transform,this.renderShapeTransform(e,n)):t[s].ty==="sh"||t[s].ty==="el"||t[s].ty==="rc"||t[s].ty==="sr"?this.renderPath(t[s],r[s]):t[s].ty==="fl"?this.renderFill(t[s],r[s],n):t[s].ty==="st"?this.renderStroke(t[s],r[s],n):t[s].ty==="gf"||t[s].ty==="gs"?this.renderGradientFill(t[s],r[s],n):t[s].ty==="gr"?this.renderShape(n,t[s].it,r[s].it):t[s].ty==="tm";i&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(e,t){if(this._isFirstFrame||t._mdf||e.transforms._mdf){var r=e.trNodes,i=t.paths,s,a,n,p=i._length;r.length=0;var c=e.transforms.finalTransform;for(n=0;n<p;n+=1){var m=i.shapes[n];if(m&&m.v){for(a=m._length,s=1;s<a;s+=1)s===1&&r.push({t:"m",p:c.applyToPointArray(m.v[0][0],m.v[0][1],0)}),r.push({t:"c",pts:c.applyToTriplePoints(m.o[s-1],m.i[s],m.v[s])});a===1&&r.push({t:"m",p:c.applyToPointArray(m.v[0][0],m.v[0][1],0)}),m.c&&a&&(r.push({t:"c",pts:c.applyToTriplePoints(m.o[s-1],m.i[0],m.v[0])}),r.push({t:"z"}))}}e.trNodes=r}},CVShapeElement.prototype.renderPath=function(e,t){if(e.hd!==!0&&e._shouldRender){var r,i=t.styledShapes.length;for(r=0;r<i;r+=1)this.renderStyledShape(t.styledShapes[r],t.sh)}},CVShapeElement.prototype.renderFill=function(e,t,r){var i=t.style;(t.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(t.c.v[0])+","+bmFloor(t.c.v[1])+","+bmFloor(t.c.v[2])+")"),(t.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=t.o.v*r.opacity)},CVShapeElement.prototype.renderGradientFill=function(e,t,r){var i=t.style,s;if(!i.grd||t.g._mdf||t.s._mdf||t.e._mdf||e.t!==1&&(t.h._mdf||t.a._mdf)){var a=this.globalData.canvasContext,n=t.s.v,p=t.e.v;if(e.t===1)s=a.createLinearGradient(n[0],n[1],p[0],p[1]);else{var c=Math.sqrt(Math.pow(n[0]-p[0],2)+Math.pow(n[1]-p[1],2)),m=Math.atan2(p[1]-n[1],p[0]-n[0]),g=t.h.v;g>=1?g=.99:g<=-1&&(g=-.99);var x=c*g,u=Math.cos(m+t.a.v)*x+n[0],P=Math.sin(m+t.a.v)*x+n[1];s=a.createRadialGradient(u,P,0,n[0],n[1],c)}var v,d=e.g.p,S=t.g.c,o=1;for(v=0;v<d;v+=1)t.g._hasOpacity&&t.g._collapsable&&(o=t.g.o[v*2+1]),s.addColorStop(S[v*4]/100,"rgba("+S[v*4+1]+","+S[v*4+2]+","+S[v*4+3]+","+o+")");i.grd=s}i.coOp=t.o.v*r.opacity},CVShapeElement.prototype.renderStroke=function(e,t,r){var i=t.style,s=t.d;s&&(s._mdf||this._isFirstFrame)&&(i.da=s.dashArray,i.do=s.dashoffset[0]),(t.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(t.c.v[0])+","+bmFloor(t.c.v[1])+","+bmFloor(t.c.v[2])+")"),(t.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=t.o.v*r.opacity),(t.w._mdf||this._isFirstFrame)&&(i.wi=t.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0};function CVTextElement(e,t,r){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var e=this.textProperty.currentData;this.renderedLetters=createSizedArray(e.l?e.l.length:0);var t=!1;e.fc?(t=!0,this.values.fill=this.buildColor(e.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=t;var r=!1;e.sc&&(r=!0,this.values.stroke=this.buildColor(e.sc),this.values.sWidth=e.sw);var i=this.globalData.fontManager.getFontByName(e.f),s,a,n=e.l,p=this.mHelper;this.stroke=r,this.values.fValue=e.finalSize+"px "+this.globalData.fontManager.getFontByName(e.f).fFamily,a=e.finalText.length;var c,m,g,x,u,P,v,d,S,o,f=this.data.singleShape,l=e.tr*.001*e.finalSize,h=0,y=0,b=!0,_=0;for(s=0;s<a;s+=1){c=this.globalData.fontManager.getCharData(e.finalText[s],i.fStyle,this.globalData.fontManager.getFontByName(e.f).fFamily),m=c&&c.data||{},p.reset(),f&&n[s].n&&(h=-l,y+=e.yOffset,y+=b?1:0,b=!1),u=m.shapes?m.shapes[0].it:[],v=u.length,p.scale(e.finalSize/100,e.finalSize/100),f&&this.applyTextPropertiesToMatrix(e,p,n[s].line,h,y),S=createSizedArray(v-1);var C=0;for(P=0;P<v;P+=1)if(u[P].ty==="sh"){for(x=u[P].ks.k.i.length,d=u[P].ks.k,o=[],g=1;g<x;g+=1)g===1&&o.push(p.applyToX(d.v[0][0],d.v[0][1],0),p.applyToY(d.v[0][0],d.v[0][1],0)),o.push(p.applyToX(d.o[g-1][0],d.o[g-1][1],0),p.applyToY(d.o[g-1][0],d.o[g-1][1],0),p.applyToX(d.i[g][0],d.i[g][1],0),p.applyToY(d.i[g][0],d.i[g][1],0),p.applyToX(d.v[g][0],d.v[g][1],0),p.applyToY(d.v[g][0],d.v[g][1],0));o.push(p.applyToX(d.o[g-1][0],d.o[g-1][1],0),p.applyToY(d.o[g-1][0],d.o[g-1][1],0),p.applyToX(d.i[0][0],d.i[0][1],0),p.applyToY(d.i[0][0],d.i[0][1],0),p.applyToX(d.v[0][0],d.v[0][1],0),p.applyToY(d.v[0][0],d.v[0][1],0)),S[C]=o,C+=1}f&&(h+=n[s].l,h+=l),this.textSpans[_]?this.textSpans[_].elem=S:this.textSpans[_]={elem:S},_+=1}},CVTextElement.prototype.renderInnerContent=function(){var e=this.canvasContext;e.font=this.values.fValue,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4,this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var t,r,i,s,a,n,p=this.textAnimator.renderedLetters,c=this.textProperty.currentData.l;r=c.length;var m,g=null,x=null,u=null,P,v;for(t=0;t<r;t+=1)if(!c[t].n){if(m=p[t],m&&(this.globalData.renderer.save(),this.globalData.renderer.ctxTransform(m.p),this.globalData.renderer.ctxOpacity(m.o)),this.fill){for(m&&m.fc?g!==m.fc&&(g=m.fc,e.fillStyle=m.fc):g!==this.values.fill&&(g=this.values.fill,e.fillStyle=this.values.fill),P=this.textSpans[t].elem,s=P.length,this.globalData.canvasContext.beginPath(),i=0;i<s;i+=1)for(v=P[i],n=v.length,this.globalData.canvasContext.moveTo(v[0],v[1]),a=2;a<n;a+=6)this.globalData.canvasContext.bezierCurveTo(v[a],v[a+1],v[a+2],v[a+3],v[a+4],v[a+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.fill()}if(this.stroke){for(m&&m.sw?u!==m.sw&&(u=m.sw,e.lineWidth=m.sw):u!==this.values.sWidth&&(u=this.values.sWidth,e.lineWidth=this.values.sWidth),m&&m.sc?x!==m.sc&&(x=m.sc,e.strokeStyle=m.sc):x!==this.values.stroke&&(x=this.values.stroke,e.strokeStyle=this.values.stroke),P=this.textSpans[t].elem,s=P.length,this.globalData.canvasContext.beginPath(),i=0;i<s;i+=1)for(v=P[i],n=v.length,this.globalData.canvasContext.moveTo(v[0],v[1]),a=2;a<n;a+=6)this.globalData.canvasContext.bezierCurveTo(v[a],v[a+1],v[a+2],v[a+3],v[a+4],v[a+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.stroke()}m&&this.globalData.renderer.restore()}};function CVImageElement(e,t,r){this.assetData=t.getAssetData(e.refId),this.img=t.imageLoader.getAsset(this.assetData),this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var e=createTag("canvas");e.width=this.assetData.w,e.height=this.assetData.h;var t=e.getContext("2d"),r=this.img.width,i=this.img.height,s=r/i,a=this.assetData.w/this.assetData.h,n,p,c=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;s>a&&c==="xMidYMid slice"||s<a&&c!=="xMidYMid slice"?(p=i,n=p*a):(n=r,p=n/a),t.drawImage(this.img,(r-n)/2,(i-p)/2,n,p,0,0,this.assetData.w,this.assetData.h),this.img=e}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null};function CVSolidElement(e,t,r){this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){var e=this.canvasContext;e.fillStyle=this.data.sc,e.fillRect(0,0,this.data.sw,this.data.sh)};function CanvasRendererBase(e,t){this.animationItem=e,this.renderConfig={clearCanvas:t&&t.clearCanvas!==void 0?t.clearCanvas:!0,context:t&&t.context||null,progressiveLoad:t&&t.progressiveLoad||!1,preserveAspectRatio:t&&t.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:t&&t.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:t&&t.contentVisibility||"visible",className:t&&t.className||"",id:t&&t.id||""},this.renderConfig.dpr=t&&t.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=t&&t.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(e){return new CVShapeElement(e,this.globalData,this)},CanvasRendererBase.prototype.createText=function(e){return new CVTextElement(e,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(e){return new CVImageElement(e,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(e){return new CVSolidElement(e,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(e){if(!(e[0]===1&&e[1]===0&&e[4]===0&&e[5]===1&&e[12]===0&&e[13]===0)){if(!this.renderConfig.clearCanvas){this.canvasContext.transform(e[0],e[1],e[4],e[5],e[12],e[13]);return}this.transformMat.cloneFromProps(e);var t=this.contextData.cTr.props;this.transformMat.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]),this.contextData.cTr.cloneFromProps(this.transformMat.props);var r=this.contextData.cTr.props;this.canvasContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13])}},CanvasRendererBase.prototype.ctxOpacity=function(e){if(!this.renderConfig.clearCanvas){this.canvasContext.globalAlpha*=e<0?0:e,this.globalData.currentGlobalAlpha=this.contextData.cO;return}this.contextData.cO*=e<0?0:e,this.globalData.currentGlobalAlpha!==this.contextData.cO&&(this.canvasContext.globalAlpha=this.contextData.cO,this.globalData.currentGlobalAlpha=this.contextData.cO)},CanvasRendererBase.prototype.reset=function(){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}this.contextData.reset()},CanvasRendererBase.prototype.save=function(e){if(!this.renderConfig.clearCanvas){this.canvasContext.save();return}e&&this.canvasContext.save();var t=this.contextData.cTr.props;this.contextData._length<=this.contextData.cArrPos&&this.contextData.duplicate();var r,i=this.contextData.saved[this.contextData.cArrPos];for(r=0;r<16;r+=1)i[r]=t[r];this.contextData.savedOp[this.contextData.cArrPos]=this.contextData.cO,this.contextData.cArrPos+=1},CanvasRendererBase.prototype.restore=function(e){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}e&&(this.canvasContext.restore(),this.globalData.blendMode="source-over"),this.contextData.cArrPos-=1;var t=this.contextData.saved[this.contextData.cArrPos],r,i=this.contextData.cTr.props;for(r=0;r<16;r+=1)i[r]=t[r];this.canvasContext.setTransform(t[0],t[1],t[4],t[5],t[12],t[13]),t=this.contextData.savedOp[this.contextData.cArrPos],this.contextData.cO=t,this.globalData.currentGlobalAlpha!==t&&(this.canvasContext.globalAlpha=t,this.globalData.currentGlobalAlpha=t)},CanvasRendererBase.prototype.configAnimation=function(e){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var t=this.animationItem.container.style;t.width="100%",t.height="100%";var r="0px 0px 0px";t.transformOrigin=r,t.mozTransformOrigin=r,t.webkitTransformOrigin=r,t["-webkit-transform"]=r,t.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.data=e,this.layers=e.layers,this.transformCanvas={w:e.w,h:e.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(e,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(e.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(){this.reset();var e,t;this.animationItem.wrapper&&this.animationItem.container?(e=this.animationItem.wrapper.offsetWidth,t=this.animationItem.wrapper.offsetHeight,this.animationItem.container.setAttribute("width",e*this.renderConfig.dpr),this.animationItem.container.setAttribute("height",t*this.renderConfig.dpr)):(e=this.canvasContext.canvas.width*this.renderConfig.dpr,t=this.canvasContext.canvas.height*this.renderConfig.dpr);var r,i;if(this.renderConfig.preserveAspectRatio.indexOf("meet")!==-1||this.renderConfig.preserveAspectRatio.indexOf("slice")!==-1){var s=this.renderConfig.preserveAspectRatio.split(" "),a=s[1]||"meet",n=s[0]||"xMidYMid",p=n.substr(0,4),c=n.substr(4);r=e/t,i=this.transformCanvas.w/this.transformCanvas.h,i>r&&a==="meet"||i<r&&a==="slice"?(this.transformCanvas.sx=e/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=e/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=t/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=t/(this.transformCanvas.h/this.renderConfig.dpr)),p==="xMid"&&(i<r&&a==="meet"||i>r&&a==="slice")?this.transformCanvas.tx=(e-this.transformCanvas.w*(t/this.transformCanvas.h))/2*this.renderConfig.dpr:p==="xMax"&&(i<r&&a==="meet"||i>r&&a==="slice")?this.transformCanvas.tx=(e-this.transformCanvas.w*(t/this.transformCanvas.h))*this.renderConfig.dpr:this.transformCanvas.tx=0,c==="YMid"&&(i>r&&a==="meet"||i<r&&a==="slice")?this.transformCanvas.ty=(t-this.transformCanvas.h*(e/this.transformCanvas.w))/2*this.renderConfig.dpr:c==="YMax"&&(i>r&&a==="meet"||i<r&&a==="slice")?this.transformCanvas.ty=(t-this.transformCanvas.h*(e/this.transformCanvas.w))*this.renderConfig.dpr:this.transformCanvas.ty=0}else this.renderConfig.preserveAspectRatio==="none"?(this.transformCanvas.sx=e/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=t/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText="");var e,t=this.layers?this.layers.length:0;for(e=t-1;e>=0;e-=1)this.elements[e]&&this.elements[e].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(e,t){if(!(this.renderedFrame===e&&this.renderConfig.clearCanvas===!0&&!t||this.destroyed||e===-1)){this.renderedFrame=e,this.globalData.frameNum=e-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||t,this.globalData.projectInterface.currentFrame=e;var r,i=this.layers.length;for(this.completeLayers||this.checkLayers(e),r=0;r<i;r+=1)(this.completeLayers||this.elements[r])&&this.elements[r].prepareFrame(e-this.layers[r].st);if(this.globalData._mdf){for(this.renderConfig.clearCanvas===!0?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&this.elements[r].renderFrame();this.renderConfig.clearCanvas!==!0&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(e){var t=this.elements;if(!(t[e]||this.layers[e].ty===99)){var r=this.createItem(this.layers[e],this,this.globalData);t[e]=r,r.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var e=this.pendingElements.pop();e.checkParenting()}},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"};function CVCompElement(e,t,r){this.completeLayers=!1,this.layers=e.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(e,t,r),this.tm=e.tm?PropertyFactory.getProp(this,e.tm,0,t.frameRate,this):{_placeholder:!0}}extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var e=this.canvasContext;e.beginPath(),e.moveTo(0,0),e.lineTo(this.data.w,0),e.lineTo(this.data.w,this.data.h),e.lineTo(0,this.data.h),e.lineTo(0,0),e.clip();var t,r=this.layers.length;for(t=r-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},CVCompElement.prototype.destroy=function(){var e,t=this.layers.length;for(e=t-1;e>=0;e-=1)this.elements[e]&&this.elements[e].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(e){return new CVCompElement(e,this.globalData,this)};function CanvasRenderer(e,t){this.animationItem=e,this.renderConfig={clearCanvas:t&&t.clearCanvas!==void 0?t.clearCanvas:!0,context:t&&t.context||null,progressiveLoad:t&&t.progressiveLoad||!1,preserveAspectRatio:t&&t.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:t&&t.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:t&&t.contentVisibility||"visible",className:t&&t.className||"",id:t&&t.id||""},this.renderConfig.dpr=t&&t.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=t&&t.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(e){return new CVCompElement(e,this.globalData,this)};function HBaseElement(){}HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),this.data.bm!==0&&this.setBlendMode()},renderElement:function(){var t=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var r=this.finalTransform.mat.toCSS();t.transform=r,t.webkitTransform=r}this.finalTransform._opMdf&&(t.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting;function HSolidElement(e,t,r){this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var e;this.data.hasMask?(e=createNS("rect"),e.setAttribute("width",this.data.sw),e.setAttribute("height",this.data.sh),e.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):(e=createTag("div"),e.style.width=this.data.sw+"px",e.style.height=this.data.sh+"px",e.style.backgroundColor=this.data.sc),this.layerElement.appendChild(e)};function HShapeElement(e,t,r){this.shapes=[],this.shapesData=e.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(e,t,r),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var e;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),e=this.svgElement;else{e=createNS("svg");var t=this.comp.data?this.comp.data:this.globalData.compSize;e.setAttribute("width",t.w),e.setAttribute("height",t.h),e.appendChild(this.shapesContainer),this.layerElement.appendChild(e)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=e},HShapeElement.prototype.getTransformedPoint=function(e,t){var r,i=e.length;for(r=0;r<i;r+=1)t=e[r].mProps.v.applyToPointArray(t[0],t[1],0);return t},HShapeElement.prototype.calculateShapeBoundingBox=function(e,t){var r=e.sh.v,i=e.transformers,s,a=r._length,n,p,c,m;if(!(a<=1)){for(s=0;s<a-1;s+=1)n=this.getTransformedPoint(i,r.v[s]),p=this.getTransformedPoint(i,r.o[s]),c=this.getTransformedPoint(i,r.i[s+1]),m=this.getTransformedPoint(i,r.v[s+1]),this.checkBounds(n,p,c,m,t);r.c&&(n=this.getTransformedPoint(i,r.v[s]),p=this.getTransformedPoint(i,r.o[s]),c=this.getTransformedPoint(i,r.i[0]),m=this.getTransformedPoint(i,r.v[0]),this.checkBounds(n,p,c,m,t))}},HShapeElement.prototype.checkBounds=function(e,t,r,i,s){this.getBoundsOfCurve(e,t,r,i);var a=this.shapeBoundingBox;s.x=bmMin(a.left,s.x),s.xMax=bmMax(a.right,s.xMax),s.y=bmMin(a.top,s.y),s.yMax=bmMax(a.bottom,s.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(e,t,r,i){for(var s=[[e[0],i[0]],[e[1],i[1]]],a,n,p,c,m,g,x,u=0;u<2;++u)n=6*e[u]-12*t[u]+6*r[u],a=-3*e[u]+9*t[u]-9*r[u]+3*i[u],p=3*t[u]-3*e[u],n|=0,a|=0,p|=0,a===0&&n===0||(a===0?(c=-p/n,c>0&&c<1&&s[u].push(this.calculateF(c,e,t,r,i,u))):(m=n*n-4*p*a,m>=0&&(g=(-n+bmSqrt(m))/(2*a),g>0&&g<1&&s[u].push(this.calculateF(g,e,t,r,i,u)),x=(-n-bmSqrt(m))/(2*a),x>0&&x<1&&s[u].push(this.calculateF(x,e,t,r,i,u)))));this.shapeBoundingBox.left=bmMin.apply(null,s[0]),this.shapeBoundingBox.top=bmMin.apply(null,s[1]),this.shapeBoundingBox.right=bmMax.apply(null,s[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,s[1])},HShapeElement.prototype.calculateF=function(e,t,r,i,s,a){return bmPow(1-e,3)*t[a]+3*bmPow(1-e,2)*e*r[a]+3*(1-e)*bmPow(e,2)*i[a]+bmPow(e,3)*s[a]},HShapeElement.prototype.calculateBoundingBox=function(e,t){var r,i=e.length;for(r=0;r<i;r+=1)e[r]&&e[r].sh?this.calculateShapeBoundingBox(e[r],t):e[r]&&e[r].it?this.calculateBoundingBox(e[r].it,t):e[r]&&e[r].style&&e[r].w&&this.expandStrokeBoundingBox(e[r].w,t)},HShapeElement.prototype.expandStrokeBoundingBox=function(e,t){var r=0;if(e.keyframes){for(var i=0;i<e.keyframes.length;i+=1){var s=e.keyframes[i].s;s>r&&(r=s)}r*=e.mult}else r=e.v*e.mult;t.x-=r,t.xMax+=r,t.y-=r,t.yMax+=r},HShapeElement.prototype.currentBoxContains=function(e){return this.currentBBox.x<=e.x&&this.currentBBox.y<=e.y&&this.currentBBox.width+this.currentBBox.x>=e.x+e.width&&this.currentBBox.height+this.currentBBox.y>=e.y+e.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var e=this.tempBoundingBox,t=999999;if(e.x=t,e.xMax=-t,e.y=t,e.yMax=-t,this.calculateBoundingBox(this.itemsData,e),e.width=e.xMax<e.x?0:e.xMax-e.x,e.height=e.yMax<e.y?0:e.yMax-e.y,this.currentBoxContains(e))return;var r=!1;if(this.currentBBox.w!==e.width&&(this.currentBBox.w=e.width,this.shapeCont.setAttribute("width",e.width),r=!0),this.currentBBox.h!==e.height&&(this.currentBBox.h=e.height,this.shapeCont.setAttribute("height",e.height),r=!0),r||this.currentBBox.x!==e.x||this.currentBBox.y!==e.y){this.currentBBox.w=e.width,this.currentBBox.h=e.height,this.currentBBox.x=e.x,this.currentBBox.y=e.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var i=this.shapeCont.style,s="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";i.transform=s,i.webkitTransform=s}}};function HTextElement(e,t,r){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var e=createNS("g");this.maskedElement.appendChild(e),this.innerElem=e}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var e=this.textProperty.currentData;this.renderedLetters=createSizedArray(e.l?e.l.length:0);var t=this.innerElem.style,r=e.fc?this.buildColor(e.fc):"rgba(0,0,0,0)";t.fill=r,t.color=r,e.sc&&(t.stroke=this.buildColor(e.sc),t.strokeWidth=e.sw+"px");var i=this.globalData.fontManager.getFontByName(e.f);if(!this.globalData.fontManager.chars)if(t.fontSize=e.finalSize+"px",t.lineHeight=e.finalSize+"px",i.fClass)this.innerElem.className=i.fClass;else{t.fontFamily=i.fFamily;var s=e.fWeight,a=e.fStyle;t.fontStyle=a,t.fontWeight=s}var n,p,c=e.l;p=c.length;var m,g,x,u=this.mHelper,P,v="",d=0;for(n=0;n<p;n+=1){if(this.globalData.fontManager.chars?(this.textPaths[d]?m=this.textPaths[d]:(m=createNS("path"),m.setAttribute("stroke-linecap",lineCapEnum[1]),m.setAttribute("stroke-linejoin",lineJoinEnum[2]),m.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[d]?(g=this.textSpans[d],x=g.children[0]):(g=createTag("div"),g.style.lineHeight=0,x=createNS("svg"),x.appendChild(m),styleDiv(g)))):this.isMasked?m=this.textPaths[d]?this.textPaths[d]:createNS("text"):this.textSpans[d]?(g=this.textSpans[d],m=this.textPaths[d]):(g=createTag("span"),styleDiv(g),m=createTag("span"),styleDiv(m),g.appendChild(m)),this.globalData.fontManager.chars){var S=this.globalData.fontManager.getCharData(e.finalText[n],i.fStyle,this.globalData.fontManager.getFontByName(e.f).fFamily),o;if(S?o=S.data:o=null,u.reset(),o&&o.shapes&&o.shapes.length&&(P=o.shapes[0].it,u.scale(e.finalSize/100,e.finalSize/100),v=this.createPathShape(u,P),m.setAttribute("d",v)),this.isMasked)this.innerElem.appendChild(m);else{if(this.innerElem.appendChild(g),o&&o.shapes){document.body.appendChild(x);var f=x.getBBox();x.setAttribute("width",f.width+2),x.setAttribute("height",f.height+2),x.setAttribute("viewBox",f.x-1+" "+(f.y-1)+" "+(f.width+2)+" "+(f.height+2));var l=x.style,h="translate("+(f.x-1)+"px,"+(f.y-1)+"px)";l.transform=h,l.webkitTransform=h,c[n].yOffset=f.y-1}else x.setAttribute("width",1),x.setAttribute("height",1);g.appendChild(x)}}else if(m.textContent=c[n].val,m.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(m);else{this.innerElem.appendChild(g);var y=m.style,b="translate3d(0,"+-e.finalSize/1.2+"px,0)";y.transform=b,y.webkitTransform=b}this.isMasked?this.textSpans[d]=m:this.textSpans[d]=g,this.textSpans[d].style.display="block",this.textPaths[d]=m,d+=1}for(;d<this.textSpans.length;)this.textSpans[d].style.display="none",d+=1},HTextElement.prototype.renderInnerContent=function(){var e;if(this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),e=this.svgElement.style;var t="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";e.transform=t,e.webkitTransform=t}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),!(!this.lettersChangedFlag&&!this.textAnimator.lettersChangedFlag)){var r,i,s=0,a=this.textAnimator.renderedLetters,n=this.textProperty.currentData.l;i=n.length;var p,c,m;for(r=0;r<i;r+=1)n[r].n?s+=1:(c=this.textSpans[r],m=this.textPaths[r],p=a[s],s+=1,p._mdf.m&&(this.isMasked?c.setAttribute("transform",p.m):(c.style.webkitTransform=p.m,c.style.transform=p.m)),c.style.opacity=p.o,p.sw&&p._mdf.sw&&m.setAttribute("stroke-width",p.sw),p.sc&&p._mdf.sc&&m.setAttribute("stroke",p.sc),p.fc&&p._mdf.fc&&(m.setAttribute("fill",p.fc),m.style.color=p.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var g=this.innerElem.getBBox();this.currentBBox.w!==g.width&&(this.currentBBox.w=g.width,this.svgElement.setAttribute("width",g.width)),this.currentBBox.h!==g.height&&(this.currentBBox.h=g.height,this.svgElement.setAttribute("height",g.height));var x=1;if(this.currentBBox.w!==g.width+x*2||this.currentBBox.h!==g.height+x*2||this.currentBBox.x!==g.x-x||this.currentBBox.y!==g.y-x){this.currentBBox.w=g.width+x*2,this.currentBBox.h=g.height+x*2,this.currentBBox.x=g.x-x,this.currentBBox.y=g.y-x,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),e=this.svgElement.style;var u="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";e.transform=u,e.webkitTransform=u}}}};function HCameraElement(e,t,r){this.initFrame(),this.initBaseData(e,t,r),this.initHierarchy();var i=PropertyFactory.getProp;if(this.pe=i(this,e.pe,0,0,this),e.ks.p.s?(this.px=i(this,e.ks.p.x,1,0,this),this.py=i(this,e.ks.p.y,1,0,this),this.pz=i(this,e.ks.p.z,1,0,this)):this.p=i(this,e.ks.p,1,0,this),e.ks.a&&(this.a=i(this,e.ks.a,1,0,this)),e.ks.or.k.length&&e.ks.or.k[0].to){var s,a=e.ks.or.k.length;for(s=0;s<a;s+=1)e.ks.or.k[s].to=null,e.ks.or.k[s].ti=null}this.or=i(this,e.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=i(this,e.ks.rx,0,degToRads,this),this.ry=i(this,e.ks.ry,0,degToRads,this),this.rz=i(this,e.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var e,t=this.comp.threeDElements.length,r,i,s;for(e=0;e<t;e+=1)if(r=this.comp.threeDElements[e],r.type==="3d"){i=r.perspectiveElem.style,s=r.container.style;var a=this.pe.v+"px",n="0px 0px 0px",p="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";i.perspective=a,i.webkitPerspective=a,s.transformOrigin=n,s.mozTransformOrigin=n,s.webkitTransformOrigin=n,i.transform=p,i.webkitTransform=p}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var e=this._isFirstFrame,t,r;if(this.hierarchy)for(r=this.hierarchy.length,t=0;t<r;t+=1)e=this.hierarchy[t].finalTransform.mProp._mdf||e;if(e||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(r=this.hierarchy.length-1,t=r;t>=0;t-=1){var i=this.hierarchy[t].finalTransform.mProp;this.mat.translate(-i.p.v[0],-i.p.v[1],i.p.v[2]),this.mat.rotateX(-i.or.v[0]).rotateY(-i.or.v[1]).rotateZ(i.or.v[2]),this.mat.rotateX(-i.rx.v).rotateY(-i.ry.v).rotateZ(i.rz.v),this.mat.scale(1/i.s.v[0],1/i.s.v[1],1/i.s.v[2]),this.mat.translate(i.a.v[0],i.a.v[1],i.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var s;this.p?s=[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:s=[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]];var a=Math.sqrt(Math.pow(s[0],2)+Math.pow(s[1],2)+Math.pow(s[2],2)),n=[s[0]/a,s[1]/a,s[2]/a],p=Math.sqrt(n[2]*n[2]+n[0]*n[0]),c=Math.atan2(n[1],p),m=Math.atan2(n[0],-n[2]);this.mat.rotateY(m).rotateX(-c)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var g=!this._prevMat.equals(this.mat);if((g||this.pe._mdf)&&this.comp.threeDElements){r=this.comp.threeDElements.length;var x,u,P;for(t=0;t<r;t+=1)if(x=this.comp.threeDElements[t],x.type==="3d"){if(g){var v=this.mat.toCSS();P=x.container.style,P.transform=v,P.webkitTransform=v}this.pe._mdf&&(u=x.perspectiveElem.style,u.perspective=this.pe.v+"px",u.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(e){this.prepareProperties(e,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null};function HImageElement(e,t,r){this.assetData=t.getAssetData(e.refId),this.initElement(e,t,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var e=this.globalData.getAssetsPath(this.assetData),t=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",e),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(t),t.crossOrigin="anonymous",t.src=e,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)};function HybridRendererBase(e,t){this.animationItem=e,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:t&&t.className||"",imagePreserveAspectRatio:t&&t.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(t&&t.hideOnTransparent===!1),filterSize:{width:t&&t.filterSize&&t.filterSize.width||"400%",height:t&&t.filterSize&&t.filterSize.height||"400%",x:t&&t.filterSize&&t.filterSize.x||"-100%",y:t&&t.filterSize&&t.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var e=this.pendingElements.pop();e.checkParenting()}},HybridRendererBase.prototype.appendElementInPos=function(e,t){var r=e.getBaseElement();if(!!r){var i=this.layers[t];if(!i.ddd||!this.supports3d)if(this.threeDElements)this.addTo3dContainer(r,t);else{for(var s=0,a,n,p;s<t;)this.elements[s]&&this.elements[s]!==!0&&this.elements[s].getBaseElement&&(n=this.elements[s],p=this.layers[s].ddd?this.getThreeDContainerByPos(s):n.getBaseElement(),a=p||a),s+=1;a?(!i.ddd||!this.supports3d)&&this.layerElement.insertBefore(r,a):(!i.ddd||!this.supports3d)&&this.layerElement.appendChild(r)}else this.addTo3dContainer(r,t)}},HybridRendererBase.prototype.createShape=function(e){return this.supports3d?new HShapeElement(e,this.globalData,this):new SVGShapeElement(e,this.globalData,this)},HybridRendererBase.prototype.createText=function(e){return this.supports3d?new HTextElement(e,this.globalData,this):new SVGTextLottieElement(e,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(e){return this.camera=new HCameraElement(e,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(e){return this.supports3d?new HImageElement(e,this.globalData,this):new IImageElement(e,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(e){return this.supports3d?new HSolidElement(e,this.globalData,this):new ISolidElement(e,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(e){for(var t=0,r=this.threeDElements.length;t<r;){if(this.threeDElements[t].startPos<=e&&this.threeDElements[t].endPos>=e)return this.threeDElements[t].perspectiveElem;t+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(e,t){var r=createTag("div"),i,s;styleDiv(r);var a=createTag("div");if(styleDiv(a),t==="3d"){i=r.style,i.width=this.globalData.compSize.w+"px",i.height=this.globalData.compSize.h+"px";var n="50% 50%";i.webkitTransformOrigin=n,i.mozTransformOrigin=n,i.transformOrigin=n,s=a.style;var p="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";s.transform=p,s.webkitTransform=p}r.appendChild(a);var c={container:a,perspectiveElem:r,startPos:e,endPos:e,type:t};return this.threeDElements.push(c),c},HybridRendererBase.prototype.build3dContainers=function(){var e,t=this.layers.length,r,i="";for(e=0;e<t;e+=1)this.layers[e].ddd&&this.layers[e].ty!==3?(i!=="3d"&&(i="3d",r=this.createThreeDContainer(e,"3d")),r.endPos=Math.max(r.endPos,e)):(i!=="2d"&&(i="2d",r=this.createThreeDContainer(e,"2d")),r.endPos=Math.max(r.endPos,e));for(t=this.threeDElements.length,e=t-1;e>=0;e-=1)this.resizerElem.appendChild(this.threeDElements[e].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(e,t){for(var r=0,i=this.threeDElements.length;r<i;){if(t<=this.threeDElements[r].endPos){for(var s=this.threeDElements[r].startPos,a;s<t;)this.elements[s]&&this.elements[s].getBaseElement&&(a=this.elements[s].getBaseElement()),s+=1;a?this.threeDElements[r].container.insertBefore(e,a):this.threeDElements[r].container.appendChild(e);break}r+=1}},HybridRendererBase.prototype.configAnimation=function(e){var t=createTag("div"),r=this.animationItem.wrapper,i=t.style;i.width=e.w+"px",i.height=e.h+"px",this.resizerElem=t,styleDiv(t),i.transformStyle="flat",i.mozTransformStyle="flat",i.webkitTransformStyle="flat",this.renderConfig.className&&t.setAttribute("class",this.renderConfig.className),r.appendChild(t),i.overflow="hidden";var s=createNS("svg");s.setAttribute("width","1"),s.setAttribute("height","1"),styleDiv(s),this.resizerElem.appendChild(s);var a=createNS("defs");s.appendChild(a),this.data=e,this.setupGlobalData(e,s),this.globalData.defs=a,this.layers=e.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var e,t=this.layers?this.layers.length:0;for(e=0;e<t;e+=1)this.elements[e].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var e=this.animationItem.wrapper.offsetWidth,t=this.animationItem.wrapper.offsetHeight,r=e/t,i=this.globalData.compSize.w/this.globalData.compSize.h,s,a,n,p;i>r?(s=e/this.globalData.compSize.w,a=e/this.globalData.compSize.w,n=0,p=(t-this.globalData.compSize.h*(e/this.globalData.compSize.w))/2):(s=t/this.globalData.compSize.h,a=t/this.globalData.compSize.h,n=(e-this.globalData.compSize.w*(t/this.globalData.compSize.h))/2,p=0);var c=this.resizerElem.style;c.webkitTransform="matrix3d("+s+",0,0,0,0,"+a+",0,0,0,0,1,0,"+n+","+p+",0,1)",c.transform=c.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var e=this.globalData.compSize.w,t=this.globalData.compSize.h,r,i=this.threeDElements.length;for(r=0;r<i;r+=1){var s=this.threeDElements[r].perspectiveElem.style;s.webkitPerspective=Math.sqrt(Math.pow(e,2)+Math.pow(t,2))+"px",s.perspective=s.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(e){var t,r=e.length,i=createTag("div");for(t=0;t<r;t+=1)if(e[t].xt){var s=this.createComp(e[t],i,this.globalData.comp,null);s.initExpressions(),this.globalData.projectInterface.registerComposition(s)}};function HCompElement(e,t,r){this.layers=e.layers,this.supports3d=!e.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(e,t,r),this.tm=e.tm?PropertyFactory.getProp(this,e.tm,0,t.frameRate,this):{_placeholder:!0}}extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(e,t){for(var r=0,i;r<t;)this.elements[r]&&this.elements[r].getBaseElement&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(e,i):this.layerElement.appendChild(e)},HCompElement.prototype.createComp=function(e){return this.supports3d?new HCompElement(e,this.globalData,this):new SVGCompElement(e,this.globalData,this)};function HybridRenderer(e,t){this.animationItem=e,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:t&&t.className||"",imagePreserveAspectRatio:t&&t.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(t&&t.hideOnTransparent===!1),filterSize:{width:t&&t.filterSize&&t.filterSize.width||"400%",height:t&&t.filterSize&&t.filterSize.height||"400%",x:t&&t.filterSize&&t.filterSize.x||"-100%",y:t&&t.filterSize&&t.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(e){return this.supports3d?new HCompElement(e,this.globalData,this):new SVGCompElement(e,this.globalData,this)};var Expressions=function(){var e={};e.initExpressions=t;function t(r){var i=0,s=[];function a(){i+=1}function n(){i-=1,i===0&&c()}function p(m){s.indexOf(m)===-1&&s.push(m)}function c(){var m,g=s.length;for(m=0;m<g;m+=1)s[m].release();s.length=0}r.renderer.compInterface=CompExpressionInterface(r.renderer),r.renderer.globalData.projectInterface.registerComposition(r.renderer),r.renderer.globalData.pushExpression=a,r.renderer.globalData.popExpression=n,r.renderer.globalData.registerExpressionProperty=p}return e}();function _typeof$1(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$1=function(r){return typeof r}:_typeof$1=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$1(e)}function seedRandom(e,t){var r=this,i=256,s=6,a=52,n="random",p=t.pow(i,s),c=t.pow(2,a),m=c*2,g=i-1,x;function u(l,h,y){var b=[];h=h===!0?{entropy:!0}:h||{};var _=S(d(h.entropy?[l,f(e)]:l===null?o():l,3),b),C=new P(b),F=function(){for(var R=C.g(s),I=p,D=0;R<c;)R=(R+D)*i,I*=i,D=C.g(1);for(;R>=m;)R/=2,I/=2,D>>>=1;return(R+D)/I};return F.int32=function(){return C.g(4)|0},F.quick=function(){return C.g(4)/4294967296},F.double=F,S(f(C.S),e),(h.pass||y||function(L,R,I,D){return D&&(D.S&&v(D,C),L.state=function(){return v(C,{})}),I?(t[n]=L,R):L})(F,_,"global"in h?h.global:this==t,h.state)}t["seed"+n]=u;function P(l){var h,y=l.length,b=this,_=0,C=b.i=b.j=0,F=b.S=[];for(y||(l=[y++]);_<i;)F[_]=_++;for(_=0;_<i;_++)F[_]=F[C=g&C+l[_%y]+(h=F[_])],F[C]=h;b.g=function(L){for(var R,I=0,D=b.i,B=b.j,w=b.S;L--;)R=w[D=g&D+1],I=I*i+w[g&(w[D]=w[B=g&B+R])+(w[B]=R)];return b.i=D,b.j=B,I}}function v(l,h){return h.i=l.i,h.j=l.j,h.S=l.S.slice(),h}function d(l,h){var y=[],b=_typeof$1(l),_;if(h&&b=="object")for(_ in l)try{y.push(d(l[_],h-1))}catch{}return y.length?y:b=="string"?l:l+"\0"}function S(l,h){for(var y=l+"",b,_=0;_<y.length;)h[g&_]=g&(b^=h[g&_]*19)+y.charCodeAt(_++);return f(h)}function o(){try{if(x)return f(x.randomBytes(i));var l=new Uint8Array(i);return(r.crypto||r.msCrypto).getRandomValues(l),f(l)}catch{var h=r.navigator,y=h&&h.plugins;return[+new Date,r,y,r.screen,f(e)]}}function f(l){return String.fromCharCode.apply(0,l)}S(t.random(),e)}function initialize$2(e){seedRandom([],e)}var propTypes={SHAPE:"shape"};function _typeof(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof=function(r){return typeof r}:_typeof=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof(e)}var ExpressionManager=function(){"use strict";var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null;initialize$2(BMMath);function $bm_isInstanceOfArray(e){return e.constructor===Array||e.constructor===Float32Array}function isNumerable(e,t){return e==="number"||e==="boolean"||e==="string"||t instanceof Number}function $bm_neg(e){var t=_typeof(e);if(t==="number"||t==="boolean"||e instanceof Number)return-e;if($bm_isInstanceOfArray(e)){var r,i=e.length,s=[];for(r=0;r<i;r+=1)s[r]=-e[r];return s}return e.propType?e.v:-e}var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(e,t){var r=_typeof(e),i=_typeof(t);if(r==="string"||i==="string"||isNumerable(r,e)&&isNumerable(i,t))return e+t;if($bm_isInstanceOfArray(e)&&isNumerable(i,t))return e=e.slice(0),e[0]+=t,e;if(isNumerable(r,e)&&$bm_isInstanceOfArray(t))return t=t.slice(0),t[0]=e+t[0],t;if($bm_isInstanceOfArray(e)&&$bm_isInstanceOfArray(t)){for(var s=0,a=e.length,n=t.length,p=[];s<a||s<n;)(typeof e[s]=="number"||e[s]instanceof Number)&&(typeof t[s]=="number"||t[s]instanceof Number)?p[s]=e[s]+t[s]:p[s]=t[s]===void 0?e[s]:e[s]||t[s],s+=1;return p}return 0}var add=sum;function sub(e,t){var r=_typeof(e),i=_typeof(t);if(isNumerable(r,e)&&isNumerable(i,t))return r==="string"&&(e=parseInt(e,10)),i==="string"&&(t=parseInt(t,10)),e-t;if($bm_isInstanceOfArray(e)&&isNumerable(i,t))return e=e.slice(0),e[0]-=t,e;if(isNumerable(r,e)&&$bm_isInstanceOfArray(t))return t=t.slice(0),t[0]=e-t[0],t;if($bm_isInstanceOfArray(e)&&$bm_isInstanceOfArray(t)){for(var s=0,a=e.length,n=t.length,p=[];s<a||s<n;)(typeof e[s]=="number"||e[s]instanceof Number)&&(typeof t[s]=="number"||t[s]instanceof Number)?p[s]=e[s]-t[s]:p[s]=t[s]===void 0?e[s]:e[s]||t[s],s+=1;return p}return 0}function mul(e,t){var r=_typeof(e),i=_typeof(t),s;if(isNumerable(r,e)&&isNumerable(i,t))return e*t;var a,n;if($bm_isInstanceOfArray(e)&&isNumerable(i,t)){for(n=e.length,s=createTypedArray("float32",n),a=0;a<n;a+=1)s[a]=e[a]*t;return s}if(isNumerable(r,e)&&$bm_isInstanceOfArray(t)){for(n=t.length,s=createTypedArray("float32",n),a=0;a<n;a+=1)s[a]=e*t[a];return s}return 0}function div(e,t){var r=_typeof(e),i=_typeof(t),s;if(isNumerable(r,e)&&isNumerable(i,t))return e/t;var a,n;if($bm_isInstanceOfArray(e)&&isNumerable(i,t)){for(n=e.length,s=createTypedArray("float32",n),a=0;a<n;a+=1)s[a]=e[a]/t;return s}if(isNumerable(r,e)&&$bm_isInstanceOfArray(t)){for(n=t.length,s=createTypedArray("float32",n),a=0;a<n;a+=1)s[a]=e/t[a];return s}return 0}function mod(e,t){return typeof e=="string"&&(e=parseInt(e,10)),typeof t=="string"&&(t=parseInt(t,10)),e%t}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(e,t,r){if(t>r){var i=r;r=t,t=i}return Math.min(Math.max(e,t),r)}function radiansToDegrees(e){return e/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(e){return e*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(e,t){if(typeof e=="number"||e instanceof Number)return t=t||0,Math.abs(e-t);t||(t=helperLengthArray);var r,i=Math.min(e.length,t.length),s=0;for(r=0;r<i;r+=1)s+=Math.pow(t[r]-e[r],2);return Math.sqrt(s)}function normalize(e){return div(e,length(e))}function rgbToHsl(e){var t=e[0],r=e[1],i=e[2],s=Math.max(t,r,i),a=Math.min(t,r,i),n,p,c=(s+a)/2;if(s===a)n=0,p=0;else{var m=s-a;switch(p=c>.5?m/(2-s-a):m/(s+a),s){case t:n=(r-i)/m+(r<i?6:0);break;case r:n=(i-t)/m+2;break;case i:n=(t-r)/m+4;break;default:break}n/=6}return[n,p,c,e[3]]}function hue2rgb(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function hslToRgb(e){var t=e[0],r=e[1],i=e[2],s,a,n;if(r===0)s=i,n=i,a=i;else{var p=i<.5?i*(1+r):i+r-i*r,c=2*i-p;s=hue2rgb(c,p,t+1/3),a=hue2rgb(c,p,t),n=hue2rgb(c,p,t-1/3)}return[s,a,n,e[3]]}function linear(e,t,r,i,s){if((i===void 0||s===void 0)&&(i=t,s=r,t=0,r=1),r<t){var a=r;r=t,t=a}if(e<=t)return i;if(e>=r)return s;var n=r===t?0:(e-t)/(r-t);if(!i.length)return i+(s-i)*n;var p,c=i.length,m=createTypedArray("float32",c);for(p=0;p<c;p+=1)m[p]=i[p]+(s[p]-i[p])*n;return m}function random(e,t){if(t===void 0&&(e===void 0?(e=0,t=1):(t=e,e=void 0)),t.length){var r,i=t.length;e||(e=createTypedArray("float32",i));var s=createTypedArray("float32",i),a=BMMath.random();for(r=0;r<i;r+=1)s[r]=e[r]+a*(t[r]-e[r]);return s}e===void 0&&(e=0);var n=BMMath.random();return e+n*(t-e)}function createPath(e,t,r,i){var s,a=e.length,n=shapePool.newElement();n.setPathData(!!i,a);var p=[0,0],c,m;for(s=0;s<a;s+=1)c=t&&t[s]?t[s]:p,m=r&&r[s]?r[s]:p,n.setTripleAt(e[s][0],e[s][1],m[0]+e[s][0],m[1]+e[s][1],c[0]+e[s][0],c[1]+e[s][1],s,!0);return n}function initiateExpression(elem,data,property){var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=val.indexOf("random")!==-1,elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||this.data.hd!==!0,wiggle=function e(t,r){var i,s,a=this.pv.length?this.pv.length:1,n=createTypedArray("float32",a);t=5;var p=Math.floor(time*t);for(i=0,s=0;i<p;){for(s=0;s<a;s+=1)n[s]+=-r+r*2*BMMath.random();i+=1}var c=time*t,m=c-Math.floor(c),g=createTypedArray("float32",a);if(a>1){for(s=0;s<a;s+=1)g[s]=this.pv[s]+n[s]+(-r+r*2*BMMath.random())*m;return g}return this.pv+n[0]+(-r+r*2*BMMath.random())*m}.bind(this);thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty));function loopInDuration(e,t){return loopIn(e,t,!0)}function loopOutDuration(e,t){return loopOut(e,t,!0)}this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface);function lookAt(e,t){var r=[t[0]-e[0],t[1]-e[1],t[2]-e[2]],i=Math.atan2(r[0],Math.sqrt(r[1]*r[1]+r[2]*r[2]))/degToRads,s=-Math.atan2(r[1],r[2])/degToRads;return[s,i,0]}function easeOut(e,t,r,i,s){return applyEase(easeOutBez,e,t,r,i,s)}function easeIn(e,t,r,i,s){return applyEase(easeInBez,e,t,r,i,s)}function ease(e,t,r,i,s){return applyEase(easeInOutBez,e,t,r,i,s)}function applyEase(e,t,r,i,s,a){s===void 0?(s=r,a=i):t=(t-r)/(i-r),t>1?t=1:t<0&&(t=0);var n=e(t);if($bm_isInstanceOfArray(s)){var p,c=s.length,m=createTypedArray("float32",c);for(p=0;p<c;p+=1)m[p]=(a[p]-s[p])*n+s[p];return m}return(a-s)*n+s}function nearestKey(e){var t,r=data.k.length,i,s;if(!data.k.length||typeof data.k[0]=="number")i=0,s=0;else if(i=-1,e*=elem.comp.globalData.frameRate,e<data.k[0].t)i=1,s=data.k[0].t;else{for(t=0;t<r-1;t+=1)if(e===data.k[t].t){i=t+1,s=data.k[t].t;break}else if(e>data.k[t].t&&e<data.k[t+1].t){e-data.k[t].t>data.k[t+1].t-e?(i=t+2,s=data.k[t+1].t):(i=t+1,s=data.k[t].t);break}i===-1&&(i=t+1,s=data.k[t].t)}var a={};return a.index=i,a.time=s/elem.comp.globalData.frameRate,a}function key(e){var t,r,i;if(!data.k.length||typeof data.k[0]=="number")throw new Error("The property has no keyframe at index "+e);e-=1,t={time:data.k[e].t/elem.comp.globalData.frameRate,value:[]};var s=Object.prototype.hasOwnProperty.call(data.k[e],"s")?data.k[e].s:data.k[e-1].e;for(i=s.length,r=0;r<i;r+=1)t[r]=s[r],t.value[r]=s[r];return t}function framesToTime(e,t){return t||(t=elem.comp.globalData.frameRate),e/t}function timeToFrames(e,t){return!e&&e!==0&&(e=time),t||(t=elem.comp.globalData.frameRate),e*t}function seedRandom(e){BMMath.seedrandom(randSeed+e)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(e,t){return typeof value=="string"?t===void 0?value.substring(e):value.substring(e,t):""}function substr(e,t){return typeof value=="string"?t===void 0?value.substr(e):value.substr(e,t):""}function posterizeTime(e){time=e===0?0:Math.floor(time*e)/e,value=valueAtTime(time)}var time,velocity,value,text,textIndex,textTotal,selectorValue,index=elem.data.ind,hasParent=!!(elem.hierarchy&&elem.hierarchy.length),parent,randSeed=Math.floor(Math.random()*1e6),globalData=elem.globalData;function executeExpression(e){return value=e,this.frameExpressionId===elem.globalData.frameId&&this.propType!=="textSelector"?value:(this.propType==="textSelector"&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform,transform&&(anchorPoint=transform.anchorPoint)),elemType===4&&!content&&(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),hasParent=!!(elem.hierarchy&&elem.hierarchy.length),hasParent&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt,scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath],ob}(),expressionHelpers=function(){function e(n,p,c){p.x&&(c.k=!0,c.x=!0,c.initiateExpression=ExpressionManager.initiateExpression,c.effectsSequence.push(c.initiateExpression(n,p,c).bind(c)))}function t(n){return n*=this.elem.globalData.frameRate,n-=this.offsetTime,n!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<n?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(n,this._cachingAtTime),this._cachingAtTime.lastFrame=n),this._cachingAtTime.value}function r(n){var p=-.01,c=this.getValueAtTime(n),m=this.getValueAtTime(n+p),g=0;if(c.length){var x;for(x=0;x<c.length;x+=1)g+=Math.pow(m[x]-c[x],2);g=Math.sqrt(g)*100}else g=0;return g}function i(n){if(this.vel!==void 0)return this.vel;var p=-.001,c=this.getValueAtTime(n),m=this.getValueAtTime(n+p),g;if(c.length){g=createTypedArray("float32",c.length);var x;for(x=0;x<c.length;x+=1)g[x]=(m[x]-c[x])/p}else g=(m-c)/p;return g}function s(){return this.pv}function a(n){this.propertyGroup=n}return{searchExpressions:e,getSpeedAtTime:r,getVelocityAtTime:i,getValueAtTime:t,getStaticValueAtTime:s,setGroupProperty:a}}();function addPropertyDecorator(){function e(u,P,v){if(!this.k||!this.keyframes)return this.pv;u=u?u.toLowerCase():"";var d=this.comp.renderedFrame,S=this.keyframes,o=S[S.length-1].t;if(d<=o)return this.pv;var f,l;v?(P?f=Math.abs(o-this.elem.comp.globalData.frameRate*P):f=Math.max(0,o-this.elem.data.ip),l=o-f):((!P||P>S.length-1)&&(P=S.length-1),l=S[S.length-1-P].t,f=o-l);var h,y,b;if(u==="pingpong"){var _=Math.floor((d-l)/f);if(_%2!==0)return this.getValueAtTime((f-(d-l)%f+l)/this.comp.globalData.frameRate,0)}else if(u==="offset"){var C=this.getValueAtTime(l/this.comp.globalData.frameRate,0),F=this.getValueAtTime(o/this.comp.globalData.frameRate,0),L=this.getValueAtTime(((d-l)%f+l)/this.comp.globalData.frameRate,0),R=Math.floor((d-l)/f);if(this.pv.length){for(b=new Array(C.length),y=b.length,h=0;h<y;h+=1)b[h]=(F[h]-C[h])*R+L[h];return b}return(F-C)*R+L}else if(u==="continue"){var I=this.getValueAtTime(o/this.comp.globalData.frameRate,0),D=this.getValueAtTime((o-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(b=new Array(I.length),y=b.length,h=0;h<y;h+=1)b[h]=I[h]+(I[h]-D[h])*((d-o)/this.comp.globalData.frameRate)/5e-4;return b}return I+(I-D)*((d-o)/.001)}return this.getValueAtTime(((d-l)%f+l)/this.comp.globalData.frameRate,0)}function t(u,P,v){if(!this.k)return this.pv;u=u?u.toLowerCase():"";var d=this.comp.renderedFrame,S=this.keyframes,o=S[0].t;if(d>=o)return this.pv;var f,l;v?(P?f=Math.abs(this.elem.comp.globalData.frameRate*P):f=Math.max(0,this.elem.data.op-o),l=o+f):((!P||P>S.length-1)&&(P=S.length-1),l=S[P].t,f=l-o);var h,y,b;if(u==="pingpong"){var _=Math.floor((o-d)/f);if(_%2===0)return this.getValueAtTime(((o-d)%f+o)/this.comp.globalData.frameRate,0)}else if(u==="offset"){var C=this.getValueAtTime(o/this.comp.globalData.frameRate,0),F=this.getValueAtTime(l/this.comp.globalData.frameRate,0),L=this.getValueAtTime((f-(o-d)%f+o)/this.comp.globalData.frameRate,0),R=Math.floor((o-d)/f)+1;if(this.pv.length){for(b=new Array(C.length),y=b.length,h=0;h<y;h+=1)b[h]=L[h]-(F[h]-C[h])*R;return b}return L-(F-C)*R}else if(u==="continue"){var I=this.getValueAtTime(o/this.comp.globalData.frameRate,0),D=this.getValueAtTime((o+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(b=new Array(I.length),y=b.length,h=0;h<y;h+=1)b[h]=I[h]+(I[h]-D[h])*(o-d)/.001;return b}return I+(I-D)*(o-d)/.001}return this.getValueAtTime((f-((o-d)%f+o))/this.comp.globalData.frameRate,0)}function r(u,P){if(!this.k)return this.pv;if(u=(u||.4)*.5,P=Math.floor(P||5),P<=1)return this.pv;var v=this.comp.renderedFrame/this.comp.globalData.frameRate,d=v-u,S=v+u,o=P>1?(S-d)/(P-1):1,f=0,l=0,h;this.pv.length?h=createTypedArray("float32",this.pv.length):h=0;for(var y;f<P;){if(y=this.getValueAtTime(d+f*o),this.pv.length)for(l=0;l<this.pv.length;l+=1)h[l]+=y[l];else h+=y;f+=1}if(this.pv.length)for(l=0;l<this.pv.length;l+=1)h[l]/=P;else h/=P;return h}function i(u){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var P=this._transformCachingAtTime.v;if(P.cloneFromProps(this.pre.props),this.appliedTransformations<1){var v=this.a.getValueAtTime(u);P.translate(-v[0]*this.a.mult,-v[1]*this.a.mult,v[2]*this.a.mult)}if(this.appliedTransformations<2){var d=this.s.getValueAtTime(u);P.scale(d[0]*this.s.mult,d[1]*this.s.mult,d[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var S=this.sk.getValueAtTime(u),o=this.sa.getValueAtTime(u);P.skewFromAxis(-S*this.sk.mult,o*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var f=this.r.getValueAtTime(u);P.rotate(-f*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var l=this.rz.getValueAtTime(u),h=this.ry.getValueAtTime(u),y=this.rx.getValueAtTime(u),b=this.or.getValueAtTime(u);P.rotateZ(-l*this.rz.mult).rotateY(h*this.ry.mult).rotateX(y*this.rx.mult).rotateZ(-b[2]*this.or.mult).rotateY(b[1]*this.or.mult).rotateX(b[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var _=this.px.getValueAtTime(u),C=this.py.getValueAtTime(u);if(this.data.p.z){var F=this.pz.getValueAtTime(u);P.translate(_*this.px.mult,C*this.py.mult,-F*this.pz.mult)}else P.translate(_*this.px.mult,C*this.py.mult,0)}else{var L=this.p.getValueAtTime(u);P.translate(L[0]*this.p.mult,L[1]*this.p.mult,-L[2]*this.p.mult)}return P}function s(){return this.v.clone(new Matrix)}var a=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(u,P,v){var d=a(u,P,v);return d.dynamicProperties.length?d.getValueAtTime=i.bind(d):d.getValueAtTime=s.bind(d),d.setGroupProperty=expressionHelpers.setGroupProperty,d};var n=PropertyFactory.getProp;PropertyFactory.getProp=function(u,P,v,d,S){var o=n(u,P,v,d,S);o.kf?o.getValueAtTime=expressionHelpers.getValueAtTime.bind(o):o.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(o),o.setGroupProperty=expressionHelpers.setGroupProperty,o.loopOut=e,o.loopIn=t,o.smooth=r,o.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(o),o.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(o),o.numKeys=P.a===1?P.k.length:0,o.propertyIndex=P.ix;var f=0;return v!==0&&(f=createTypedArray("float32",P.a===1?P.k[0].s.length:P.k.length)),o._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:f},expressionHelpers.searchExpressions(u,P,o),o.k&&S.addDynamicProperty(o),o};function p(u){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),u*=this.elem.globalData.frameRate,u-=this.offsetTime,u!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<u?this._caching.lastIndex:0,this._cachingAtTime.lastTime=u,this.interpolateShape(u,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue}var c=ShapePropertyFactory.getConstructorFunction(),m=ShapePropertyFactory.getKeyframedConstructorFunction();function g(){}g.prototype={vertices:function(P,v){this.k&&this.getValue();var d=this.v;v!==void 0&&(d=this.getValueAtTime(v,0));var S,o=d._length,f=d[P],l=d.v,h=createSizedArray(o);for(S=0;S<o;S+=1)P==="i"||P==="o"?h[S]=[f[S][0]-l[S][0],f[S][1]-l[S][1]]:h[S]=[f[S][0],f[S][1]];return h},points:function(P){return this.vertices("v",P)},inTangents:function(P){return this.vertices("i",P)},outTangents:function(P){return this.vertices("o",P)},isClosed:function(){return this.v.c},pointOnPath:function(P,v){var d=this.v;v!==void 0&&(d=this.getValueAtTime(v,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(d));for(var S=this._segmentsLength,o=S.lengths,f=S.totalLength*P,l=0,h=o.length,y=0,b;l<h;){if(y+o[l].addedLength>f){var _=l,C=d.c&&l===h-1?0:l+1,F=(f-y)/o[l].addedLength;b=bez.getPointInSegment(d.v[_],d.v[C],d.o[_],d.i[C],F,o[l]);break}else y+=o[l].addedLength;l+=1}return b||(b=d.c?[d.v[0][0],d.v[0][1]]:[d.v[d._length-1][0],d.v[d._length-1][1]]),b},vectorOnPath:function(P,v,d){P==1?P=this.v.c:P==0&&(P=.999);var S=this.pointOnPath(P,v),o=this.pointOnPath(P+.001,v),f=o[0]-S[0],l=o[1]-S[1],h=Math.sqrt(Math.pow(f,2)+Math.pow(l,2));if(h===0)return[0,0];var y=d==="tangent"?[f/h,l/h]:[-l/h,f/h];return y},tangentOnPath:function(P,v){return this.vectorOnPath(P,v,"tangent")},normalOnPath:function(P,v){return this.vectorOnPath(P,v,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([g],c),extendPrototype([g],m),m.prototype.getValueAtTime=p,m.prototype.initiateExpression=ExpressionManager.initiateExpression;var x=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(u,P,v,d,S){var o=x(u,P,v,d,S);return o.propertyIndex=P.ix,o.lock=!1,v===3?expressionHelpers.searchExpressions(u,P.pt,o):v===4&&expressionHelpers.searchExpressions(u,P.ks,o),o.k&&u.addDynamicProperty(o),o}}function initialize$1(){addPropertyDecorator()}function addDecorator(){function e(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}TextProperty.prototype.getExpressionValue=function(t,r){var i=this.calculateExpression(r);if(t.t!==i){var s={};return this.copyData(s,t),s.t=i.toString(),s.__complete=!1,s}return t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),r=this.searchExpressions();return this.kf=t||r,this.kf},TextProperty.prototype.searchExpressions=e}function initialize(){addDecorator()}function SVGComposableEffect(){}SVGComposableEffect.prototype={createMergeNode:function e(t,r){var i=createNS("feMerge");i.setAttribute("result",t);var s,a;for(a=0;a<r.length;a+=1)s=createNS("feMergeNode"),s.setAttribute("in",r[a]),i.appendChild(s),i.appendChild(s);return i}};function SVGTintFilter(e,t,r,i,s){this.filterManager=t;var a=createNS("feColorMatrix");a.setAttribute("type","matrix"),a.setAttribute("color-interpolation-filters","linearRGB"),a.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),a.setAttribute("result",i+"_tint_1"),e.appendChild(a),a=createNS("feColorMatrix"),a.setAttribute("type","matrix"),a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),a.setAttribute("result",i+"_tint_2"),e.appendChild(a),this.matrixFilter=a;var n=this.createMergeNode(i,[s,i+"_tint_1",i+"_tint_2"]);e.appendChild(n)}extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){var t=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v/100;this.matrixFilter.setAttribute("values",r[0]-t[0]+" 0 0 0 "+t[0]+" "+(r[1]-t[1])+" 0 0 0 "+t[1]+" "+(r[2]-t[2])+" 0 0 0 "+t[2]+" 0 0 0 "+i+" 0")}};function SVGFillFilter(e,t,r,i){this.filterManager=t;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),s.setAttribute("result",i),e.appendChild(s),this.matrixFilter=s}SVGFillFilter.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){var t=this.filterManager.effectElements[2].p.v,r=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+t[0]+" 0 0 0 0 "+t[1]+" 0 0 0 0 "+t[2]+" 0 0 0 "+r+" 0")}};function SVGStrokeEffect(e,t,r){this.initialized=!1,this.filterManager=t,this.elem=r,this.paths=[]}SVGStrokeEffect.prototype.initialize=function(){var e=this.elem.layerElement.children||this.elem.layerElement.childNodes,t,r,i,s;for(this.filterManager.effectElements[1].p.v===1?(s=this.elem.maskManager.masksProperties.length,i=0):(i=this.filterManager.effectElements[0].p.v-1,s=i+1),r=createNS("g"),r.setAttribute("fill","none"),r.setAttribute("stroke-linecap","round"),r.setAttribute("stroke-dashoffset",1),i;i<s;i+=1)t=createNS("path"),r.appendChild(t),this.paths.push({p:t,m:i});if(this.filterManager.effectElements[10].p.v===3){var a=createNS("mask"),n=createElementID();a.setAttribute("id",n),a.setAttribute("mask-type","alpha"),a.appendChild(r),this.elem.globalData.defs.appendChild(a);var p=createNS("g");for(p.setAttribute("mask","url("+getLocationHref()+"#"+n+")");e[0];)p.appendChild(e[0]);this.elem.layerElement.appendChild(p),this.masker=a,r.setAttribute("stroke","#fff")}else if(this.filterManager.effectElements[10].p.v===1||this.filterManager.effectElements[10].p.v===2){if(this.filterManager.effectElements[10].p.v===2)for(e=this.elem.layerElement.children||this.elem.layerElement.childNodes;e.length;)this.elem.layerElement.removeChild(e[0]);this.elem.layerElement.appendChild(r),this.elem.layerElement.removeAttribute("mask"),r.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=r},SVGStrokeEffect.prototype.renderFrame=function(e){this.initialized||this.initialize();var t,r=this.paths.length,i,s;for(t=0;t<r;t+=1)if(this.paths[t].m!==-1&&(i=this.elem.maskManager.viewData[this.paths[t].m],s=this.paths[t].p,(e||this.filterManager._mdf||i.prop._mdf)&&s.setAttribute("d",i.lastPath),e||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||i.prop._mdf)){var a;if(this.filterManager.effectElements[7].p.v!==0||this.filterManager.effectElements[8].p.v!==100){var n=Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v)*.01,p=Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v)*.01,c=s.getTotalLength();a="0 0 0 "+c*n+" ";var m=c*(p-n),g=1+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01,x=Math.floor(m/g),u;for(u=0;u<x;u+=1)a+="1 "+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01+" ";a+="0 "+c*10+" 0 0"}else a="1 "+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01;s.setAttribute("stroke-dasharray",a)}if((e||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",this.filterManager.effectElements[4].p.v*2),(e||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(this.filterManager.effectElements[10].p.v===1||this.filterManager.effectElements[10].p.v===2)&&(e||this.filterManager.effectElements[3].p._mdf)){var P=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(P[0]*255)+","+bmFloor(P[1]*255)+","+bmFloor(P[2]*255)+")")}};function SVGTritoneFilter(e,t,r,i){this.filterManager=t;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","linearRGB"),s.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),e.appendChild(s);var a=createNS("feComponentTransfer");a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("result",i),this.matrixFilter=a;var n=createNS("feFuncR");n.setAttribute("type","table"),a.appendChild(n),this.feFuncR=n;var p=createNS("feFuncG");p.setAttribute("type","table"),a.appendChild(p),this.feFuncG=p;var c=createNS("feFuncB");c.setAttribute("type","table"),a.appendChild(c),this.feFuncB=c,e.appendChild(a)}SVGTritoneFilter.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){var t=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v,s=i[0]+" "+r[0]+" "+t[0],a=i[1]+" "+r[1]+" "+t[1],n=i[2]+" "+r[2]+" "+t[2];this.feFuncR.setAttribute("tableValues",s),this.feFuncG.setAttribute("tableValues",a),this.feFuncB.setAttribute("tableValues",n)}};function SVGProLevelsFilter(e,t,r,i){this.filterManager=t;var s=this.filterManager.effectElements,a=createNS("feComponentTransfer");(s[10].p.k||s[10].p.v!==0||s[11].p.k||s[11].p.v!==1||s[12].p.k||s[12].p.v!==1||s[13].p.k||s[13].p.v!==0||s[14].p.k||s[14].p.v!==1)&&(this.feFuncR=this.createFeFunc("feFuncR",a)),(s[17].p.k||s[17].p.v!==0||s[18].p.k||s[18].p.v!==1||s[19].p.k||s[19].p.v!==1||s[20].p.k||s[20].p.v!==0||s[21].p.k||s[21].p.v!==1)&&(this.feFuncG=this.createFeFunc("feFuncG",a)),(s[24].p.k||s[24].p.v!==0||s[25].p.k||s[25].p.v!==1||s[26].p.k||s[26].p.v!==1||s[27].p.k||s[27].p.v!==0||s[28].p.k||s[28].p.v!==1)&&(this.feFuncB=this.createFeFunc("feFuncB",a)),(s[31].p.k||s[31].p.v!==0||s[32].p.k||s[32].p.v!==1||s[33].p.k||s[33].p.v!==1||s[34].p.k||s[34].p.v!==0||s[35].p.k||s[35].p.v!==1)&&(this.feFuncA=this.createFeFunc("feFuncA",a)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(a.setAttribute("color-interpolation-filters","sRGB"),e.appendChild(a)),(s[3].p.k||s[3].p.v!==0||s[4].p.k||s[4].p.v!==1||s[5].p.k||s[5].p.v!==1||s[6].p.k||s[6].p.v!==0||s[7].p.k||s[7].p.v!==1)&&(a=createNS("feComponentTransfer"),a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("result",i),e.appendChild(a),this.feFuncRComposed=this.createFeFunc("feFuncR",a),this.feFuncGComposed=this.createFeFunc("feFuncG",a),this.feFuncBComposed=this.createFeFunc("feFuncB",a))}SVGProLevelsFilter.prototype.createFeFunc=function(e,t){var r=createNS(e);return r.setAttribute("type","table"),t.appendChild(r),r},SVGProLevelsFilter.prototype.getTableValue=function(e,t,r,i,s){for(var a=0,n=256,p,c=Math.min(e,t),m=Math.max(e,t),g=Array.call(null,{length:n}),x,u=0,P=s-i,v=t-e;a<=256;)p=a/256,p<=c?x=v<0?s:i:p>=m?x=v<0?i:s:x=i+P*Math.pow((p-e)/v,1/r),g[u]=x,u+=1,a+=256/(n-1);return g.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){var t,r=this.filterManager.effectElements;this.feFuncRComposed&&(e||r[3].p._mdf||r[4].p._mdf||r[5].p._mdf||r[6].p._mdf||r[7].p._mdf)&&(t=this.getTableValue(r[3].p.v,r[4].p.v,r[5].p.v,r[6].p.v,r[7].p.v),this.feFuncRComposed.setAttribute("tableValues",t),this.feFuncGComposed.setAttribute("tableValues",t),this.feFuncBComposed.setAttribute("tableValues",t)),this.feFuncR&&(e||r[10].p._mdf||r[11].p._mdf||r[12].p._mdf||r[13].p._mdf||r[14].p._mdf)&&(t=this.getTableValue(r[10].p.v,r[11].p.v,r[12].p.v,r[13].p.v,r[14].p.v),this.feFuncR.setAttribute("tableValues",t)),this.feFuncG&&(e||r[17].p._mdf||r[18].p._mdf||r[19].p._mdf||r[20].p._mdf||r[21].p._mdf)&&(t=this.getTableValue(r[17].p.v,r[18].p.v,r[19].p.v,r[20].p.v,r[21].p.v),this.feFuncG.setAttribute("tableValues",t)),this.feFuncB&&(e||r[24].p._mdf||r[25].p._mdf||r[26].p._mdf||r[27].p._mdf||r[28].p._mdf)&&(t=this.getTableValue(r[24].p.v,r[25].p.v,r[26].p.v,r[27].p.v,r[28].p.v),this.feFuncB.setAttribute("tableValues",t)),this.feFuncA&&(e||r[31].p._mdf||r[32].p._mdf||r[33].p._mdf||r[34].p._mdf||r[35].p._mdf)&&(t=this.getTableValue(r[31].p.v,r[32].p.v,r[33].p.v,r[34].p.v,r[35].p.v),this.feFuncA.setAttribute("tableValues",t))}};function SVGDropShadowEffect(e,t,r,i,s){var a=t.container.globalData.renderConfig.filterSize,n=t.data.fs||a;e.setAttribute("x",n.x||a.x),e.setAttribute("y",n.y||a.y),e.setAttribute("width",n.width||a.width),e.setAttribute("height",n.height||a.height),this.filterManager=t;var p=createNS("feGaussianBlur");p.setAttribute("in","SourceAlpha"),p.setAttribute("result",i+"_drop_shadow_1"),p.setAttribute("stdDeviation","0"),this.feGaussianBlur=p,e.appendChild(p);var c=createNS("feOffset");c.setAttribute("dx","25"),c.setAttribute("dy","0"),c.setAttribute("in",i+"_drop_shadow_1"),c.setAttribute("result",i+"_drop_shadow_2"),this.feOffset=c,e.appendChild(c);var m=createNS("feFlood");m.setAttribute("flood-color","#00ff00"),m.setAttribute("flood-opacity","1"),m.setAttribute("result",i+"_drop_shadow_3"),this.feFlood=m,e.appendChild(m);var g=createNS("feComposite");g.setAttribute("in",i+"_drop_shadow_3"),g.setAttribute("in2",i+"_drop_shadow_2"),g.setAttribute("operator","in"),g.setAttribute("result",i+"_drop_shadow_4"),e.appendChild(g);var x=this.createMergeNode(i,[i+"_drop_shadow_4",s]);e.appendChild(x)}extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){if((e||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),e||this.filterManager.effectElements[0].p._mdf){var t=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(t[0]*255),Math.round(t[1]*255),Math.round(t[2]*255)))}if((e||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),e||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var r=this.filterManager.effectElements[3].p.v,i=(this.filterManager.effectElements[2].p.v-90)*degToRads,s=r*Math.cos(i),a=r*Math.sin(i);this.feOffset.setAttribute("dx",s),this.feOffset.setAttribute("dy",a)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(e,t,r){this.initialized=!1,this.filterManager=t,this.filterElem=e,this.elem=r,r.matteElement=createNS("g"),r.matteElement.appendChild(r.layerElement),r.matteElement.appendChild(r.transformedElement),r.baseElement=r.matteElement}SVGMatte3Effect.prototype.findSymbol=function(e){for(var t=0,r=_svgMatteSymbols.length;t<r;){if(_svgMatteSymbols[t]===e)return _svgMatteSymbols[t];t+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(e,t){var r=e.layerElement.parentNode;if(!!r){for(var i=r.children,s=0,a=i.length;s<a&&i[s]!==e.layerElement;)s+=1;var n;s<=a-2&&(n=i[s+1]);var p=createNS("use");p.setAttribute("href","#"+t),n?r.insertBefore(p,n):r.appendChild(p)}},SVGMatte3Effect.prototype.setElementAsMask=function(e,t){if(!this.findSymbol(t)){var r=createElementID(),i=createNS("mask");i.setAttribute("id",t.layerId),i.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(t);var s=e.globalData.defs;s.appendChild(i);var a=createNS("symbol");a.setAttribute("id",r),this.replaceInParent(t,r),a.appendChild(t.layerElement),s.appendChild(a);var n=createNS("use");n.setAttribute("href","#"+r),i.appendChild(n),t.data.hd=!1,t.show()}e.setMatte(t.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var e=this.filterManager.effectElements[0].p.v,t=this.elem.comp.elements,r=0,i=t.length;r<i;)t[r]&&t[r].data.ind===e&&this.setElementAsMask(this.elem,t[r]),r+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()};function SVGGaussianBlurEffect(e,t,r,i){e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width","300%"),e.setAttribute("height","300%"),this.filterManager=t;var s=createNS("feGaussianBlur");s.setAttribute("result",i),e.appendChild(s),this.feGaussianBlur=s}return SVGGaussianBlurEffect.prototype.renderFrame=function(e){if(e||this.filterManager._mdf){var t=.3,r=this.filterManager.effectElements[0].p.v*t,i=this.filterManager.effectElements[1].p.v,s=i==3?0:r,a=i==2?0:r;this.feGaussianBlur.setAttribute("stdDeviation",s+" "+a);var n=this.filterManager.effectElements[2].p.v==1?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",n)}},registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),setExpressionsPlugin(Expressions),initialize$1(),initialize(),registerEffect(20,SVGTintFilter,!0),registerEffect(21,SVGFillFilter,!0),registerEffect(22,SVGStrokeEffect,!1),registerEffect(23,SVGTritoneFilter,!0),registerEffect(24,SVGProLevelsFilter,!0),registerEffect(25,SVGDropShadowEffect,!0),registerEffect(28,SVGMatte3Effect,!1),registerEffect(29,SVGGaussianBlurEffect,!0),lottie})});var be=we(ve());function me(e,t){let r,i=null;return function(s){if(r){i=s;return}r=setTimeout(function(){e(i),r=null},t)}}function ye(e,t){let r=me(t,15);return e.addEventListener("click",r),function(){e.removeEventListener("click",r)}}var ge=(e,t,r=!1)=>{let i=!1,s=!1;function a(m){return function(){m&&!i?(i=!0,n(m)):!m&&i&&(i=!1,n(m))}}function n(m){s&&r||(s=!0,t(m))}let p=a(!0),c=a(!1);return e.addEventListener("mousemove",p,!1),e.addEventListener("mouseover",p,!1),e.addEventListener("mouseleave",c,!1),function(){e.removeEventListener("mousemove",p,!1),e.removeEventListener("mouseover",p,!1),e.removeEventListener("mouseout",c,!1)}};var{attach:Ie}=window.csGlobal.rivet,{watchElementIsVisible:Ve,elementIsVisibleInViewport:Re}=window.csGlobal.rivet.util,Le=/__cs_debug/.test(location.href),De="autoplay",Be="play-when-visible",Ge="hover",Oe="scroll-position-seek",ze="click",de={};Ie("[data-x-element-lottie]",(e,t)=>{var m,g;let{type:r,json:i,loop:s,loop_amount:a,url:n}=t!=null?t:{},p={teardown:function(){}},c=!!s;a!==-1&&s&&(c=a),t.trigger==="scroll-position-seek"&&(c=!1);try{let x={container:e,loop:c,autoplay:!1,renderer:t.renderer||"svg",path:n};if(r==="json")x.animationData=JSON.parse(i);else if(!n){e.innerHTML="<div class='tco-loader'>No URL or JSON set for lottie element</div>";return}let u=((g=(m=window.csAppConfig)==null?void 0:m.data)==null?void 0:g.isPreview)?"Loading...":"";e.innerHTML=`<div class='tco-loader'>${u}</div>`;let P=be.default.loadAnimation(x);p.lottie_animation=P,Le&&console.trace("Loaded Animation",P,x),Ne(P,t).then(v=>{p.teardown=He(v,t)}).catch(function(v){Se(e,v)})}catch(x){Se(e,x),console.warn("Unable to attach lottie animation",x,e,t)}return function(){p.lottie_animation&&p.lottie_animation.destory&&p.lottie_animation.destory(),p.teardown&&p.teardown()}});var Ne=(e,t)=>new Promise((r,i)=>{if(t.type==="json"){setTimeout(function(){r(e)},25);return}de[t.url]&&(e.animationData=de[t.url],setTimeout(function(){r(e)},25)),e.addEventListener("data_ready",function(){r(e),setTimeout(function(){de[t.url]=e.animationData},25)}),e.addEventListener("data_failed",function(){i("Failed to load based on url "+t.url)})}),He=(e,t)=>{e.wrapper.removeChild(e.wrapper.childNodes[0]);let r=null;switch(t.trigger){case De:ue(e,t,!0);break;case Be:r=We(e,t);break;case Ge:r=je(e,t);break;case Oe:r=Ke(e,t);break;case ze:r=qe(e,t);break;default:return consle.warn("Invalid Lottie trigger",t.trigger,t),r}return t.speed_multiplier&&e.setSpeed(parseFloat(t.speed_multiplier)),r},We=(e,t)=>{let r=parseFloat(t.offset_top),i=parseFloat(t.offset_bottom),s=!1,a;function n(p){p&&!e.isPaused||(p?!s&&!a&&(a=ue(e,t,!0),s=!0):(e.isPaused||(a&&clearTimeout(a),e.pause()),a=null,s=!1))}requestAnimationFrame(function(){n(Re(e.wrapper,r,i,n))}),Ve(e.wrapper,r,i,n)},je=(e,t)=>{let r=t.hover_behavior==="reverse-on-leave";function i(a){if(!(a&&!e.isPaused&&!r))if(a)ue(e,t,!0,r);else{if(r){Ye(e,t);return}if(t.loop){$e(e,t);return}}}return ge(e.wrapper,i)};function qe(e,t){return ye(e.wrapper,function(){ue(e,t)})}var ue=(e,t,r=!1,i=!1)=>{r&&Xe(e,i);let s=Ee(e,t),a=e.currentFrame;i||e.goToAndStop(s[0],!0);let n=Ue(t.animation_delay);if(!n){e.playSegments(s,!0),i&&e.goToAndPlay(a,!0);return}return setTimeout(()=>{e.playSegments(s,!0),i&&e.goToAndPlay(a,!0)},n)},$e=function(e,t){let r=Ee(e,t);function i(){e.goToAndStop(r[0],!0),e.removeEventListener("loopComplete",i)}e.addEventListener("loopComplete",i)},Ye=(e,t={})=>{e.setDirection(-1),e.play()},Xe=(e,t=!1)=>{e.setDirection(1),t||e.goToAndStop(0,!0)},Ke=(e,t)=>{let r=window.innerHeight*(parseFloat(t.offset_top)*.01),i=window.innerHeight*(parseFloat(t.offset_bottom)*.01),s=t.loop_amount<=1?1:t.loop_amount,a=e.totalFrames,n=me(function(p){let c=e.wrapper.getBoundingClientRect(),m=window.innerHeight-c.top-r,g=window.innerHeight+c.height-i,x=m/g;x=Math.max(x,0),x=Math.min(x,1),t.loop&&s!==1&&(x=x*s%1);let u=a*x;e.goToAndStop(u,!0)},25);return n(),window.addEventListener("scroll",n),function(){window.removeEventListener("scroll",n)}},Ue=e=>typeof e=="number"?e:e.match(/^\d+s$/)?parseFloat(e)*1e3:parseFloat(e),Pe=e=>parseFloat(e)*.01,Ee=(e,t)=>{let r=e.csTotalFramesCache||e.totalFrames;e.csTotalFramesCache||(e.csTotalFramesCache=r);let i=Pe(t.animation_frame_start),s=r*i|0,a=Pe(t.animation_frame_end),n=r*a|0;return[s,n]};function Se(e,t){e.innerHTML=`<p>Error loading Lottie</p><code>${t}</code>`,console.warn("Error loading Lottie",t)}})();
/*!
 Transformation Matrix v2.0
 (c) Epistemex 2014-2015
 www.epistemex.com
 By Ken Fyrstenberg
 Contributions by leeoniya.
 License: MIT, header required.
 */
