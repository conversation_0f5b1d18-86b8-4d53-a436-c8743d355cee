(()=>{var{store:et,util:tt}=window.csGlobal.rivet,{subtractVectors:$,debounce:nt,teardown:st,listener:Y,listenerPassive:De,divideVectors:Te,clampVector:rt,vectorsEq:it,addVectors:ge,absVector:ot,roundVector:Fe,multiplyVectors:ve}=tt,ne=.065,at=75e-6,Ie=3,Me=7,ct=3,se=e=>!1,lt=at*-1;function dt(e,{canBeginMove:n=()=>!0,onBeginMove:r=()=>{},onEndMove:a=()=>{},onMove:o=()=>{},onClick:u=()=>{},className:t="is-drag",drag:c,click:g,scroll:s,checkY:l=!1}){let i=!0,d,v=null,p=!1,y=!1,P=!1,x,A=0,h=[],F=!0,T=(f,m)=>{x||(x=f.timeStamp);let b=x-f.timeStamp;x=f.timeStamp;let w=$(d,m);d=m;let D=rt(Te(w,[b,b]),Me*-1,Me);return h.push(D),h.length>ct&&h.shift(),A=Te(h.reduce((E,I)=>ge(E,I)),[h.length,h.length]),[m,A]},M=({clientX:f,clientY:m})=>[f,m],V=f=>{var m;return f.type.includes("touch")?M((m=f.touches[0])!=null?m:f.changedTouches[0]):M(f)},L=([f,m])=>Math.abs(f)>Ie||l&&Math.abs(m)>Ie,k=f=>{v=V(f),d=v,h=[]},q=f=>$(v,V(f)),R=f=>{se("POINTER START"),c&&!P&&n(f)&&(k(f),e.addEventListener("pointermove",_))},O=f=>{se("TOUCH START"),c&&!P&&n(f)&&(y=!0,k(f),e.removeEventListener("pointermove",_),e.addEventListener("touchmove",J))},U=f=>{if(se("DRAG",f.type),!!i)if(p)f.cancelable&&f.preventDefault(),o(...T(f,q(f)));else{f.type==="pointermove"&&e.setPointerCapture(f.pointerId);let m=q(f);L(m)&&(k(f),p=!0,r(...T(f,m),!1,f),t&&e.classList.add(t))}},_=f=>{U(f)},J=f=>{U(f)},H=(f,m)=>{x=null,!(f.type.includes("pointer")&&(e.removeEventListener("pointermove",_),e.releasePointerCapture(f.pointerId),y))&&(f.type.includes("touch")&&(e.removeEventListener("touchmove",J),y=!1),!!p&&(se("END",f.type,m),a(q(f),A,!1,m),t&&e.classList.remove(t),p=!1))},j=f=>{H(f),g&&f.target!==e&&!y&&u(f.target)},Z=f=>void H(f,!0),ee=f=>{if(!i){i=!0;return}H(f)},K=f=>void H(f),de=f=>{P?o(f,[0,0],!0):(P=!0,r([0,0],[0,0],!0)),ue()},ue=nt(()=>{P=!1,a([0,0],[0,0],!0)},200),fe=f=>{if(!p){let m=[f.deltaX,f.deltaY],b=ot(m);b[0]>b[1]&&(f.preventDefault(),de(m))}},z,S=function(){i=!1,z&&clearTimeout(z),z=setTimeout(function(){i=!0},100)};return st([Y(e,"touchstart",O),Y(e,"touchend",ee),Y(e,"touchcancel",K),Y(e,"pointerdown",R),Y(e,"pointerup",j),Y(e,"pointercancel",Z),s?Y(e,"wheel",fe):null,De(window,"scroll",S),De(document.body,"scroll",S),()=>void e.removeEventListener("pointermove",_)])}function re({el:e,drag:n,click:r,scroll:a,cursor:o=!1},u){if(window.csGlobal&&window.csGlobal.disableSlideInteractions)return()=>{};if(!n&&!r)return()=>{};o&&e.classList.add("can-drag");let{unsubscribe:t,getState:c,dispatch:g}=et.subscribe(e,"slider"),s=dt(e,{...u({getState:c,dispatch:g}),className:o?"is-drag":"",click:r,drag:n,scroll:a});return()=>{o&&e.classList.remove("can-drag"),s(),t()}}var Ve=e=>{let n=!1,r=!1,a=!1,o=null,u=[0,0],t=[0,0],c=$([1,1],[ne,ne]),g,s,l=d=>{let v=d-s;s=d,!a&&o&&(t=ge(t,ve($(u,o),[lt*v,0]))),t=ve(t,c);let p=ve(t,[v,v]),y=ge(u,p),P=!1,x=!1;if(c[0]<1||c[1]<1){let h=.001*v,F=Math.abs(t[0])<=h&&t[0]!==0,T=Math.abs(t[1])<=h&&t[1]!==0;F&&(t[0]=0),T&&(t[1]=0),(F&&T||F&&t[1]===0||T&&t[0]===0)&&(x=!0)}if(o){let h=$(o,y),F=$(o,u);h[0]>0^F[0]>0&&(y[0]=o[0],t[0]=0),h[1]>0^F[1]>0&&(y[1]=o[1],t[1]=0),it(Fe(o,1),Fe(y,1))&&(y=o,o=null,P=!0,x=!0,t=[0,0])}r&&(r=!1,x=!0,t=[0,0]),u=y;let A=e(y,{snapped:P,stalled:x});x&&!o?n=!1:g=requestAnimationFrame(l),typeof A=="function"&&A()},i=()=>{r=!1,!n&&(n=!0,s=performance.now(),cancelAnimationFrame(g),requestAnimationFrame(l))};return{start:()=>{i()},snapTo:d=>{i(),o=d?[d[0],0]:null},stall:()=>{r=!0,o=null},suspend:d=>{a=!!d},isSuspended:()=>a,setPosition:([d])=>{i(),u=[d,0]},setVelocity:([d],v=!0)=>{v&&i(),t=[d,0]},setFriction:d=>{c=$([1,1],[d,d])},resetFriction:()=>{c=[ne,ne]}}},ut=.2,ie=e=>Math.abs(e)<ut?0:e>0?1:-1;var{store:ft,util:pt}=window.csGlobal.rivet,{teardown:mt,listener:Le,intersect:gt,getEasing:vt,round:yt,onPageVisibilityChange:ht,triggerScan:mn,oncePassive:bt,listenerPassive:wt}=pt,Ce=({onPlay:e,onPause:n,onReset:r,duration:a})=>{let o=new Set,u=!1,t=()=>{!u&&o.size<=0&&(u=!0,e(i))},c=d=>{o.has(d)&&o.delete(d),t()},g=d=>{let v=u;u=!1,o.has(d)||o.add(d),v&&n(i)};g("main");let i={getDuration:()=>(typeof a=="function"?a():a)||1e3,release:c,hold:g,reset:()=>{r(i,u),t()},setup:()=>{}};return i},St=({duration:e,interval:n,animate:r,easing:a="linear"})=>{let o,u,t,c,g=0,s=0,l=vt(a);r(0);let i=p=>{let y=yt(l((p-u)/t),1e3);y!==g&&r(y),g=y,c=requestAnimationFrame(i)},d=p=>{u=window.performance.now(),s?u=window.performance.now()-p*s:r(0),g=s,t=p,s=0,cancelAnimationFrame(c),c=requestAnimationFrame(i)},v=p=>{let y=p;s&&(y=p-p*s),d(p),o=setInterval(()=>{d(p),y!==p&&(clearInterval(o),cancelAnimationFrame(c),v(p)),n()},y)};return Ce({duration:e,onPlay({getDuration:p}){v(p())},onPause(){s=g,clearInterval(o),cancelAnimationFrame(c)},onReset({getDuration:p},y){clearInterval(o),cancelAnimationFrame(c),y&&v(p())}})},xt=({duration:e})=>{let n={play:()=>{},pause:()=>{},reset:()=>{}};return{...Ce({duration:e,onPlay:(...o)=>n.play(...o),onPause:(...o)=>n.pause(...o),onReset:(...o)=>n.reset(...o)}),setup:o=>{n={...n,...o}}}},At=({mode:e,...n})=>e==="interval"?St(n):e==="marquee"?xt(n):{reset:()=>{},setup:()=>{},hold:()=>{},release:()=>{}},Pt=({autoplay:e,stacked:n})=>n&&e==="marquee"?"off":e,oe=(e,n,r)=>{let a=Pt(r),{unsubscribe:o,getState:u,dispatch:t}=ft.subscribe(e,"slider",(v,p,y)=>{y&&requestAnimationFrame(()=>{c.release("main")}),!y&&p.includes("lastUserInteraction")&&c.reset()},["lastUserInteraction"]),c=At({mode:a,duration:()=>u().autoplayDuration||1e3,interval:()=>t(v=>({...v,autoplayAdvance:1})),animate:v=>{n.style.setProperty("--x-slide-autoplay-progress",v)}}),g=Le(window,"tco-drag-start",()=>{c.hold("dragging")}),s=Le(window,"tco-drag-end",()=>{c.release("dragging")}),l=null,i=null;r.autoplayStartInView&&(c.hold("viewport"),l=gt(e,({isIntersecting:v})=>{v?(c.release("viewport"),Array.from(e.querySelectorAll('[loading="lazy"]')).forEach(p=>{p.removeAttribute("loading")})):c.hold("viewport")},{threshold:.15,top:"0px",bottom:"0px"}),i=ht(v=>{v?c.release("vis"):c.hold("vis")})),u().autoplayer=c;let d=[o,g,s,l,i];if(r.pauseOnHover){let v=kt(e,c);d.push(v)}return{unsubscribe:mt(d),autoplayer:c}};function kt(e,n){let r=e.parentNode;return wt(r,"mouseover",function(){n.hold(),bt(r,"mouseout",function(){n.release()})})}var qe=(e,n,r,a)=>e(()=>{let{unsubscribe:o,autoplayer:u}=oe(n,r,a),t=(a.swipe||"").includes("x"),c=(a.swipe||"").includes("y"),g=([i,d])=>t&&c?Math.abs(i)>=Math.abs(d)?0:1:c?1:0,s=i=>ie(i[g(i)]),l=re({el:n,drag:!!a.swipe,checkY:c},({dispatch:i,getState:d})=>({onBeginMove:()=>void u.hold("stacked-drag"),onEndMove:(v,p,y,P)=>{if(!P){let x=s(p);x!==0&&i(A=>({...A,advance:x}))}u.release("stacked-drag")}}));return()=>{o(),l()}});var{store:Et,util:Dt}=window.csGlobal.rivet,{multiplyVectors:Be,once:Sn,oncePassive:Tt,vectorsEq:ye,listener:Ft,wrapNumber:It,clamp:Mt,addVectors:N,subtractVectors:X,balanceFromDifference:Vt,makeSortByKey:Lt,elementMeta:G,onViewportChange:Ct,onLoad:qt}=Dt,Bt=e=>n=>Math.floor(n/e),Nt=({subprime:e=0,totalSlides:n=0,slidesPerScrollPage:r=1})=>Mt(Bt(r)(e)*r,0,n),he=e=>n=>{if(!n)return null;let r=n;do r=r[e];while(r&&!r.matches("[data-x-slide]"));return r};function Rt(e,n,{wrapAround:r}){if(!r)return()=>{};let a=()=>{let{clones:t=[]}=G.get(e);t.forEach(c=>c.remove())},o=t=>c=>({before:t,clone:G.get(c).createClone()}),u=Ft(e,"tco-slide-refresh",()=>{let{slides:t=[]}=G.get(e);a();let c=[...t.map(o(!0)).reverse(),...t.map(o(!1))],g=0;t.forEach(function(l){g+=l.getBoundingClientRect().width});let s=window.innerWidth/g;for(s=Math.ceil(s),s=Math.min(6,s),g===0&&(s=2),s-=2;s>0;)c=[...t.map(o(!0)).reverse(),...c,...t.map(o(!1))],s-=2;c.forEach(({clone:l,before:i})=>{n[i?"prepend":"append"](l)}),G.update(e,l=>({...l,clones:c.map(({clone:i})=>i)})),e.dispatchEvent(new CustomEvent("tco-slides-cloned"))});return()=>{u(),a()}}var Ot=(e,n,r)=>{let a=()=>G.get(e).slides;a.first=()=>a()[0],a.last=()=>{let s=a();return s[s.length-1]};let o=Rt(e,n,r),u=(s,l)=>{let i=he(l<0?"previousElementSibling":"nextElementSibling");return(d,v)=>{if(l===0)return s[d];let p=i(s[v]);for(;p;){if(G.get(p).index===d)return p;p=i(p)}return s[d]}},t=(s,{prevSubprime:l=0,totalSlides:i,advanceDirection:d})=>{let v=a();if(!r.wrapAround)return v[s];let p=d===0?Vt(s,l,i):d;return u(v,p)(s,l)};return{anticipateSnapTarget:s=>t(Nt(s),s),teardownSlides:()=>{o()},getSlides:a}},Ut=(e,{justify:n})=>{let r,a=()=>{r=new WeakMap};a();let o=({offsetLeft:l,offsetTop:i,offsetWidth:d,offsetHeight:v})=>[l,i,d,v],u=l=>{if(!r.has()){let i=o(l);return r.set(l,i),i}return r.get(l)};return{getElementOffsets:u,getElementDimensions:l=>{let[,,i,d]=u(l);return[i,d]},getElementCoordinates:l=>{let[i,d]=u(l);return[i,d]},getVectorFromTarget:l=>{if(!l)return[0,0];let[i,d,v,p]=u(l),y=[i,d];return n==="center"?N(y,[v/2,p/2]):n==="end"?N(y,[v,p]):y},normalizeJustifiedVector:l=>{let[,,i,d]=u(e);return n==="center"?X(l,[i/2,d/2]):n==="end"?X(l,[i,d]):l},teardown:Ct(a)}},Ht=(e,n,{getElementDimensions:r,getElementOffsets:a,getElementCoordinates:o,normalizeJustifiedVector:u,getVectorFromTarget:t},{contain:c,wrapAround:g})=>{let s=0;return l=>{let i=e.first(),d=e.last();if(g){let v=X(l,t(he("previousElementSibling")(i))),p=X(l,t(he("nextElementSibling")(d)));if(v[s]<=0)return[l,N(t(d),v)];if(p[s]>=0)return[l,N(t(i),p)]}else{if(c){let y=u(l);if(y[s]<0){let h=Be(u([0,0]),[-1,-1]);return[h,h]}let P=r(n.parentElement),x=N(o(d),r(d)),A=X(x,P);if(u(A),y[s]>A[s]){let h=N(A,X(A,u(A)));return[h,h]}}let v=t(i),p=t(d);if(l[s]<v[s])return[v,v];if(l[s]>p[s])return[p,p]}return[l,null]}},Ne=(e,n,r,a,o)=>e(()=>{let{wrapAround:u=!1,snap:t=!1,int:c=""}=o,{unsubscribe:g,autoplayer:s}=oe(n,r,o),l=o.autoplay==="marquee";l&&s.hold("marquee-positioned");let i=Number.parseFloat(o.speed)||.1;o.direction==="reverse"&&(i=-i);let{getSlides:d,anticipateSnapTarget:v,teardownSlides:p}=Ot(n,a,o),y=Ut(a,o),{normalizeJustifiedVector:P,getElementDimensions:x,getVectorFromTarget:A,teardown:h}=y,F=Ht(d,a,y,o),T=!1,M=[0,0],V=[0,0],L=[0,0],k=m=>{if(ye(m,M)&&T)return!1;M=m,V=M;let[b]=P(m);return a.style.setProperty("transform",`translate3d(${b*-1}px,0,0)`),!0},q,R=([m])=>{let[b,w]=F([m,0]);S.suspend(!0),s.hold("transition"),a.style.removeProperty("transition"),k(b),clearTimeout(q),q=setTimeout(()=>{a.style.setProperty("transition","none"),a.offsetHeight,w&&k(w),s.release("transition"),S.suspend(!1),S.stall()},J().transitionDuration)},O=m=>A(v(m)),U=m=>l&&i<0?A(d.last()):O(m),{unsubscribe:_,getState:J,dispatch:H}=Et.subscribe(n,"slider",(m,b)=>{if(!m.positioned||!m.pageLoaded)return;if(!T){a.style.setProperty("transition","none"),a.offsetHeight,k(U(m)),a.offsetHeight,a.style.removeProperty("transition"),T=!0,requestAnimationFrame(()=>{s.release("marquee-positioned")});return}if(l)return;if(b.includes("lastAutoBasisAdvance")){let E=Be(x(a.parentElement),[m.autoBasisAdvanceAmount,m.autoBasisAdvanceAmount]),I=N(V,E),[,B]=F(I);R(I),z(B||I);return}let w=m.allowSnapTransition&&b.includes("subprime")||b.includes("snapBack"),D=b.includes("styleUpdate");(w||D)&&R(O(m))},["isTouchInteraction","allowSnapTransition","styleUpdate","subprime","slidesPerPage","snapBack","lastAutoBasisAdvance"]),j=(m,b,w=0)=>{let{slideData:D}=J(),E=[];w!==0&&(E=D.filter(({isPageStart:C,balance:Q})=>C&&Q===w).map(({index:C})=>C),E.length===0&&u&&(E=D.filter(({isPageStart:C})=>C).map(({index:C})=>C)));let I=P(N(m,b)),B=Array.from(a.children),pe=Math.floor(B.length/2),te=[];for(let C=0;C<B.length;C++){let Q=B[It(C+pe,B.length)],{index:me,isClone:Ke}=G.get(Q),{isPageStart:Qe}=D[me];if(!Qe||E.length>0&&!E.includes(me))continue;let Pe=A(Q),Ze=x(Q)[0],je=X(P(Pe),I),ke=Math.abs(je[0]),Ee={slide:Q,vector:Pe,isClone:Ke,index:me,distance:ke};if(E.length===0&&ke<Ze/2)return Ee;te.push(Ee)}return te.sort(Lt("distance")).shift()},Z=c.includes("drag"),ee=!1,K=!1,de=re({el:n,drag:Z,click:c.includes("click")&&!l,cursor:Z,scroll:Z&&!l},({dispatch:m})=>({canBeginMove:({target:b})=>{if(d().length<=0||K)return!1;let w=b.closest(".tco-element-preview");return!w||w.matches(".x-slide, .x-slide-container-viewport")},onBeginMove:(b,w,D,E)=>{D&&(ee=!0),s.hold("user-drag"),L=M,S.suspend(!0),S.snapTo(null),S.setVelocity([0,0]),S.setPosition(L),m(I=>({...I,isTouchInteraction:!0,allowSnapTransition:!1}))},onEndMove:(b,w,D,E)=>{S.suspend(!1),S.setVelocity(w),s.hold("physics"),D&&(S.stall(),ee=!1);let I={isTouchInteraction:!1};if(t&&!l){let B=E?0:ie(w[0]),{index:pe,vector:te}=j(D||E?M:L,E?[0,0]:b,B);I.softNavigateTo=pe,S.snapTo(te)}m(B=>({...B,...I})),s.release("user-drag")},onMove:(b,w,D)=>{if(!K)if(D){let E=N(M,b);if(u)S.setPosition(E);else{let[I]=F(E);S.setPosition(ye(E,I)?E:I)}}else S.snapTo(null),S.setPosition(N(L,b))},onClick:b=>{let w=G.get(b.closest("[data-x-slide]")).index;typeof w!="undefined"&&m(D=>({...D,navigateToIndex:w}))}})),ue=m=>{let[b,w]=F(m);return u?(w&&l&&S.setPosition(w),w||m):(S.isSuspended()||b&&w&&ye(b,w)&&(K=!0,S.snapTo(w),z(w)),m)},fe=m=>{J().prime!==m&&H(b=>({...b,softNavigateTo:m}))},z=m=>{fe(j(m,[0,0]).index)},S=Ve((m,{snapped:b,stalled:w})=>{let D=ue(m);if(k(D),(w||b)&&(K=!1),l&&(z(D),b&&(i*=-1,S.resetFriction(),S.setVelocity([i,i]))),w)return()=>{s.release("physics")}});a.style.setProperty("transition","none"),s.setup({play:()=>{S.setFriction(0),S.setPosition(M),S.setVelocity([i,i])},pause:()=>{S.setVelocity([0,0],!1),S.resetFriction(),S.stall()}});let f=Tt(n,"tco-slides-cloned",()=>{H(m=>({...m,positioned:!0}))});return()=>{p(),f(),g(),_(),qt(()=>{H(m=>m),S.start()}),de(),h()}});var{drive:Gt,attach:Re,util:Wt,debug:zt}=window.csGlobal.rivet,{elementMeta:W,getCachedJsonAttribute:$t,listener:Yt}=Wt,Oe=e=>!!W.get(e).isClone,Ue=e=>e?Array.from(e.children).filter(n=>!Oe(n)&&n.matches(".x-slide")):[],He=e=>Ue(e).filter(n=>W.get(n).ready),ae=(e,n)=>{if(!n){let{index:r}=W.get(e);if(typeof r!="undefined")return r}return Ue(e.parentElement).indexOf(e)},Ge=e=>{if(!e.hasAttribute("data-cs-observeable-id"))return;let n=e.getAttribute("data-cs-observeable-id");e.classList.remove("tco-element-preview"),e.removeAttribute("data-cs-observeable-container"),e.removeAttribute("data-cs-observe"),e.removeAttribute("data-cs-observeable"),e.removeAttribute("data-cs-observeable-id"),e.classList.add(`tco-observe-${n}`)},Xt=(e,n)=>{let r=e.cloneNode(!0);return r.classList.add("is-virtual"),r.setAttribute("aria-hidden",!0),Ge(r),Array.from(r.querySelectorAll("[data-cs-observeable-id]")).map(Ge),Array.from(r.querySelectorAll("[id]")).map(a=>a.removeAttribute("id")),()=>{let a=r.cloneNode(!0);return W.set(a,{...n,index:ae(e),isClone:!0}),a}};Re("[data-x-slide]",e=>{if(Oe(e))return;let n=e.closest("[data-x-slide-container]"),{wrapAround:r,enter:a="effect",exit:o="effect"}=$t(n,"data-x-slide-container"),u={enter:a,exit:o};W.update(e,c=>({...c,index:ae(e),ready:!0,effects:u,createClone:r?Xt(e,{effects:u}):null})),n.dispatchEvent(new CustomEvent("tco-slide-added-or-removed"));let t=Yt(n,"tco-slide-reindex",()=>{W.update(e,c=>({...c,index:ae(e,!0)}))});return()=>{W.del(e),t(),n.dispatchEvent(new CustomEvent("tco-slide-added-or-removed"))}},50);Re("[data-x-slide]",e=>(W.update(e,n=>({...n,effectRivet:["effects",{key:()=>`slideData.${ae(e)}.active`,condition:!0,enter:"effect",exit:"effect"},"slider"]})),Gt([[e,"classname",{key:"slideData.meta(index).active",classname:"is-current-slide",condition:!0},"slider"],zt(e)&&[e,"attr",{key:"subprime",attr:"data-prime-slide",value:!0,condition:["meta(index)","==="]},"slider"],[e,"effects",{key:"slideData.meta(index).active",condition:!0,enter:"meta(effects.enter)",exit:"meta(effects.exit)"},"slider"],[e,"prop",{key:"slideData.meta(index).distance",prop:"--x-slide-distance"},"slider"],[e,"prop",{key:"slideData.meta(index).balance",prop:"--x-slide-balance"},"slider"]])),200);var{attach:_t,store:We,drive:Jt,util:ze}=window.csGlobal.rivet,{onLoad:Kt,wrapNumber:Qt,clamp:Zt,makeStateSynchronizer:jt,distanceInRange:$e,balanceFromDifference:en,listenerPassive:ce,elementMeta:be,parseTime:Ye}=ze,tn=e=>n=>Math.floor(n/e),nn=({slidesPerScrollPage:e,totalSlides:n})=>e?Math.ceil(n/e):n,sn=()=>{let e=null,n=!0,r=(u,t)=>{n=t?!!t.keyboardNavigation:!0,e=u.closest("[data-x-slide-context], [data-x-slide-container], [data-x-slide-pagination], [data-x-slide-next], [data-x-slide-prev], [data-x-slide-goto]")};ce(window,"focusin",({target:u})=>{r(u)});let a=["ArrowRight","ArrowDown"],o=["ArrowLeft","ArrowUp"];return ce(window.document,"keyup",({key:u})=>{if(e&&n){let t=a.includes(u),c=o.includes(u);if(t||c){let g=We.makeDispatch(e,"slider");g&&g(s=>({...s,advance:t?1:-1}))}}}),r},rn=sn(),on=()=>({advance:e,autoplayAdvance:n,navigateToIndex:r,softNavigateTo:a,navigateTo:o,...u})=>{let t={...u},{scrollBySlide:c,wrapAround:g,autoBasis:s}=t.options,l=!c&&s;typeof t.autoplayDuration!="number"&&(t={...t,autoplayDuration:Ye(t.autoplayDuration)}),typeof t.transitionDuration!="number"&&(t={...t,transitionDuration:Ye(t.transitionDuration)}),typeof t.slidesPerPage!="number"&&(t={...t,slidesPerPage:Number(t.slidesPerPage)}),t.slidesPerScrollPage=c?1:t.slidesPerPage;let i=tn(t.slidesPerScrollPage),d=nn(t),v=i(t.prime),p,y;if(typeof a!="undefined"&&(y=a),typeof r!="undefined"&&(y=i(r)),t.options.autoplay!=="marquee"&&((typeof e!="undefined"||typeof o!="undefined"||typeof r!="undefined")&&(t={...t,lastUserInteraction:Date.now(),allowSnapTransition:!0}),typeof e!="undefined"&&t.ready&&(p=e),typeof o!="undefined"&&t.ready&&(y=o,t={...t,advanceDirection:0})),typeof n!="undefined"&&(p=n),typeof p!="undefined"&&(l?t={...t,lastAutoBasisAdvance:Date.now(),autoBasisAdvanceAmount:p,allowSnapTransition:!1}:(t={...t,advanceDirection:p>0?1:-1},y=Qt(v+p,d))),typeof y!="undefined"){let A=y*t.slidesPerScrollPage;t={...t,prime:Zt(A,0,t.totalSlides-1)}}let P=i(t.prime),x=i(t.subprime);return t={...t,current:P+1,total:d,atStart:P===0,atEnd:P===d-1,slideData:Array.from({length:t.totalSlides}).map((A,h)=>{let T=h%t.slidesPerScrollPage===0;if(i(h)===x)return{active:!0,distance:0,balance:0,index:h,isPageStart:T};let V=t.subprime,L=t.subprime+t.slidesPerScrollPage-1;if(g){let R=en(h,t.subprime,t.totalSlides),O=R===1?L:V,U=Math.min($e(h,O,t.totalSlides),$e(O,h,t.totalSlides));return{active:!1,distance:U,balance:R,index:h,isPageStart:T}}let k=h>V?1:-1,q=h>V?h-L:V-h;return{active:!1,balance:k,distance:q,index:h,isPageStart:T}})},t};_t("[data-x-slide-container]",(e,n)=>{let r=e.closest("[data-x-slide-context]")||e,a=e.querySelector(".x-slide-container"),{stacked:o=!1,autoplay:u="off",adaptiveHeight:t=!1,wrapAround:c,startingSlide:g=1}=n,s=l=>{let i=e.querySelector(".x-slide-container-content"),d=()=>be.update(e,k=>({...k,slides:He(a)}));d();let v=be.get(e).slides.length;g=v>0?Math.min(v,g):g,g=Math.max(1,g);let p=g-1,y=document.readyState==="complete",P=Jt([[r,"define",{options:n,loading:!0,ready:!1,advanceDirection:0,autoplayDuration:"var(--x-slide-container-autoplay-transition-duration, 5s)",transitionDuration:"var(--x-slide-container-transition-duration, 1s)",isTouchInteraction:!1,allowSnapTransition:!0,current:g,prime:p,subprime:p,prevSubprime:p,slideData:[],styleUpdate:g,slidesPerPage:"var(--x-slides-per-page,1)",lastUserInteraction:Date.now(),autoBasisAdvanceAmount:1,lastAutoBasisAdvance:Date.now(),positioned:!!(o||!c),pageLoaded:y,total:v,totalSlides:v,_reducer:on()},"slider"],[r,"prop",{key:"current",prop:"--x-slide-current"},"slider"],[r,"prop",{key:"total",prop:"--x-slide-total"},"slider"],[e,"onready"],[e,"onload"],[e,"onscan"],[e,"inc",{state:"slider",key:"styleUpdate"}],[e,"onload",{},"loaded"],[e,"onready",{},"ready"],[e,"set",{state:"slider",key:"ready",value:!0,defer:!0},"ready"],[e,"classname",{key:"ready",classname:"is-ready",condition:!0},"slider"],[e,"set",{state:"slider",key:"transitionDuration",value:"var(--x-slide-container-transition-duration, 1s)"}],u!=="off"&&[e,"set",{state:"slider",key:"autoplayDuration",value:"var(--x-slide-container-autoplay-transition-duration, 5s)"}],!o&&[e,"set",{state:"slider",key:"slidesPerPage",value:"var(--x-slides-per-page)"}],t&&[i,"height",{key:"subprime",selector:".is-current-slide"},"slider"]]),x=ze.rivetListener(e,"loaded",()=>{e.classList.remove("is-loading")}),A=jt((k,q)=>{let R=k,O=F().transitionDuration;T(U=>({...U,prevSubprime:U.subprime,subprime:R})),setTimeout(q,O)}),{unsubscribe:h,getState:F,dispatch:T}=We.subscribe(e,"slider",({prime:k})=>void A(k)),M=ce(e,"tco-slide-added-or-removed",()=>{d(),T(k=>({...k,totalSlides:be.get(e).slides.length})),e.dispatchEvent(new CustomEvent("tco-slide-reindex")),e.dispatchEvent(new CustomEvent("tco-slide-refresh"))}),V=l();e.dispatchEvent(new CustomEvent("tco-slide-refresh"));let L=ce(e,"pointerup",()=>rn(e,n));return[h,P,M,x,V,L,y?null:Kt(()=>{T(k=>({...k,pageLoaded:document.readyState==="complete"}))})]};return o?qe(s,e,r,n):Ne(s,e,r,a,n)},100);var{csHooks:an}=window.csGlobal,{attach:le,store:we,drive:Xe,util:cn,debug:Dn}=window.csGlobal.rivet,{makeStateSynchronizer:ln,getTransitionDuration:dn,listener:Se,elementMeta:un}=cn;le("[data-x-slide-pagination]",e=>{let n=ln((c,g)=>{Array.from(e.querySelectorAll("li")).forEach((s,l)=>{let i=l===c-1;s.classList.toggle("is-active",i),!i&&s.hasAttribute("aria-current")&&s.removeAttribute("aria-current"),i&&!s.hasAttribute("aria-current")&&s.setAttribute("aria-current","step")}),setTimeout(g,dn(e.querySelector("li")))}),r=c=>{let g=(s,l,i)=>s+`<li data-x-slide-index="${i+1}" aria-label="Slider Page ${i+1}"><span class="visually-hidden">${i}</span></li>`;e.innerHTML=Array.from({length:Math.max(1,c)}).reduce(g,""),n.reset()};r(1);let a=xe(e),{unsubscribe:o,dispatch:u}=we.subscribe(a,"slider",({total:c,current:g},s,l)=>{(l||s.includes("total"))&&r(c),n(g)},["current","total","slidesPerPage","styleUpdate"]),t=Ae(e);return[Se(e,t,c=>{c.preventDefault();let g=c.target.closest("li");if(g){let s=Number.parseInt(g.getAttribute("data-x-slide-index"),10)-1;isNaN(s)||u(l=>({...l,navigateTo:s}))}}),o]},1e3);var fn=(e,n,r)=>e?Xe([[n,"effects",{key:r===-1?"atStart":"atEnd",condition:[!1,"==="],enter:"effect",exit:"effect"},"slider"],[n,"attr",{key:r===-1?"atStart":"atEnd",attr:"disabled",value:"",condition:[!0,"==="]},"slider"]]):()=>{},_e=e=>{let n=Number.parseInt(e);return isNaN(n)||n<=0?1:n},Je=e=>(n,r)=>{var s,l;let a=_e(r)*e,o=xe(n),{unsubscribe:u,getState:t,dispatch:c}=we.subscribe(o,"slider"),g=Ae(n);return[fn(!((l=(s=t())==null?void 0:s.options)==null?void 0:l.wrapAround)&&r!=="noDisable",n,e),Se(n,g,i=>{c(d=>({...d,advance:a}))},{passive:!0}),u]};le("[data-x-slide-next]",Je(1),1e3);le("[data-x-slide-prev]",Je(-1),1e3);le("[data-x-slide-goto]",(e,n)=>{let r=_e(n),a=r-1,o=xe(e),u=!!e.getAttribute("data-x-slide-goto-keep-active");u=an.apply("slide-goto-keep-active",u,e);let{unsubscribe:t,getState:c,dispatch:g}=we.subscribe(o,"slider",function(i){!u||(r===i.current?e.classList.add("x-active"):e.classList.remove("x-active"))}),s=["effects",{key:"current",condition:[a+1,"=="],enter:"effect",exit:"effect"},"slider"];un.update(e,i=>({...i,effectRivet:s}));let l=Ae(e);return[Se(e,l,i=>{g(d=>({...d,navigateTo:a}))},{passive:!0}),t,Xe([[e,...s]])]});function xe(e){let n=e;if(e.hasAttribute("data-x-slider-id")){let r=e.getAttribute("data-x-slider-id"),a=document.getElementById(r);a&&(n=a)}return n}function Ae(e){return e.getAttribute("data-x-slide-goto-trigger")||"click"}})();
