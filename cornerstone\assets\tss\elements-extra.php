[{"1": [{"2": ["audio", [], [{"3": [["&.x-audio"], [{"4": ["width", {"5": ["get", "(audio_width)", [{"6": "audio_width"}]]}, false]}, {"7": [{"5": ["is-set", "(get(audio_max_width), get-base(audio_max_width))", [{"5": ["get", "(audio_max_width)", [{"6": "audio_max_width"}]]}, {"5": ["get-base", "(audio_max_width)", [{"6": "audio_max_width"}]]}]]}, [{"4": ["max-width", {"5": ["get", "(audio_max_width)", [{"6": "audio_max_width"}]]}, false]}]]}, {"8": ["margin", [{"5": ["get-base", "(audio_margin)", [{"6": "audio_margin"}]]}, {"5": ["get", "(audio_margin)", [{"6": "audio_margin"}]]}, {"9": ["0", "px"]}]]}]]}]]}, {"2": ["mejs", [], [{"3": [[".x-mejs .mejs-controls"], [{"7": [{"10": [{"5": ["get", "(mejs_type)", [{"6": "mejs_type"}]]}, "==", {"11": "video"}]}, [{"8": ["margin", [{"5": ["get-base", "(mejs_controls_margin)", [{"6": "mejs_controls_margin"}]]}, {"5": ["get", "(mejs_controls_margin)", [{"6": "mejs_controls_margin"}]]}]]}]]}, {"8": ["border", [{"12": ["width", {"5": ["get", "(mejs_controls_border_width)", [{"6": "mejs_controls_border_width"}]]}]}, {"12": ["style", {"5": ["get", "(mejs_controls_border_style)", [{"6": "mejs_controls_border_style"}]]}]}, {"12": ["base", {"5": ["get", "(mejs_controls_border_color)", [{"6": "mejs_controls_border_color"}]]}]}]]}, {"8": ["border-radius", [{"5": ["get-base", "(mejs_controls_border_radius)", [{"6": "mejs_controls_border_radius"}]]}, {"5": ["get", "(mejs_controls_border_radius)", [{"6": "mejs_controls_border_radius"}]]}]]}, {"8": ["padding", [{"5": ["get-base", "(mejs_controls_padding)", [{"6": "mejs_controls_padding"}]]}, {"5": ["get", "(mejs_controls_padding)", [{"6": "mejs_controls_padding"}]]}]]}, {"8": ["background-color", [{"6": "mejs_controls_bg_color"}]]}, {"8": ["box-shadow", [{"5": ["get", "(mejs_controls_box_shadow_dimensions)", [{"6": "mejs_controls_box_shadow_dimensions"}]]}, {"5": ["get", "(mejs_controls_box_shadow_color)", [{"6": "mejs_controls_box_shadow_color"}]]}]]}]]}, {"7": [{"10": [{"5": ["get", "(mejs_type)", [{"6": "mejs_type"}]]}, "==", {"11": "video"}]}, [{"3": [[".x-mejs video"], [{"4": ["object-fit", {"5": ["get", "(mejs_object_fit)", [{"6": "mejs_object_fit"}]]}, false]}, {"4": ["object-position", {"5": ["get", "(mejs_object_position)", [{"6": "mejs_object_position"}]]}, false]}]]}]]}, {"3": [[".x-mejs .mejs-button button"], [{"8": ["text-color", [{"5": ["get", "(mejs_controls_button_color)", [{"6": "mejs_controls_button_color"}]]}, {"5": ["get", "(mejs_controls_button_color_alt)", [{"6": "mejs_controls_button_color_alt"}]]}]]}]]}, {"3": [[".x-mejs .mejs-button button:hover", ".x-mejs .mejs-button button:focus"], [{"8": ["text-color-alt", [{"5": ["get", "(mejs_controls_button_color)", [{"6": "mejs_controls_button_color"}]]}, {"5": ["get", "(mejs_controls_button_color_alt)", [{"6": "mejs_controls_button_color_alt"}]]}]]}]]}, {"3": [[".x-mejs .mejs-time-total"], [{"8": ["border-radius", [{"5": ["get-base", "(mejs_controls_time_rail_border_radius)", [{"6": "mejs_controls_time_rail_border_radius"}]]}, {"5": ["get", "(mejs_controls_time_rail_border_radius)", [{"6": "mejs_controls_time_rail_border_radius"}]]}]]}, {"8": ["background-color", [{"6": "mejs_controls_time_total_bg_color"}]]}, {"8": ["text-shadow", [{"5": ["get", "(mejs_controls_time_rail_box_shadow_dimensions)", [{"6": "mejs_controls_time_rail_box_shadow_dimensions"}]]}, {"5": ["get", "(mejs_controls_time_rail_box_shadow_color)", [{"6": "mejs_controls_time_rail_box_shadow_color"}]]}]]}]]}, {"3": [[".x-mejs .mejs-time-loaded"], [{"8": ["background-color", [{"6": "mejs_controls_time_loaded_bg_color"}]]}]]}, {"3": [[".x-mejs .mejs-time-current"], [{"8": ["background-color", [{"6": "mejs_controls_time_current_bg_color"}]]}]]}, {"3": [[".x-mejs .mejs-time"], [{"8": ["text-color", [{"5": ["get", "(mejs_controls_color)", [{"6": "mejs_controls_color"}]]}]]}]]}]]}, {"2": ["rating", [], [{"8": ["flexbox", [{"11": "rating"}]]}, {"8": ["changedmixin", [{"11": "auto"}, {"6": "rating_width"}, {"11": "width"}]]}, {"7": [{"5": ["is-set", "(get(rating_max_width), get-base(rating_max_width))", [{"5": ["get", "(rating_max_width)", [{"6": "rating_max_width"}]]}, {"5": ["get-base", "(rating_max_width)", [{"6": "rating_max_width"}]]}]]}, [{"4": ["max-width", {"5": ["get", "(rating_max_width)", [{"6": "rating_max_width"}]]}, false]}]]}, {"8": ["margin", [{"5": ["get-base", "(rating_margin)", [{"6": "rating_margin"}]]}, {"5": ["get", "(rating_margin)", [{"6": "rating_margin"}]]}]]}, {"8": ["border", [{"12": ["width", {"5": ["get", "(rating_border_width)", [{"6": "rating_border_width"}]]}]}, {"12": ["style", {"5": ["get", "(rating_border_style)", [{"6": "rating_border_style"}]]}]}, {"12": ["base", {"5": ["get", "(rating_border_color)", [{"6": "rating_border_color"}]]}]}]]}, {"8": ["border-radius", [{"5": ["get-base", "(rating_border_radius)", [{"6": "rating_border_radius"}]]}, {"5": ["get", "(rating_border_radius)", [{"6": "rating_border_radius"}]]}]]}, {"8": ["padding", [{"5": ["get-base", "(rating_padding)", [{"6": "rating_padding"}]]}, {"5": ["get", "(rating_padding)", [{"6": "rating_padding"}]]}]]}, {"4": ["font-size", {"5": ["get", "(rating_base_font_size)", [{"6": "rating_base_font_size"}]]}, false]}, {"8": ["background-color", [{"6": "rating_bg_color"}]]}, {"8": ["box-shadow", [{"12": ["dimensions", {"5": ["get", "(rating_box_shadow_dimensions)", [{"6": "rating_box_shadow_dimensions"}]]}]}, {"12": ["base", {"5": ["get", "(rating_box_shadow_color)", [{"6": "rating_box_shadow_color"}]]}]}]]}, {"3": [[".x-rating-graphic > * + *"], [{"4": ["margin-left", {"5": ["get", "(rating_graphic_spacing)", [{"6": "rating_graphic_spacing"}]]}, false]}]]}, {"7": [{"10": [{"5": ["get", "(rating_graphic_type)", [{"6": "rating_graphic_type"}]]}, "==", {"11": "icon"}]}, [{"3": [[".x-icon"], [{"8": ["text-color", [{"5": ["get", "(rating_graphic_icon_color)", [{"6": "rating_graphic_icon_color"}]]}]]}]]}]]}, {"7": [{"10": [{"5": ["get", "(rating_graphic_type)", [{"6": "rating_graphic_type"}]]}, "==", {"11": "image"}]}, [{"3": [[".x-image"], [{"4": ["max-width", {"5": ["get", "(rating_graphic_image_max_width)", [{"6": "rating_graphic_image_max_width"}]]}, false]}]]}]]}, {"7": [{"5": ["get", "(rating_text)", [{"6": "rating_text"}]]}, [{"3": [[".x-rating-text"], [{"8": ["margin", [{"5": ["get-base", "(rating_text_margin)", [{"6": "rating_text_margin"}]]}, {"5": ["get", "(rating_text_margin)", [{"6": "rating_text_margin"}]]}]]}, {"8": ["linotype", [{"12": ["ff", {"5": ["get", "(rating_font_family)", [{"6": "rating_font_family"}]]}]}, {"12": ["fsize", {"5": ["get", "(rating_font_size)", [{"6": "rating_font_size"}]]}]}, {"12": ["fstyle", {"5": ["get", "(rating_font_style)", [{"6": "rating_font_style"}]]}]}, {"12": ["fw", {"5": ["get", "(rating_font_weight)", [{"6": "rating_font_weight"}]]}]}, {"12": ["lh", {"5": ["get", "(rating_line_height)", [{"6": "rating_line_height"}]]}]}, {"12": ["ls", {"5": ["get", "(rating_letter_spacing)", [{"6": "rating_letter_spacing"}]]}]}, {"12": ["ta", {"5": ["get", "(rating_text_align)", [{"6": "rating_text_align"}]]}]}, {"12": ["td", {"5": ["get", "(rating_text_decoration)", [{"6": "rating_text_decoration"}]]}]}, {"12": ["tt", {"5": ["get", "(rating_text_transform)", [{"6": "rating_text_transform"}]]}]}]]}, {"8": ["text-shadow", [{"12": ["dimensions", {"5": ["get", "(rating_text_shadow_dimensions)", [{"6": "rating_text_shadow_dimensions"}]]}]}, {"12": ["base", {"5": ["get", "(rating_text_shadow_color)", [{"6": "rating_text_shadow_color"}]]}]}]]}, {"8": ["text-color", [{"5": ["get", "(rating_text_color)", [{"6": "rating_text_color"}]]}]]}]]}]]}]]}, {"2": ["lottie", [], [{"3": [["&.x-lottie"], [{"8": ["changedmixin", [{"11": "auto"}, {"6": "lottie_width"}, {"11": "width"}]]}, {"7": [{"5": ["is-set", "(get(lottie_max_width), get-base(lottie_max_width))", [{"5": ["get", "(lottie_max_width)", [{"6": "lottie_max_width"}]]}, {"5": ["get-base", "(lottie_max_width)", [{"6": "lottie_max_width"}]]}]]}, [{"4": ["max-width", {"5": ["get", "(lottie_max_width)", [{"6": "lottie_max_width"}]]}, false]}]]}, {"8": ["margin", [{"5": ["get-base", "(lottie_margin)", [{"6": "lottie_margin"}]]}, {"5": ["get", "(lottie_margin)", [{"6": "lottie_margin"}]]}]]}, {"4": ["font-size", {"5": ["get", "(lottie_base_font_size)", [{"6": "lottie_base_font_size"}]]}, false]}]]}]]}]}, {"document": 1, "module": 2, "styleRule": 3, "assignProperty": 4, "call": 5, "primitive": 6, "if": 7, "include": 8, "dimension": 9, "operation": 10, "singleQuotedString": 11, "keywordArgument": 12}]