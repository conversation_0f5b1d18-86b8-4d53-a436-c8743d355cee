<?php

namespace Themeco\Cornerstone\Services;

class Social implements Service {

  public $options;

  public function get_social_share_options() {
    if ( ! isset( $this->options ) ) {
      $this->options = apply_filters( 'cs_social_share_options', $this->default_options() );
    }
    return $this->options;
  }

  public function get_default_share_type() {
    $options = $this->get_social_share_options();
    return ! empty( $options ) && isset( $options[0]['value'] ) ? $options[0]['value'] : '';
  }

  public function default_options() {
    return array(
      array( 'value' => 'facebook',  'label' => 'Facebook' ),
      array( 'value' => 'twitter',   'label' => 'X' ),
      array( 'value' => 'tiktok',    'label' => 'TikTok' ),
      array( 'value' => 'bluesky',   'label' => 'Bluesky' ),
      array( 'value' => 'linkedin',  'label' => 'LinkedIn' ),
      array( 'value' => 'pinterest', 'label' => 'Pinterest' ),
      array( 'value' => 'reddit',    'label' => 'Reddit' ),
      array( 'value' => 'email',     'label' => __( 'Email', 'cornerstone' ) ),
    );
  }

  public function get_share_url( $type='facebook' ) {

    global $wp;

    $share_url = is_singular() ? get_permalink() : home_url( ($wp->request) ? $wp->request : '' );

    return urlencode( apply_filters('cs_social_share_url', $share_url, $type ) );

  }

  public function get_share_title() {
    if (is_singular()) {
      return get_the_title();
    }

    $pageForPosts = get_post( get_option( 'page_for_posts' ) );

    if (empty($pageForPosts)) {
      return '';
    }

    return apply_filters( 'the_title', $pageForPosts->post_title, $pageForPosts->ID );
  }

  public function get_share_image() {

    if ( function_exists( 'x_get_featured_image_with_fallback_url' ) ) {
      return urlencode( x_get_featured_image_with_fallback_url() );
    }

    $share_image_info = wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' );

    if ( $share_image_info ) {
      return urlencode( $share_image_info[0] );
    }

    return '';

  }

  public function make_popup( $name = 'popupShare', $url = '', $args = [] ) {

    $args = wp_parse_args( $args, array(
      'width'      => 500,
      'height'     => 500,
      'resizable'  => 0,
      'toolbar'    => 0,
      'menubar'    => 0,
      'status'     => 0,
      'location'   => 0,
      'scrollbars' => 0
    ));

    $arg_list = array();

    foreach ($args as $arg => $value ) {
      $arg_list[] = "$arg=$value";
    }

    $arg_string = implode( ', ', $arg_list );

    return "window.open('$url', '$name', '$arg_string'); return false;";
  }

  public function setup_atts( $atts, $type, $title ) {

    $atts['href'] = '#';

    $share_url = $this->get_share_url( $type );
    $title = esc_attr( cs_dynamic_content( $title ? $title : $this->get_share_title() ) );

    $share_title = str_replace(
      array(
        '%26%23038%3B',   // &
        '%26%238217%3B',  // '
        '%26lt%3B',       // <
        '%26gt%3B',       // >
      ),
      array( '%26', '%27', '%3C', '%3E' ),
      rawurlencode( $title )
    );


    switch ($type) {
      case 'facebook' :
        $atts['onclick'] = $this->make_popup( 'popupFacebook', "http://www.facebook.com/sharer.php?u={$share_url}&amp;t={$share_title}", array( 'height' => 400, 'width' => 650 ) );
        break;
      case 'twitter':
        $atts['onclick'] = $this->make_popup('popupTwitter', "https://twitter.com/intent/tweet?text={$share_title}&amp;url={$share_url}", array('height' => 400, 'width' => 650));
        break;
      case 'bluesky':
        $atts['onclick'] = $this->make_popup('popupBluesky', "https://bsky.app/intent/compose?text={$share_title}%20{$share_url}", array('height' => 400, 'width' => 650));
        break;
      case 'tiktok':
        $atts['onclick'] = $this->make_popup('popupTikTok', "https://www.tiktok.com/upload?url={$share_url}", array('height' => 600, 'width' => 800));
        break;
      case 'google-plus' :
        $atts['onclick'] = $this->make_popup( 'popupGooglePlus', "https://plus.google.com/share?url={$share_url}", array( 'height' => 226, 'width' => 650 ) );
        break;
      case 'linkedin' :
        $share_content = urlencode( cs_get_excerpt_for_social() );
        $share_source  = urlencode( get_bloginfo( 'name' ) );
        $atts['onclick'] = $this->make_popup( 'popupLinkedIn', "http://www.linkedin.com/shareArticle?mini=true&amp;url={$share_url}&amp;title={$share_title}&amp;summary={$share_content}&amp;source={$share_source}", array( 'height' => 480, 'width' => 610 ) );
        break;
      case 'pinterest' :
        $share_image = $this->get_share_image();
        $atts['onclick'] = $this->make_popup( 'popupPinterest', "http://pinterest.com/pin/create/button/?url={$share_url}&amp;media={$share_image}&amp;description={$share_title}", array( 'height' => 265, 'width' => 750 ) );
        break;
      case 'reddit' :
        $atts['onclick'] = $this->make_popup( 'popupReddit', "http://www.reddit.com/submit?url={$share_url}", array( 'height' => 450, 'width' => 875 ) );
        break;
      case 'email' :
        $atts['href'] = "mailto:?subject=$share_title&amp;body=$share_url";
        break;
    }

    return apply_filters( 'cs_social_share_atts', $atts, $type, $title );
  }



}
