[{"1": [{"2": ["theme-options", [], [{"3": ["--x-root-background-color", {"4": ["global-color", "(get(root_background_color))", [{"4": ["get", "(root_background_color)", [{"5": "root_background_color"}]]}]]}, false]}, {"3": ["--x-container-width", {"4": ["get", "(container_width)", [{"5": "container_width"}]]}, false]}, {"3": ["--x-container-max-width", {"4": ["get", "(container_max_width)", [{"5": "container_max_width"}]]}, false]}, {"3": ["--x-a-color", {"4": ["global-color", "(get(a_color))", [{"4": ["get", "(a_color)", [{"6": ["a", "_color"]}]]}]]}, false]}, {"3": ["--x-a-text-decoration", {"7": [{"4": ["get", "(a_text_decoration_line)", [{"6": ["a", "_text_decoration_line"]}]]}, {"4": ["get", "(a_text_decoration_style)", [{"6": ["a", "_text_decoration_style"]}]]}, {"4": ["get", "(a_text_decoration_thickness)", [{"6": ["a", "_text_decoration_thickness"]}]]}, {"4": ["get", "(a_text_decoration_color)", [{"6": ["a", "_text_decoration_color"]}]]}]}, false]}, {"3": ["--x-a-text-underline-offset", {"4": ["get", "(a_text_underline_offset)", [{"6": ["a", "_text_underline_offset"]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-a-int-color"}]}]}, {"9": ["base", {"4": ["get", "(a_color)", [{"6": ["a", "_color"]}]]}]}, {"9": ["alt", {"4": ["get", "(a_color_alt)", [{"6": ["a", "_color_alt"]}]]}]}]]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-a-int-text-decoration-color"}]}]}, {"9": ["base", {"4": ["get", "(a_text_decoration_color)", [{"6": ["a", "_text_decoration_color"]}]]}]}, {"9": ["alt", {"4": ["get", "(a_text_decoration_color_alt)", [{"6": ["a", "_text_decoration_color_alt"]}]]}]}]]}, {"3": ["--x-root-color", {"4": ["global-color", "(get(root_color))", [{"4": ["get", "(root_color)", [{"5": "root_color"}]]}]]}, false]}, {"3": ["--x-root-font-family", {"4": ["global-ff", "(get(root_font_family))", [{"4": ["get", "(root_font_family)", [{"5": "root_font_family"}]]}]]}, false]}, {"3": ["--x-root-font-size", {"4": ["get", "(root_font_size)", [{"5": "root_font_size"}]]}, false]}, {"3": ["--x-root-font-style", {"4": ["get", "(root_font_style)", [{"5": "root_font_style"}]]}, false]}, {"3": ["--x-root-font-weight", {"4": ["global-fw", "(get(root_font_family), get(root_font_weight))", [{"4": ["get", "(root_font_family)", [{"5": "root_font_family"}]]}, {"4": ["get", "(root_font_weight)", [{"5": "root_font_weight"}]]}]]}, false]}, {"3": ["--x-root-letter-spacing", {"4": ["get", "(root_letter_spacing)", [{"5": "root_letter_spacing"}]]}, false]}, {"3": ["--x-root-line-height", {"4": ["get", "(root_line_height)", [{"5": "root_line_height"}]]}, false]}, {"3": ["--x-h1-color", {"4": ["global-color", "(get(h1_color))", [{"4": ["get", "(h1_color)", [{"5": "h1_color"}]]}]]}, false]}, {"3": ["--x-h1-font-family", {"4": ["global-ff", "(get(h1_font_family))", [{"4": ["get", "(h1_font_family)", [{"5": "h1_font_family"}]]}]]}, false]}, {"3": ["--x-h1-font-size", {"4": ["get", "(h1_font_size)", [{"5": "h1_font_size"}]]}, false]}, {"3": ["--x-h1-font-style", {"4": ["get", "(h1_font_style)", [{"5": "h1_font_style"}]]}, false]}, {"3": ["--x-h1-font-weight", {"4": ["global-fw", "(get(h1_font_family), get(h1_font_weight))", [{"4": ["get", "(h1_font_family)", [{"5": "h1_font_family"}]]}, {"4": ["get", "(h1_font_weight)", [{"5": "h1_font_weight"}]]}]]}, false]}, {"3": ["--x-h1-letter-spacing", {"4": ["get", "(h1_letter_spacing)", [{"5": "h1_letter_spacing"}]]}, false]}, {"3": ["--x-h1-line-height", {"4": ["get", "(h1_line_height)", [{"5": "h1_line_height"}]]}, false]}, {"3": ["--x-h1-text-transform", {"4": ["get", "(h1_text_transform)", [{"5": "h1_text_transform"}]]}, false]}, {"3": ["--x-h2-color", {"4": ["global-color", "(get(h2_color))", [{"4": ["get", "(h2_color)", [{"5": "h2_color"}]]}]]}, false]}, {"3": ["--x-h2-font-family", {"4": ["global-ff", "(get(h2_font_family))", [{"4": ["get", "(h2_font_family)", [{"5": "h2_font_family"}]]}]]}, false]}, {"3": ["--x-h2-font-size", {"4": ["get", "(h2_font_size)", [{"5": "h2_font_size"}]]}, false]}, {"3": ["--x-h2-font-style", {"4": ["get", "(h2_font_style)", [{"5": "h2_font_style"}]]}, false]}, {"3": ["--x-h2-font-weight", {"4": ["global-fw", "(get(h2_font_family), get(h2_font_weight))", [{"4": ["get", "(h2_font_family)", [{"5": "h2_font_family"}]]}, {"4": ["get", "(h2_font_weight)", [{"5": "h2_font_weight"}]]}]]}, false]}, {"3": ["--x-h2-letter-spacing", {"4": ["get", "(h2_letter_spacing)", [{"5": "h2_letter_spacing"}]]}, false]}, {"3": ["--x-h2-line-height", {"4": ["get", "(h2_line_height)", [{"5": "h2_line_height"}]]}, false]}, {"3": ["--x-h2-text-transform", {"4": ["get", "(h2_text_transform)", [{"5": "h2_text_transform"}]]}, false]}, {"3": ["--x-h3-color", {"4": ["global-color", "(get(h3_color))", [{"4": ["get", "(h3_color)", [{"5": "h3_color"}]]}]]}, false]}, {"3": ["--x-h3-font-family", {"4": ["global-ff", "(get(h3_font_family))", [{"4": ["get", "(h3_font_family)", [{"5": "h3_font_family"}]]}]]}, false]}, {"3": ["--x-h3-font-size", {"4": ["get", "(h3_font_size)", [{"5": "h3_font_size"}]]}, false]}, {"3": ["--x-h3-font-style", {"4": ["get", "(h3_font_style)", [{"5": "h3_font_style"}]]}, false]}, {"3": ["--x-h3-font-weight", {"4": ["global-fw", "(get(h3_font_family), get(h3_font_weight))", [{"4": ["get", "(h3_font_family)", [{"5": "h3_font_family"}]]}, {"4": ["get", "(h3_font_weight)", [{"5": "h3_font_weight"}]]}]]}, false]}, {"3": ["--x-h3-letter-spacing", {"4": ["get", "(h3_letter_spacing)", [{"5": "h3_letter_spacing"}]]}, false]}, {"3": ["--x-h3-line-height", {"4": ["get", "(h3_line_height)", [{"5": "h3_line_height"}]]}, false]}, {"3": ["--x-h3-text-transform", {"4": ["get", "(h3_text_transform)", [{"5": "h3_text_transform"}]]}, false]}, {"3": ["--x-h4-color", {"4": ["global-color", "(get(h4_color))", [{"4": ["get", "(h4_color)", [{"5": "h4_color"}]]}]]}, false]}, {"3": ["--x-h4-font-family", {"4": ["global-ff", "(get(h4_font_family))", [{"4": ["get", "(h4_font_family)", [{"5": "h4_font_family"}]]}]]}, false]}, {"3": ["--x-h4-font-size", {"4": ["get", "(h4_font_size)", [{"5": "h4_font_size"}]]}, false]}, {"3": ["--x-h4-font-style", {"4": ["get", "(h4_font_style)", [{"5": "h4_font_style"}]]}, false]}, {"3": ["--x-h4-font-weight", {"4": ["global-fw", "(get(h4_font_family), get(h4_font_weight))", [{"4": ["get", "(h4_font_family)", [{"5": "h4_font_family"}]]}, {"4": ["get", "(h4_font_weight)", [{"5": "h4_font_weight"}]]}]]}, false]}, {"3": ["--x-h4-letter-spacing", {"4": ["get", "(h4_letter_spacing)", [{"5": "h4_letter_spacing"}]]}, false]}, {"3": ["--x-h4-line-height", {"4": ["get", "(h4_line_height)", [{"5": "h4_line_height"}]]}, false]}, {"3": ["--x-h4-text-transform", {"4": ["get", "(h4_text_transform)", [{"5": "h4_text_transform"}]]}, false]}, {"3": ["--x-h5-color", {"4": ["global-color", "(get(h5_color))", [{"4": ["get", "(h5_color)", [{"5": "h5_color"}]]}]]}, false]}, {"3": ["--x-h5-font-family", {"4": ["global-ff", "(get(h5_font_family))", [{"4": ["get", "(h5_font_family)", [{"5": "h5_font_family"}]]}]]}, false]}, {"3": ["--x-h5-font-size", {"4": ["get", "(h5_font_size)", [{"5": "h5_font_size"}]]}, false]}, {"3": ["--x-h5-font-style", {"4": ["get", "(h5_font_style)", [{"5": "h5_font_style"}]]}, false]}, {"3": ["--x-h5-font-weight", {"4": ["global-fw", "(get(h5_font_family), get(h5_font_weight))", [{"4": ["get", "(h5_font_family)", [{"5": "h5_font_family"}]]}, {"4": ["get", "(h5_font_weight)", [{"5": "h5_font_weight"}]]}]]}, false]}, {"3": ["--x-h5-letter-spacing", {"4": ["get", "(h5_letter_spacing)", [{"5": "h5_letter_spacing"}]]}, false]}, {"3": ["--x-h5-line-height", {"4": ["get", "(h5_line_height)", [{"5": "h5_line_height"}]]}, false]}, {"3": ["--x-h5-text-transform", {"4": ["get", "(h5_text_transform)", [{"5": "h5_text_transform"}]]}, false]}, {"3": ["--x-h6-color", {"4": ["global-color", "(get(h6_color))", [{"4": ["get", "(h6_color)", [{"5": "h6_color"}]]}]]}, false]}, {"3": ["--x-h6-font-family", {"4": ["global-ff", "(get(h6_font_family))", [{"4": ["get", "(h6_font_family)", [{"5": "h6_font_family"}]]}]]}, false]}, {"3": ["--x-h6-font-size", {"4": ["get", "(h6_font_size)", [{"5": "h6_font_size"}]]}, false]}, {"3": ["--x-h6-font-style", {"4": ["get", "(h6_font_style)", [{"5": "h6_font_style"}]]}, false]}, {"3": ["--x-h6-font-weight", {"4": ["global-fw", "(get(h6_font_family), get(h6_font_weight))", [{"4": ["get", "(h6_font_family)", [{"5": "h6_font_family"}]]}, {"4": ["get", "(h6_font_weight)", [{"5": "h6_font_weight"}]]}]]}, false]}, {"3": ["--x-h6-letter-spacing", {"4": ["get", "(h6_letter_spacing)", [{"5": "h6_letter_spacing"}]]}, false]}, {"3": ["--x-h6-line-height", {"4": ["get", "(h6_line_height)", [{"5": "h6_line_height"}]]}, false]}, {"3": ["--x-h6-text-transform", {"4": ["get", "(h6_text_transform)", [{"5": "h6_text_transform"}]]}, false]}, {"3": ["--x-label-color", {"4": ["global-color", "(get(label_color))", [{"4": ["get", "(label_color)", [{"5": "label_color"}]]}]]}, false]}, {"3": ["--x-label-font-family", {"4": ["global-ff", "(get(label_font_family))", [{"4": ["get", "(label_font_family)", [{"5": "label_font_family"}]]}]]}, false]}, {"3": ["--x-label-font-size", {"4": ["get", "(label_font_size)", [{"5": "label_font_size"}]]}, false]}, {"3": ["--x-label-font-style", {"4": ["get", "(label_font_style)", [{"5": "label_font_style"}]]}, false]}, {"3": ["--x-label-font-weight", {"4": ["global-fw", "(get(label_font_family), get(label_font_weight))", [{"4": ["get", "(label_font_family)", [{"5": "label_font_family"}]]}, {"4": ["get", "(label_font_weight)", [{"5": "label_font_weight"}]]}]]}, false]}, {"3": ["--x-label-letter-spacing", {"4": ["get", "(label_letter_spacing)", [{"5": "label_letter_spacing"}]]}, false]}, {"3": ["--x-label-line-height", {"4": ["get", "(label_line_height)", [{"5": "label_line_height"}]]}, false]}, {"3": ["--x-label-text-transform", {"4": ["get", "(label_text_transform)", [{"5": "label_text_transform"}]]}, false]}, {"3": ["--x-content-copy-spacing", {"4": ["get", "(content_copy_spacing)", [{"5": "content_copy_spacing"}]]}, false]}, {"3": ["--x-content-h-margin-top", {"4": ["get", "(content_h_margin_top)", [{"5": "content_h_margin_top"}]]}, false]}, {"3": ["--x-content-h-margin-bottom", {"4": ["get", "(content_h_margin_bottom)", [{"5": "content_h_margin_bottom"}]]}, false]}, {"3": ["--x-content-ol-padding-inline-start", {"4": ["get", "(content_ol_padding_inline_start)", [{"5": "content_ol_padding_inline_start"}]]}, false]}, {"3": ["--x-content-ul-padding-inline-start", {"4": ["get", "(content_ul_padding_inline_start)", [{"5": "content_ul_padding_inline_start"}]]}, false]}, {"3": ["--x-content-li-spacing", {"4": ["get", "(content_li_spacing)", [{"5": "content_li_spacing"}]]}, false]}, {"3": ["--x-content-media-spacing", {"4": ["get", "(content_media_spacing)", [{"5": "content_media_spacing"}]]}, false]}, {"3": ["--x-input-background-color", {"4": ["global-color", "(get(input_background_color))", [{"4": ["get", "(input_background_color)", [{"5": "input_background_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-input-int-background-color"}]}]}, {"9": ["base", {"4": ["get", "(input_background_color)", [{"5": "input_background_color"}]]}]}, {"9": ["alt", {"4": ["get", "(input_background_color_alt)", [{"5": "input_background_color_alt"}]]}]}]]}, {"3": ["--x-input-color", {"4": ["global-color", "(get(input_color))", [{"4": ["get", "(input_color)", [{"5": "input_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-input-int-color"}]}]}, {"9": ["base", {"4": ["get", "(input_color)", [{"5": "input_color"}]]}]}, {"9": ["alt", {"4": ["get", "(input_color_alt)", [{"5": "input_color_alt"}]]}]}]]}, {"3": ["--x-input-font-family", {"4": ["global-ff", "(get(input_font_family))", [{"4": ["get", "(input_font_family)", [{"5": "input_font_family"}]]}]]}, false]}, {"3": ["--x-input-font-size", {"4": ["get", "(input_font_size)", [{"5": "input_font_size"}]]}, false]}, {"3": ["--x-input-font-style", {"4": ["get", "(input_font_style)", [{"5": "input_font_style"}]]}, false]}, {"3": ["--x-input-font-weight", {"4": ["global-fw", "(get(input_font_family), get(input_font_weight))", [{"4": ["get", "(input_font_family)", [{"5": "input_font_family"}]]}, {"4": ["get", "(input_font_weight)", [{"5": "input_font_weight"}]]}]]}, false]}, {"3": ["--x-input-letter-spacing", {"4": ["get", "(input_letter_spacing)", [{"5": "input_letter_spacing"}]]}, false]}, {"3": ["--x-input-line-height", {"4": ["get", "(input_line_height)", [{"5": "input_line_height"}]]}, false]}, {"3": ["--x-input-text-align", {"4": ["get", "(input_text_align)", [{"5": "input_text_align"}]]}, false]}, {"3": ["--x-input-text-transform", {"4": ["get", "(input_text_transform)", [{"5": "input_text_transform"}]]}, false]}, {"3": ["--x-placeholder-opacity", {"4": ["get", "(input_placeholder_opacity)", [{"5": "input_placeholder_opacity"}]]}, false]}, {"3": ["--x-placeholder-int-opacity", {"4": ["get", "(input_placeholder_opacity_alt)", [{"5": "input_placeholder_opacity_alt"}]]}, false]}, {"3": ["--x-input-outline-width", {"4": ["get", "(input_outline_width)", [{"5": "input_outline_width"}]]}, false]}, {"3": ["--x-input-outline-color", {"4": ["global-color", "(get(input_outline_color))", [{"4": ["get", "(input_outline_color)", [{"5": "input_outline_color"}]]}]]}, false]}, {"3": ["--x-input-padding-x", {"4": ["get", "(input_padding_x)", [{"5": "input_padding_x"}]]}, false]}, {"3": ["--x-input-padding-y-extra", {"4": ["get", "(input_padding_y_extra)", [{"5": "input_padding_y_extra"}]]}, false]}, {"3": ["--x-input-border-width", {"4": ["get", "(input_border_width)", [{"5": "input_border_width"}]]}, false]}, {"3": ["--x-input-border-style", {"4": ["get", "(input_border_style)", [{"5": "input_border_style"}]]}, false]}, {"3": ["--x-input-border-radius", {"4": ["get", "(input_border_radius)", [{"5": "input_border_radius"}]]}, false]}, {"3": ["--x-input-border-color", {"4": ["global-color", "(get(input_border_color))", [{"4": ["get", "(input_border_color)", [{"5": "input_border_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-input-int-border-color"}]}]}, {"9": ["base", {"4": ["get", "(input_border_color)", [{"5": "input_border_color"}]]}]}, {"9": ["alt", {"4": ["get", "(input_border_color_alt)", [{"5": "input_border_color_alt"}]]}]}]]}, {"3": ["--x-input-box-shadow", {"7": [{"4": ["get", "(input_box_shadow_x)", [{"5": "input_box_shadow_x"}]]}, {"4": ["get", "(input_box_shadow_y)", [{"5": "input_box_shadow_y"}]]}, {"4": ["get", "(input_box_shadow_blur)", [{"5": "input_box_shadow_blur"}]]}, {"4": ["get", "(input_box_shadow_spread)", [{"5": "input_box_shadow_spread"}]]}, {"4": ["global-color", "(get(input_box_shadow_color))", [{"4": ["get", "(input_box_shadow_color)", [{"5": "input_box_shadow_color"}]]}]]}]}, false]}, {"8": ["theme-options-alt-box-shadow-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-input-int-box-shadow"}]}]}, {"9": ["x", {"4": ["get", "(input_box_shadow_x)", [{"5": "input_box_shadow_x"}]]}]}, {"9": ["y", {"4": ["get", "(input_box_shadow_y)", [{"5": "input_box_shadow_y"}]]}]}, {"9": ["blur", {"4": ["get", "(input_box_shadow_blur)", [{"5": "input_box_shadow_blur"}]]}]}, {"9": ["spread", {"4": ["get", "(input_box_shadow_spread)", [{"5": "input_box_shadow_spread"}]]}]}, {"9": ["base", {"4": ["global-color", "(get(input_box_shadow_color))", [{"4": ["get", "(input_box_shadow_color)", [{"5": "input_box_shadow_color"}]]}]]}]}, {"9": ["alt", {"4": ["global-color", "(get(input_box_shadow_color_alt))", [{"4": ["get", "(input_box_shadow_color_alt)", [{"5": "input_box_shadow_color_alt"}]]}]]}]}]]}, {"3": ["--x-select-indicator-image", {"4": ["svg-in-url", "( \"select-arrowheads\", global-color(get(input_indicator_color)) )", [{"11": "select-arrowheads"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-select-indicator-hover-image", {"4": ["svg-in-url", "( \"select-arrowheads\", global-color(get(input_indicator_color)) )", [{"11": "select-arrowheads"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-select-indicator-focus-image", {"4": ["svg-in-url", "( \"select-arrowheads\", global-color(get(input_indicator_color_alt)) )", [{"11": "select-arrowheads"}, {"4": ["global-color", "(get(input_indicator_color_alt))", [{"4": ["get", "(input_indicator_color_alt)", [{"5": "input_indicator_color_alt"}]]}]]}]]}, false]}, {"3": ["--x-select-indicator-size", {"4": ["get", "(input_indicator_size)", [{"5": "input_indicator_size"}]]}, false]}, {"3": ["--x-select-indicator-spacing-x", {"4": ["get", "(input_indicator_spacing_x)", [{"5": "input_indicator_spacing_x"}]]}, false]}, {"3": ["--x-date-indicator-image", {"4": ["svg-in-url", "( \"calendar-confirm\", global-color(get(input_indicator_color)) )", [{"11": "calendar-confirm"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-date-indicator-hover-image", {"4": ["svg-in-url", "( \"calendar-confirm\", global-color(get(input_indicator_color)) )", [{"11": "calendar-confirm"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-date-indicator-focus-image", {"4": ["svg-in-url", "( \"calendar-confirm\", global-color(get(input_indicator_color_alt)) )", [{"11": "calendar-confirm"}, {"4": ["global-color", "(get(input_indicator_color_alt))", [{"4": ["get", "(input_indicator_color_alt)", [{"5": "input_indicator_color_alt"}]]}]]}]]}, false]}, {"3": ["--x-date-indicator-size", {"4": ["get", "(input_indicator_size)", [{"5": "input_indicator_size"}]]}, false]}, {"3": ["--x-date-indicator-spacing-x", {"4": ["get", "(input_indicator_spacing_x)", [{"5": "input_indicator_spacing_x"}]]}, false]}, {"3": ["--x-time-indicator-image", {"4": ["svg-in-url", "( \"time-watch-circle\", global-color(get(input_indicator_color)) )", [{"11": "time-watch-circle"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-time-indicator-hover-image", {"4": ["svg-in-url", "( \"time-watch-circle\", global-color(get(input_indicator_color)) )", [{"11": "time-watch-circle"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-time-indicator-focus-image", {"4": ["svg-in-url", "( \"time-watch-circle\", global-color(get(input_indicator_color_alt)) )", [{"11": "time-watch-circle"}, {"4": ["global-color", "(get(input_indicator_color_alt))", [{"4": ["get", "(input_indicator_color_alt)", [{"5": "input_indicator_color_alt"}]]}]]}]]}, false]}, {"3": ["--x-time-indicator-size", {"4": ["get", "(input_indicator_size)", [{"5": "input_indicator_size"}]]}, false]}, {"3": ["--x-time-indicator-spacing-x", {"4": ["get", "(input_indicator_spacing_x)", [{"5": "input_indicator_spacing_x"}]]}, false]}, {"3": ["--x-search-indicator-image", {"4": ["svg-in-url", "( \"close-thin\", global-color(get(input_indicator_color)) )", [{"11": "close-thin"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-search-indicator-hover-image", {"4": ["svg-in-url", "( \"close-thin\", global-color(get(input_indicator_color)) )", [{"11": "close-thin"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-search-indicator-focus-image", {"4": ["svg-in-url", "( \"close-thin\", global-color(get(input_indicator_color_alt)) )", [{"11": "close-thin"}, {"4": ["global-color", "(get(input_indicator_color_alt))", [{"4": ["get", "(input_indicator_color_alt)", [{"5": "input_indicator_color_alt"}]]}]]}]]}, false]}, {"3": ["--x-search-indicator-size", {"4": ["get", "(input_indicator_size)", [{"5": "input_indicator_size"}]]}, false]}, {"3": ["--x-search-indicator-spacing-x", {"4": ["get", "(input_indicator_spacing_x)", [{"5": "input_indicator_spacing_x"}]]}, false]}, {"3": ["--x-number-indicator-image", {"4": ["svg-in-url", "( \"select-triangles\", global-color(get(input_indicator_color)) )", [{"11": "select-triangles"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-number-indicator-hover-image", {"4": ["svg-in-url", "( \"select-triangles\", global-color(get(input_indicator_color)) )", [{"11": "select-triangles"}, {"4": ["global-color", "(get(input_indicator_color))", [{"4": ["get", "(input_indicator_color)", [{"5": "input_indicator_color"}]]}]]}]]}, false]}, {"3": ["--x-number-indicator-focus-image", {"4": ["svg-in-url", "( \"select-triangles\", global-color(get(input_indicator_color_alt)) )", [{"11": "select-triangles"}, {"4": ["global-color", "(get(input_indicator_color_alt))", [{"4": ["get", "(input_indicator_color_alt)", [{"5": "input_indicator_color_alt"}]]}]]}]]}, false]}, {"3": ["--x-number-indicator-size", {"4": ["get", "(input_indicator_size)", [{"5": "input_indicator_size"}]]}, false]}, {"3": ["--x-number-indicator-spacing-x", {"4": ["get", "(input_indicator_spacing_x)", [{"5": "input_indicator_spacing_x"}]]}, false]}, {"3": ["--x-rc-font-size", {"4": ["get", "(rc_font_size)", [{"5": "rc_font_size"}]]}, false]}, {"3": ["--x-rc-background-color", {"4": ["global-color", "(get(rc_background_color))", [{"4": ["get", "(rc_background_color)", [{"5": "rc_background_color"}]]}]]}, false]}, {"3": ["--x-rc-int-background-color", {"4": ["global-color", "(get(rc_background_color))", [{"4": ["get", "(rc_background_color)", [{"5": "rc_background_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-rc-checked-background-color"}]}]}, {"9": ["base", {"4": ["get", "(rc_background_color)", [{"5": "rc_background_color"}]]}]}, {"9": ["alt", {"4": ["get", "(rc_background_color_alt)", [{"5": "rc_background_color_alt"}]]}]}]]}, {"3": ["--x-rc-outline-width", {"4": ["get", "(rc_outline_width)", [{"5": "rc_outline_width"}]]}, false]}, {"3": ["--x-rc-outline-color", {"4": ["global-color", "(get(rc_outline_color))", [{"4": ["get", "(rc_outline_color)", [{"5": "rc_outline_color"}]]}]]}, false]}, {"3": ["--x-radio-marker", {"4": ["svg-in-url", "( \"radio\", global-color(get(rc_marker_color)) )", [{"11": "radio"}, {"4": ["global-color", "(get(rc_marker_color))", [{"4": ["get", "(rc_marker_color)", [{"5": "rc_marker_color"}]]}]]}]]}, false]}, {"3": ["--x-radio-marker-inset", {"4": ["get", "(radio_marker_inset)", [{"5": "radio_marker_inset"}]]}, false]}, {"3": ["--x-checkbox-marker", {"4": ["svg-in-url", "( \"check\", global-color(get(rc_marker_color)) )", [{"11": "check"}, {"4": ["global-color", "(get(rc_marker_color))", [{"4": ["get", "(rc_marker_color)", [{"5": "rc_marker_color"}]]}]]}]]}, false]}, {"3": ["--x-checkbox-marker-inset", {"4": ["get", "(checkbox_marker_inset)", [{"5": "checkbox_marker_inset"}]]}, false]}, {"3": ["--x-rc-border-width", {"4": ["get", "(rc_border_width)", [{"5": "rc_border_width"}]]}, false]}, {"3": ["--x-rc-border-style", {"4": ["get", "(rc_border_style)", [{"5": "rc_border_style"}]]}, false]}, {"3": ["--x-checkbox-border-radius", {"4": ["get", "(rc_border_radius)", [{"5": "rc_border_radius"}]]}, false]}, {"3": ["--x-rc-border-color", {"4": ["global-color", "(get(rc_border_color))", [{"4": ["get", "(rc_border_color)", [{"5": "rc_border_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-rc-int-border-color"}]}]}, {"9": ["base", {"4": ["get", "(rc_border_color)", [{"5": "rc_border_color"}]]}]}, {"9": ["alt", {"4": ["get", "(rc_border_color_alt)", [{"5": "rc_border_color_alt"}]]}]}]]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-rc-checked-border-color"}]}]}, {"9": ["base", {"4": ["get", "(rc_border_color)", [{"5": "rc_border_color"}]]}]}, {"9": ["alt", {"4": ["get", "(rc_border_color_alt)", [{"5": "rc_border_color_alt"}]]}]}]]}, {"3": ["--x-rc-box-shadow", {"7": [{"4": ["get", "(rc_box_shadow_x)", [{"5": "rc_box_shadow_x"}]]}, {"4": ["get", "(rc_box_shadow_y)", [{"5": "rc_box_shadow_y"}]]}, {"4": ["get", "(rc_box_shadow_blur)", [{"5": "rc_box_shadow_blur"}]]}, {"4": ["get", "(rc_box_shadow_spread)", [{"5": "rc_box_shadow_spread"}]]}, {"4": ["global-color", "(get(rc_box_shadow_color))", [{"4": ["get", "(rc_box_shadow_color)", [{"5": "rc_box_shadow_color"}]]}]]}]}, false]}, {"8": ["theme-options-alt-box-shadow-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-rc-int-box-shadow"}]}]}, {"9": ["x", {"4": ["get", "(rc_box_shadow_x)", [{"5": "rc_box_shadow_x"}]]}]}, {"9": ["y", {"4": ["get", "(rc_box_shadow_y)", [{"5": "rc_box_shadow_y"}]]}]}, {"9": ["blur", {"4": ["get", "(rc_box_shadow_blur)", [{"5": "rc_box_shadow_blur"}]]}]}, {"9": ["spread", {"4": ["get", "(rc_box_shadow_spread)", [{"5": "rc_box_shadow_spread"}]]}]}, {"9": ["base", {"4": ["global-color", "(get(rc_box_shadow_color))", [{"4": ["get", "(rc_box_shadow_color)", [{"5": "rc_box_shadow_color"}]]}]]}]}, {"9": ["alt", {"4": ["global-color", "(get(rc_box_shadow_color_alt))", [{"4": ["get", "(rc_box_shadow_color_alt)", [{"5": "rc_box_shadow_color_alt"}]]}]]}]}]]}, {"8": ["theme-options-alt-box-shadow-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-rc-checked-box-shadow"}]}]}, {"9": ["x", {"4": ["get", "(rc_box_shadow_x)", [{"5": "rc_box_shadow_x"}]]}]}, {"9": ["y", {"4": ["get", "(rc_box_shadow_y)", [{"5": "rc_box_shadow_y"}]]}]}, {"9": ["blur", {"4": ["get", "(rc_box_shadow_blur)", [{"5": "rc_box_shadow_blur"}]]}]}, {"9": ["spread", {"4": ["get", "(rc_box_shadow_spread)", [{"5": "rc_box_shadow_spread"}]]}]}, {"9": ["base", {"4": ["global-color", "(get(rc_box_shadow_color))", [{"4": ["get", "(rc_box_shadow_color)", [{"5": "rc_box_shadow_color"}]]}]]}]}, {"9": ["alt", {"4": ["global-color", "(get(rc_box_shadow_color_alt))", [{"4": ["get", "(rc_box_shadow_color_alt)", [{"5": "rc_box_shadow_color_alt"}]]}]]}]}]]}, {"3": ["--x-submit-background-color", {"4": ["global-color", "(get(submit_background_color))", [{"4": ["get", "(submit_background_color)", [{"5": "submit_background_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-submit-int-background-color"}]}]}, {"9": ["base", {"4": ["get", "(submit_background_color)", [{"5": "submit_background_color"}]]}]}, {"9": ["alt", {"4": ["get", "(submit_background_color_alt)", [{"5": "submit_background_color_alt"}]]}]}]]}, {"3": ["--x-submit-min-width", {"4": ["get", "(submit_min_width)", [{"5": "submit_min_width"}]]}, false]}, {"3": ["--x-submit-padding-x-extra", {"4": ["get", "(submit_padding_x)", [{"5": "submit_padding_x"}]]}, false]}, {"3": ["--x-submit-color", {"4": ["global-color", "(get(submit_color))", [{"4": ["get", "(submit_color)", [{"5": "submit_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-submit-int-color"}]}]}, {"9": ["base", {"4": ["get", "(submit_color)", [{"5": "submit_color"}]]}]}, {"9": ["alt", {"4": ["get", "(submit_color_alt)", [{"5": "submit_color_alt"}]]}]}]]}, {"3": ["--x-submit-font-weight", {"4": ["global-fw", "(get(input_font_family), get(submit_font_weight))", [{"4": ["get", "(input_font_family)", [{"5": "input_font_family"}]]}, {"4": ["get", "(submit_font_weight)", [{"5": "submit_font_weight"}]]}]]}, false]}, {"3": ["--x-submit-text-align", {"4": ["get", "(submit_text_align)", [{"5": "submit_text_align"}]]}, false]}, {"3": ["--x-submit-outline-width", {"4": ["get", "(submit_outline_width)", [{"5": "submit_outline_width"}]]}, false]}, {"3": ["--x-submit-outline-color", {"4": ["global-color", "(get(submit_outline_color))", [{"4": ["get", "(submit_outline_color)", [{"5": "submit_outline_color"}]]}]]}, false]}, {"3": ["--x-submit-border-radius", {"4": ["get", "(submit_border_radius)", [{"5": "submit_border_radius"}]]}, false]}, {"3": ["--x-submit-border-color", {"4": ["global-color", "(get(submit_border_color))", [{"4": ["get", "(submit_border_color)", [{"5": "submit_border_color"}]]}]]}, false]}, {"8": ["theme-options-alt-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-submit-int-border-color"}]}]}, {"9": ["base", {"4": ["get", "(submit_border_color)", [{"5": "submit_border_color"}]]}]}, {"9": ["alt", {"4": ["get", "(submit_border_color_alt)", [{"5": "submit_border_color_alt"}]]}]}]]}, {"3": ["--x-submit-box-shadow", {"7": [{"4": ["get", "(submit_box_shadow_x)", [{"5": "submit_box_shadow_x"}]]}, {"4": ["get", "(submit_box_shadow_y)", [{"5": "submit_box_shadow_y"}]]}, {"4": ["get", "(submit_box_shadow_blur)", [{"5": "submit_box_shadow_blur"}]]}, {"4": ["get", "(submit_box_shadow_spread)", [{"5": "submit_box_shadow_spread"}]]}, {"4": ["global-color", "(get(submit_box_shadow_color))", [{"4": ["get", "(submit_box_shadow_color)", [{"5": "submit_box_shadow_color"}]]}]]}]}, false]}, {"8": ["theme-options-alt-box-shadow-color-fallback", [{"9": ["property", {"10": ["-", {"5": "-x-submit-int-box-shadow"}]}]}, {"9": ["x", {"4": ["get", "(submit_box_shadow_x)", [{"5": "submit_box_shadow_x"}]]}]}, {"9": ["y", {"4": ["get", "(submit_box_shadow_y)", [{"5": "submit_box_shadow_y"}]]}]}, {"9": ["blur", {"4": ["get", "(submit_box_shadow_blur)", [{"5": "submit_box_shadow_blur"}]]}]}, {"9": ["spread", {"4": ["get", "(submit_box_shadow_spread)", [{"5": "submit_box_shadow_spread"}]]}]}, {"9": ["base", {"4": ["global-color", "(get(submit_box_shadow_color))", [{"4": ["get", "(submit_box_shadow_color)", [{"5": "submit_box_shadow_color"}]]}]]}]}, {"9": ["alt", {"4": ["global-color", "(get(submit_box_shadow_color_alt))", [{"4": ["get", "(submit_box_shadow_color_alt)", [{"5": "submit_box_shadow_color_alt"}]]}]]}]}]]}, {"12": [[".x-container.max"], [{"3": ["max-width", {"4": ["var", "(--x-container-max-width)", [{"10": ["-", {"5": "-x-container-max-width"}]}]]}, false]}]]}, {"12": [[".x-container.width"], [{"3": ["width", {"4": ["var", "(--x-container-width)", [{"10": ["-", {"5": "-x-container-width"}]}]]}, false]}]]}]]}]}, {"document": 1, "module": 2, "assignProperty": 3, "call": 4, "primitive": 5, "dimension": 6, "list": 7, "include": 8, "keywordArgument": 9, "unary": 10, "doubleQuotedString": 11, "styleRule": 12}]