<?php

return [

  // Background
  // ----------

  'root_background_color' => cs_value( '#ffffff', 'style:color' ),


  // Container
  // ---------

  'container_width'     => cs_value( 'calc(100% - 2rem)' ),
  'container_max_width' => cs_value( '64rem' ),


  // Anchors
  // -------

  'a_color'                     => cs_value( '#0073e6', 'style:color' ),
  'a_color_alt'                 => cs_value( 'rgba(0, 115, 230, 0.88)', 'style:color' ),
  'a_text_decoration_line'      => cs_value( 'none' ),
  'a_text_decoration_style'     => cs_value( 'solid' ),
  'a_text_underline_offset'     => cs_value( '0.085em' ),
  'a_text_decoration_thickness' => cs_value( 'auto' ),
  'a_text_decoration_color'     => cs_value( '#0073e6', 'style:color' ),
  'a_text_decoration_color_alt' => cs_value( 'rgba(0, 115, 230, 0.88)', 'style:color' ),


  // Typography
  // ----------

  'root_color'           => cs_value( '#000000', 'style:color' ),
  'root_font_family'     => cs_value( 'system:helveticaneue', 'style:font-family' ),
  'root_font_size'       => cs_value( '18px' ),
  'root_font_style'      => cs_value( 'normal' ),
  'root_font_weight'     => cs_value( 'fw-normal', 'style:font-weight' ),
  'root_letter_spacing'  => cs_value( '0em' ),
  'root_line_height'     => cs_value( '1.6' ),

  'h1_color'             => cs_value( '#000000', 'style:color' ),
  'h1_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h1_font_size'         => cs_value( '3.815em' ),
  'h1_font_style'        => cs_value( 'inherit' ),
  'h1_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h1_letter_spacing'    => cs_value( 'inherit' ),
  'h1_line_height'       => cs_value( '1.115' ),
  'h1_text_transform'    => cs_value( 'inherit' ),

  'h2_color'             => cs_value( '#000000', 'style:color' ),
  'h2_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h2_font_size'         => cs_value( '3.052em' ),
  'h2_font_style'        => cs_value( 'inherit' ),
  'h2_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h2_letter_spacing'    => cs_value( 'inherit' ),
  'h2_line_height'       => cs_value( '1.125' ),
  'h2_text_transform'    => cs_value( 'inherit' ),

  'h3_color'             => cs_value( '#000000', 'style:color' ),
  'h3_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h3_font_size'         => cs_value( '2.441em' ),
  'h3_font_style'        => cs_value( 'inherit' ),
  'h3_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h3_letter_spacing'    => cs_value( 'inherit' ),
  'h3_line_height'       => cs_value( '1.15' ),
  'h3_text_transform'    => cs_value( 'inherit' ),

  'h4_color'             => cs_value( '#000000', 'style:color' ),
  'h4_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h4_font_size'         => cs_value( '1.953em' ),
  'h4_font_style'        => cs_value( 'inherit' ),
  'h4_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h4_letter_spacing'    => cs_value( 'inherit' ),
  'h4_line_height'       => cs_value( '1.2' ),
  'h4_text_transform'    => cs_value( 'inherit' ),

  'h5_color'             => cs_value( '#000000', 'style:color' ),
  'h5_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h5_font_size'         => cs_value( '1.563em' ),
  'h5_font_style'        => cs_value( 'inherit' ),
  'h5_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h5_letter_spacing'    => cs_value( 'inherit' ),
  'h5_line_height'       => cs_value( '1.25' ),
  'h5_text_transform'    => cs_value( 'inherit' ),

  'h6_color'             => cs_value( '#000000', 'style:color' ),
  'h6_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h6_font_size'         => cs_value( '1.25em' ),
  'h6_font_style'        => cs_value( 'inherit' ),
  'h6_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h6_letter_spacing'    => cs_value( 'inherit' ),
  'h6_line_height'       => cs_value( '1.265' ),
  'h6_text_transform'    => cs_value( 'inherit' ),

  'label_color'          => cs_value( '#000000', 'style:color' ),
  'label_font_family'    => cs_value( 'inherit', 'style:font-family' ),
  'label_font_size'      => cs_value( '0.8em' ),
  'label_font_style'     => cs_value( 'inherit' ),
  'label_font_weight'    => cs_value( 'fw-bold', 'style:font-weight' ),
  'label_letter_spacing' => cs_value( 'inherit' ),
  'label_line_height'    => cs_value( '1.285' ),
  'label_text_transform' => cs_value( 'inherit' ),


  // Content Spacing
  // ---------------

  'content_copy_spacing'            => cs_value( '1.25rem' ),
  'content_h_margin_top'            => cs_value( 'calc(1rem + 1.25em)' ),
  'content_h_margin_bottom'         => cs_value( '1rem' ),
  'content_ol_padding_inline_start' => cs_value( '1.25em' ),
  'content_ul_padding_inline_start' => cs_value( '1em' ),
  'content_li_spacing'              => cs_value( '0.262em' ),
  'content_media_spacing'           => cs_value( '2.441rem' ),


  // Forms: Inputs
  // -------------

  'input_background_color'        => cs_value( '#ffffff', 'style:color' ),
  'input_background_color_alt'    => cs_value( '', 'style:color' ),

  'input_color'                   => cs_value( '#000000', 'style:color' ),
  'input_color_alt'               => cs_value( '#0073e6', 'style:color' ),
  'input_font_family'             => cs_value( 'inherit', 'style:font-family' ),
  'input_font_size'               => cs_value( '1em' ),
  'input_font_style'              => cs_value( 'inherit' ),
  'input_font_weight'             => cs_value( 'inherit', 'style:font-weight' ),
  'input_letter_spacing'          => cs_value( '0em' ),
  'input_line_height'             => cs_value( '1.4' ),
  'input_text_align'              => cs_value( 'inherit' ),
  'input_text_transform'          => cs_value( 'inherit' ),

  'input_placeholder_opacity'     => cs_value( '0.33' ),
  'input_placeholder_opacity_alt' => cs_value( '0.55' ),

  'input_outline_width'           => cs_value( '4px' ),
  'input_outline_color'           => cs_value( 'rgba(0, 115, 230, 0.16)', 'style:color' ),

  'input_indicator_size'          => cs_value( '1em' ),
  'input_indicator_spacing_x'     => cs_value( '0px' ),
  'input_indicator_color'         => cs_value( 'rgba(0, 0, 0, 0.27)', 'style:color' ),
  'input_indicator_color_alt'     => cs_value( 'rgba(0, 115, 230, 1)', 'style:color' ), // #0073e6

  'input_border_width'            => cs_value( '1px' ),
  'input_border_style'            => cs_value( 'solid' ),
  'input_border_radius'           => cs_value( '2px' ),
  'input_border_color'            => cs_value( '#000000', 'style:color' ),
  'input_border_color_alt'        => cs_value( '#0073e6', 'style:color' ),

  'input_padding_x'               => cs_value( '0.8em' ),
  'input_padding_y_extra'         => cs_value( '0.5em' ),

  'input_box_shadow_x'            => cs_value( '0em' ),
  'input_box_shadow_y'            => cs_value( '0.25em' ),
  'input_box_shadow_blur'         => cs_value( '0.65em' ),
  'input_box_shadow_spread'       => cs_value( '0em' ),
  'input_box_shadow_color'        => cs_value( 'rgba(0, 0, 0, 0.03)', 'style:color' ),
  'input_box_shadow_color_alt'    => cs_value( 'rgba(0, 115, 230, 0.11)', 'style:color' ),


  // Forms: Checkboxes & Radios
  // --------------------------

  'rc_background_color'     => cs_value( '#ffffff', 'style:color' ),
  'rc_background_color_alt' => cs_value( '#0073e6', 'style:color' ),

  'rc_font_size'            => cs_value( '24px' ),

  'rc_outline_width'        => cs_value( '4px' ),
  'rc_outline_color'        => cs_value( 'rgba(0, 115, 230, 0.16)', 'style:color' ),

  'checkbox_marker_inset'   => cs_value( '8px' ),
  'radio_marker_inset'      => cs_value( '13px' ),
  'rc_marker_color'         => cs_value( '#ffffff', 'style:color' ),

  'rc_border_width'         => cs_value( '1px' ),
  'rc_border_style'         => cs_value( 'solid' ),
  'rc_border_radius'        => cs_value( '2px' ),
  'rc_border_color'         => cs_value( '#000000', 'style:color' ),
  'rc_border_color_alt'     => cs_value( '#0073e6', 'style:color' ),

  'rc_box_shadow_x'         => cs_value( '0em' ),
  'rc_box_shadow_y'         => cs_value( '0.25em' ),
  'rc_box_shadow_blur'      => cs_value( '0.65em' ),
  'rc_box_shadow_spread'    => cs_value( '0em' ),
  'rc_box_shadow_color'     => cs_value( 'rgba(0, 0, 0, 0.03)', 'style:color' ),
  'rc_box_shadow_color_alt' => cs_value( 'rgba(0, 115, 230, 0.11)', 'style:color' ),


  // Forms: Submits
  // --------------

  'submit_background_color'     => cs_value( '#000000', 'style:color' ),
  'submit_background_color_alt' => cs_value( '#0073e6', 'style:color' ),

  'submit_min_width'            => cs_value( 'none' ),
  'submit_padding_x'            => cs_value( '1.25em' ),

  'submit_color'                => cs_value( '#ffffff', 'style:color' ),
  'submit_color_alt'            => cs_value( '', 'style:color' ),
  'submit_font_weight'          => cs_value( 'fw-bold', 'style:font-weight' ),
  'submit_text_align'           => cs_value( 'center' ),

  'submit_outline_width'        => cs_value( '4px' ),
  'submit_outline_color'        => cs_value( 'rgba(0, 115, 230, 0.16)', 'style:color' ),

  'submit_border_radius'        => cs_value( '100em' ),
  'submit_border_color'         => cs_value( '#000000', 'style:color' ),
  'submit_border_color_alt'     => cs_value( '#0073e6', 'style:color' ),

  'submit_box_shadow_x'         => cs_value( '0em' ),
  'submit_box_shadow_y'         => cs_value( '0.25em' ),
  'submit_box_shadow_blur'      => cs_value( '0.65em' ),
  'submit_box_shadow_spread'    => cs_value( '0em' ),
  'submit_box_shadow_color'     => cs_value( 'rgba(0, 0, 0, 0.03)', 'style:color' ),
  'submit_box_shadow_color_alt' => cs_value( 'rgba(0, 115, 230, 0.11)', 'style:color' ),


  // Breakpoints
  // -----------

  'bp_base'   => cs_value( 2, 'markup' ) ,
  'bp_ranges' => cs_value( [ 500, 1000 ], 'markup' ),

];
