<?php
/**
 * Featured Image Copyright Functions
 * 
 * Functions to display ACF "image_copyright" field for featured images
 * 
 * @package X Theme
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Get the copyright text for a featured image
 * 
 * @param int|null $post_id The post ID (defaults to current post)
 * @return string The copyright text or empty string if none
 */
function get_featured_image_copyright( $post_id = null ) {
    if ( ! $post_id ) {
        $post_id = get_the_ID();
    }
    
    // Get the featured image ID
    $featured_image_id = get_post_thumbnail_id( $post_id );
    
    if ( ! $featured_image_id ) {
        return '';
    }
    
    // Get the ACF field value for the attachment
    $copyright = get_field( 'image_copyright', $featured_image_id );
    
    return $copyright ? $copyright : '';
}

/**
 * Display the copyright text for a featured image
 * 
 * @param int|null $post_id The post ID (defaults to current post)
 * @param string $before Text to display before the copyright
 * @param string $after Text to display after the copyright
 * @param bool $echo Whether to echo or return the result
 * @return string|void The copyright HTML or void if echoing
 */
function the_featured_image_copyright( $post_id = null, $before = '<p class="image-copyright">', $after = '</p>', $echo = true ) {
    $copyright = get_featured_image_copyright( $post_id );
    
    if ( ! $copyright ) {
        if ( ! $echo ) {
            return '';
        }
        return;
    }
    
    $output = $before . esc_html( $copyright ) . $after;
    
    if ( $echo ) {
        echo $output;
    } else {
        return $output;
    }
}

/**
 * Get copyright for any attachment ID
 * 
 * @param int $attachment_id The attachment ID
 * @return string The copyright text or empty string if none
 */
function get_attachment_copyright( $attachment_id ) {
    if ( ! $attachment_id ) {
        return '';
    }
    
    $copyright = get_field( 'image_copyright', $attachment_id );
    return $copyright ? $copyright : '';
}

/**
 * Register dynamic content field for featured image copyright
 */
function register_featured_image_copyright_dynamic_content() {
    // Only register if Cornerstone is active
    if ( ! function_exists( 'cornerstone_dynamic_content_register_field' ) ) {
        return;
    }
    
    cornerstone_dynamic_content_register_field( array(
        'name'  => 'featured_image_copyright',
        'group' => 'post',
        'type'  => 'text',
        'label' => 'Featured Image Copyright',
        'controls' => array( 'post' )
    ) );
}

/**
 * Supply the featured image copyright value for dynamic content
 */
function supply_featured_image_copyright_dynamic_content( $result, $field, $args ) {
    if ( $field !== 'featured_image_copyright' ) {
        return $result;
    }
    
    $post = cornerstone('DynamicContent')->get_contextual_post( $args );
    
    if ( ! $post ) {
        return '';
    }
    
    return get_featured_image_copyright( $post->ID );
}

// Hook into Cornerstone's dynamic content system
add_action( 'cs_dynamic_content_setup', 'register_featured_image_copyright_dynamic_content' );
add_filter( 'cs_dynamic_content_post', 'supply_featured_image_copyright_dynamic_content', 10, 3 );

/**
 * Shortcode for featured image copyright
 * Usage: [featured_image_copyright] or [featured_image_copyright post_id="123"]
 */
function featured_image_copyright_shortcode( $atts ) {
    $atts = shortcode_atts( array(
        'post_id' => null,
        'before' => '',
        'after' => '',
        'class' => 'image-copyright'
    ), $atts );
    
    $copyright = get_featured_image_copyright( $atts['post_id'] );
    
    if ( ! $copyright ) {
        return '';
    }
    
    $class_attr = $atts['class'] ? ' class="' . esc_attr( $atts['class'] ) . '"' : '';
    
    return $atts['before'] . '<span' . $class_attr . '>' . esc_html( $copyright ) . '</span>' . $atts['after'];
}

add_shortcode( 'featured_image_copyright', 'featured_image_copyright_shortcode' );

/**
 * Add CSS for image copyright styling
 */
function featured_image_copyright_styles() {
    ?>
    <style>
    .image-copyright {
        font-size: 0.85em;
        color: #666;
        font-style: italic;
        margin-top: 0.5em;
        margin-bottom: 1em;
    }
    
    .entry-featured .image-copyright {
        margin-top: 0.25em;
    }
    </style>
    <?php
}

add_action( 'wp_head', 'featured_image_copyright_styles' );
