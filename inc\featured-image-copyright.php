<?php
/**
 * Featured Image Copyright Functions
 * 
 * Functions to display ACF "image_copyright" field for featured images
 * 
 * @package X Theme
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Get the copyright text for a featured image
 * 
 * @param int|null $post_id The post ID (defaults to current post)
 * @return string The copyright text or empty string if none
 */
function get_featured_image_copyright( $post_id = null ) {
    if ( ! $post_id ) {
        $post_id = get_the_ID();
    }
    
    // Get the featured image ID
    $featured_image_id = get_post_thumbnail_id( $post_id );
    
    if ( ! $featured_image_id ) {
        return '';
    }
    
    // Get the ACF field value for the attachment
    $copyright = get_field( 'image_copyright', $featured_image_id );
    
    return $copyright ? $copyright : '';
}

/**
 * Display the copyright text for a featured image
 * 
 * @param int|null $post_id The post ID (defaults to current post)
 * @param string $before Text to display before the copyright
 * @param string $after Text to display after the copyright
 * @param bool $echo Whether to echo or return the result
 * @return string|void The copyright HTML or void if echoing
 */
function the_featured_image_copyright( $post_id = null, $before = '<p class="image-copyright">', $after = '</p>', $echo = true ) {
    $copyright = get_featured_image_copyright( $post_id );
    
    if ( ! $copyright ) {
        if ( ! $echo ) {
            return '';
        }
        return;
    }
    
    $output = $before . esc_html( $copyright ) . $after;
    
    if ( $echo ) {
        echo $output;
    } else {
        return $output;
    }
}

/**
 * Get copyright for any attachment ID
 * 
 * @param int $attachment_id The attachment ID
 * @return string The copyright text or empty string if none
 */
function get_attachment_copyright( $attachment_id ) {
    if ( ! $attachment_id ) {
        return '';
    }
    
    $copyright = get_field( 'image_copyright', $attachment_id );
    return $copyright ? $copyright : '';
}

/**
 * Get the caption text for a featured image
 *
 * @param int|null $post_id The post ID (defaults to current post)
 * @return string The caption text or empty string if none
 */
function get_featured_image_caption( $post_id = null ) {
    if ( ! $post_id ) {
        $post_id = get_the_ID();
    }

    // Get the featured image ID
    $featured_image_id = get_post_thumbnail_id( $post_id );

    if ( ! $featured_image_id ) {
        return '';
    }

    // Get the caption from the attachment's post_excerpt
    $attachment = get_post( $featured_image_id );

    return $attachment && $attachment->post_excerpt ? $attachment->post_excerpt : '';
}

/**
 * Display the caption text for a featured image
 *
 * @param int|null $post_id The post ID (defaults to current post)
 * @param string $before Text to display before the caption
 * @param string $after Text to display after the caption
 * @param bool $echo Whether to echo or return the result
 * @return string|void The caption HTML or void if echoing
 */
function the_featured_image_caption( $post_id = null, $before = '<p class="image-caption">', $after = '</p>', $echo = true ) {
    $caption = get_featured_image_caption( $post_id );

    if ( ! $caption ) {
        if ( ! $echo ) {
            return '';
        }
        return;
    }

    $output = $before . wp_kses_post( $caption ) . $after;

    if ( $echo ) {
        echo $output;
    } else {
        return $output;
    }
}

/**
 * Get caption for any attachment ID
 *
 * @param int $attachment_id The attachment ID
 * @return string The caption text or empty string if none
 */
function get_attachment_caption( $attachment_id ) {
    if ( ! $attachment_id ) {
        return '';
    }

    $attachment = get_post( $attachment_id );
    return $attachment && $attachment->post_excerpt ? $attachment->post_excerpt : '';
}

/**
 * Register dynamic content fields for featured image copyright and caption
 */
function register_featured_image_copyright_dynamic_content() {
    // Only register if Cornerstone is active
    if ( ! function_exists( 'cornerstone_dynamic_content_register_field' ) ) {
        return;
    }

    cornerstone_dynamic_content_register_field( array(
        'name'  => 'featured_image_copyright',
        'group' => 'post',
        'type'  => 'text',
        'label' => 'Featured Image Copyright',
        'controls' => array( 'post' )
    ) );

    cornerstone_dynamic_content_register_field( array(
        'name'  => 'featured_image_caption',
        'group' => 'post',
        'type'  => 'text',
        'label' => 'Featured Image Caption',
        'controls' => array( 'post' )
    ) );
}

/**
 * Supply the featured image copyright and caption values for dynamic content
 */
function supply_featured_image_copyright_dynamic_content( $result, $field, $args ) {
    $post = cornerstone('DynamicContent')->get_contextual_post( $args );

    if ( ! $post ) {
        return '';
    }

    switch ( $field ) {
        case 'featured_image_copyright':
            return get_featured_image_copyright( $post->ID );
        case 'featured_image_caption':
            return get_featured_image_caption( $post->ID );
        default:
            return $result;
    }
}

// Hook into Cornerstone's dynamic content system
add_action( 'cs_dynamic_content_setup', 'register_featured_image_copyright_dynamic_content' );
add_filter( 'cs_dynamic_content_post', 'supply_featured_image_copyright_dynamic_content', 10, 3 );

/**
 * Shortcode for featured image copyright
 * Usage: [featured_image_copyright] or [featured_image_copyright post_id="123"]
 */
function featured_image_copyright_shortcode( $atts ) {
    $atts = shortcode_atts( array(
        'post_id' => null,
        'before' => '',
        'after' => '',
        'class' => 'image-copyright'
    ), $atts );

    $copyright = get_featured_image_copyright( $atts['post_id'] );

    if ( ! $copyright ) {
        return '';
    }

    $class_attr = $atts['class'] ? ' class="' . esc_attr( $atts['class'] ) . '"' : '';

    return $atts['before'] . '<span' . $class_attr . '>' . esc_html( $copyright ) . '</span>' . $atts['after'];
}

/**
 * Shortcode for featured image caption
 * Usage: [featured_image_caption] or [featured_image_caption post_id="123"]
 */
function featured_image_caption_shortcode( $atts ) {
    $atts = shortcode_atts( array(
        'post_id' => null,
        'before' => '',
        'after' => '',
        'class' => 'image-caption'
    ), $atts );

    $caption = get_featured_image_caption( $atts['post_id'] );

    if ( ! $caption ) {
        return '';
    }

    $class_attr = $atts['class'] ? ' class="' . esc_attr( $atts['class'] ) . '"' : '';

    return $atts['before'] . '<span' . $class_attr . '>' . wp_kses_post( $caption ) . '</span>' . $atts['after'];
}

add_shortcode( 'featured_image_copyright', 'featured_image_copyright_shortcode' );
add_shortcode( 'featured_image_caption', 'featured_image_caption_shortcode' );

/**
 * Add CSS for image copyright and caption styling
 */
function featured_image_copyright_styles() {
    ?>
    <style>
    .image-copyright {
        font-size: 0.85em;
        color: #666;
        font-style: italic;
        margin-top: 0.5em;
        margin-bottom: 1em;
    }

    .image-caption {
        font-size: 0.9em;
        color: #555;
        margin-top: 0.5em;
        margin-bottom: 1em;
        line-height: 1.4;
    }

    .entry-featured .image-copyright,
    .entry-featured .image-caption {
        margin-top: 0.25em;
    }

    /* Combined styling when both are present */
    .image-copyright + .image-caption {
        margin-top: 0.25em;
    }
    </style>
    <?php
}

add_action( 'wp_head', 'featured_image_copyright_styles' );

/**
 * Check if a Cornerstone layout is applied to the current page
 *
 * @return bool True if a layout is applied, false otherwise
 */
function is_cornerstone_layout_applied() {
    // Check if Cornerstone is active
    if ( ! function_exists( 'cornerstone' ) ) {
        return false;
    }

    try {
        // Get the active layout from Cornerstone's assignment system
        $layout = cornerstone('Assignments')->get_active_layout();

        // Return true if a layout is found and active
        return ! is_null( $layout );

    } catch ( Exception $e ) {
        // If there's an error, assume no layout is applied
        return false;
    }
}

/**
 * Remove x-header-landmark when a Cornerstone layout is applied in Ethos stack
 */
function maybe_remove_ethos_landmark_header() {
    // Only apply to Ethos stack
    if ( x_get_stack() !== 'ethos' ) {
        return;
    }

    // Check if a Cornerstone layout is applied
    if ( is_cornerstone_layout_applied() ) {
        // Remove the landmark header action
        remove_action( 'x_ethos_landmark_header', 'x_ethos_landmark_header_output' );

        // Also prevent the landmark header from being included in the header template
        add_filter( 'x_ethos_display_landmark_header', '__return_false' );
    }
}

// Hook early to ensure it runs before the header is rendered
add_action( 'wp', 'maybe_remove_ethos_landmark_header', 5 );
