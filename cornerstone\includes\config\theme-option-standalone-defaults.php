<?php

return [

  // Background
  // ----------

  //'root_background_color' => cs_value( '#ffffff', 'style:color' ),


  // Container
  // ---------

  'container_width'     => cs_value( 'calc(100% - 2rem)' ),
  'container_max_width' => cs_value( '64rem' ),


  // Anchors
  // -------

  'a_color'                     => cs_value( '#0073e6', 'style:color' ),
  'a_color_alt'                 => cs_value( 'rgba(0, 115, 230, 0.88)', 'style:color' ),
  'a_text_decoration_line'      => cs_value( 'none' ),
  'a_text_decoration_style'     => cs_value( 'solid' ),
  'a_text_underline_offset'     => cs_value( '0.085em' ),
  'a_text_decoration_thickness' => cs_value( 'auto' ),
  'a_text_decoration_color'     => cs_value( '#0073e6', 'style:color' ),
  'a_text_decoration_color_alt' => cs_value( 'rgba(0, 115, 230, 0.88)', 'style:color' ),


  // Buttons
  // @TODO if needed standalone does have access to
  // x/framework/legacy/functions/frontend/generated-css/buttons.php

  //'x_button_style'                                      => 'real',
  //'x_button_shape'                                      => 'rounded',
  //'x_button_size'                                       => 'regular',
  //'x_button_color'                                      => '#ffffff',
  //'x_button_background_color'                           => '#ff2a13',
  //'x_button_border_color'                               => '#ac1100',
  //'x_button_bottom_color'                               => '#a71000',
  //'x_button_color_hover'                                => '#ffffff',
  //'x_button_background_color_hover'                     => '#ef2201',
  //'x_button_border_color_hover'                         => '#600900',
  //'x_button_bottom_color_hover'                         => '#a71000',


  // Typography
  // ----------

  'root_color'           => cs_value( '#000000', 'style:color' ),
  'root_font_family'     => cs_value( 'system:helveticaneue', 'style:font-family' ),
  'root_font_size'       => cs_value( '18px' ),
  'root_font_style'      => cs_value( 'normal' ),
  'root_font_weight'     => cs_value( 'fw-normal', 'style:font-weight' ),
  'root_letter_spacing'  => cs_value( '0em' ),
  'root_line_height'     => cs_value( '1.6' ),
  'root_text_transform'  => cs_value( 'inherit' ),

  'h1_color'             => cs_value( '#000000', 'style:color' ),
  'h1_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h1_font_size'         => cs_value( '3.815em' ),
  'h1_font_style'        => cs_value( 'inherit' ),
  'h1_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h1_letter_spacing'    => cs_value( 'inherit' ),
  'h1_line_height'       => cs_value( '1.115' ),
  'h1_text_transform'    => cs_value( 'inherit' ),

  'h2_color'             => cs_value( '#000000', 'style:color' ),
  'h2_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h2_font_size'         => cs_value( '3.052em' ),
  'h2_font_style'        => cs_value( 'inherit' ),
  'h2_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h2_letter_spacing'    => cs_value( 'inherit' ),
  'h2_line_height'       => cs_value( '1.125' ),
  'h2_text_transform'    => cs_value( 'inherit' ),

  'h3_color'             => cs_value( '#000000', 'style:color' ),
  'h3_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h3_font_size'         => cs_value( '2.441em' ),
  'h3_font_style'        => cs_value( 'inherit' ),
  'h3_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h3_letter_spacing'    => cs_value( 'inherit' ),
  'h3_line_height'       => cs_value( '1.15' ),
  'h3_text_transform'    => cs_value( 'inherit' ),

  'h4_color'             => cs_value( '#000000', 'style:color' ),
  'h4_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h4_font_size'         => cs_value( '1.953em' ),
  'h4_font_style'        => cs_value( 'inherit' ),
  'h4_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h4_letter_spacing'    => cs_value( 'inherit' ),
  'h4_line_height'       => cs_value( '1.2' ),
  'h4_text_transform'    => cs_value( 'inherit' ),

  'h5_color'             => cs_value( '#000000', 'style:color' ),
  'h5_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h5_font_size'         => cs_value( '1.563em' ),
  'h5_font_style'        => cs_value( 'inherit' ),
  'h5_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h5_letter_spacing'    => cs_value( 'inherit' ),
  'h5_line_height'       => cs_value( '1.25' ),
  'h5_text_transform'    => cs_value( 'inherit' ),

  'h6_color'             => cs_value( '#000000', 'style:color' ),
  'h6_font_family'       => cs_value( 'inherit', 'style:font-family' ),
  'h6_font_size'         => cs_value( '1.25em' ),
  'h6_font_style'        => cs_value( 'inherit' ),
  'h6_font_weight'       => cs_value( 'inherit', 'style:font-weight' ),
  'h6_letter_spacing'    => cs_value( 'inherit' ),
  'h6_line_height'       => cs_value( '1.265' ),
  'h6_text_transform'    => cs_value( 'inherit' ),

  'label_color'          => cs_value( '#000000', 'style:color' ),
  'label_font_family'    => cs_value( 'inherit', 'style:font-family' ),
  'label_font_size'      => cs_value( '0.8em' ),
  'label_font_style'     => cs_value( 'inherit' ),
  'label_font_weight'    => cs_value( 'fw-bold', 'style:font-weight' ),
  'label_letter_spacing' => cs_value( 'inherit' ),
  'label_line_height'    => cs_value( '1.285' ),
  'label_text_transform' => cs_value( 'inherit' ),


  // Breakpoints
  // -----------

  'bp_base'   => cs_value( 2, 'markup' ) ,
  'bp_ranges' => cs_value( [ 480, 767, 979, 1200 ], 'markup' ),

  // Font Awesome
  'x_font_awesome_icon_type' => cs_value(apply_filters("cs_fa_default_type", "webfont"), 'markup'),
  'x_font_awesome_load_types_for_elements' => cs_value(false, 'markup'),
  'x_font_awesome_shim_enable' => cs_value(true, 'markup'),
  'x_font_awesome_solid_enable' => cs_value('1', 'markup'),
  'x_font_awesome_regular_enable' => cs_value('1', 'markup'),
  'x_font_awesome_light_enable' => cs_value('1', 'markup'),
  'x_font_awesome_brands_enable' => cs_value('1', 'markup'),
  'x_font_awesome_sharp-light_enable' => cs_value(false, 'markup'),
  'x_font_awesome_sharp-regular_enable' => cs_value(false, 'markup'),
  'x_font_awesome_sharp-solid_enable' => cs_value(false, 'markup'),
];
