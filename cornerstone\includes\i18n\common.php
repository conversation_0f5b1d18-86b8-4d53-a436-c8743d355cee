<?php

return array(

  // Generic
  'common.untitled'         => __( 'Untitled', 'cornerstone' ),
  'common.untitled-entity'  => __( 'Untitled %s', 'cornerstone' ),
  'common.amended-title'   => __( '%s (%s)', 'cornerstone' ),
  'common.classic'          => __( 'Classic %s', 'cornerstone' ),
  'common.blank'            => __( 'Blank', 'cornerstone' ),
  'common.edit'             => __( 'Edit %s', 'cornerstone' ),
  'common.edit-context'     => __( 'Edit %s (%s)', 'cornerstone' ),
  'common.indexed'          => __( '{{label}} {{index}}', 'cornerstone'),
  'common.count'            => __( '{{index}} {{label}}', 'cornerstone'),
  'common.add-thing'        => __( 'Add {{context}}', 'cornerstone' ),
  'common.formatting-colon' => __( '{{prefix}}: {{content}}', 'cornerstone'),

  'common.title.launch'           => __( 'Launch', 'cornerstone' ),
  'common.title.cornerstone'      => __( 'Cornerstone', 'cornerstone' ),
  'common.title.header-builder'   => __( 'Header Builder', 'cornerstone' ),
  'common.title.page-builder'     => __( 'Page Builder', 'cornerstone' ),
  'common.title.footer-builder'   => __( 'Footer Builder', 'cornerstone' ),
  'common.title.layout-builder'   => __( 'Layout Builder', 'cornerstone' ),
  'common.title.template-manager' => __( 'Templates', 'cornerstone' ),
  'common.title.component-builder' => __( 'Component Builder', 'cornerstone' ),

  'common.title.options-default'  => __( 'Options', 'cornerstone' ),
  'common.title.options-theme'    => __( 'Theme Options', 'cornerstone' ),
  'common.title.fonts'            => __( 'Fonts', 'cornerstone' ),
  'common.title.colors'           => __( 'Colors', 'cornerstone' ),
  'common.title.preferences'      => __( 'Preferences', 'cornerstone' ),

  'common.toolbar-title'         => __( 'Cornerstone', 'cornerstone' ),
  'common.toolbar-launch'        => __( 'Launch', 'cornerstone' ),
  'common.toolbar-edit-link'     => _x( 'Edit %s', 'WordPress toolbar edit link', 'cornerstone' ),


  'common.document-post-type-singular' => __( 'Cornerstone Document', 'cornerstone' ),
  'common.document-post-type'          => __( 'Cornerstone Documents', 'cornerstone' ),

  'common.document.header'          => __( 'Header', 'cornerstone' ),
  'common.document.headers'         => __( 'Headers', 'cornerstone' ),
  'common.document.content'         => __( 'Content', 'cornerstone' ),
  'common.document.custom'          => __( 'Custom', 'cornerstone' ),
  'common.document.component'       => __( 'Component', 'cornerstone' ),
  'common.document.components'      => __( 'Components', 'cornerstone' ),
  'common.document.footer'          => __( 'Footer', 'cornerstone' ),
  'common.document.footers'         => __( 'Footers', 'cornerstone' ),
  'common.document.layout'          => __( 'Layout', 'cornerstone' ),
  'common.document.layouts'         => __( 'Layouts', 'cornerstone' ),
  'common.document.single'          => __( 'Single Layout', 'cornerstone' ),
  'common.document.singles'         => __( 'Single Layouts', 'cornerstone' ),
  'common.document.archive'         => __( 'Archive Layout', 'cornerstone' ),
  'common.document.archives'        => __( 'Archive Layouts', 'cornerstone' ),
  'common.document.legacy-layout'   => __( 'Layout (CS Legacy)', 'cornerstone' ),
  'common.document.legacy-layouts'  => __( 'Layouts (CS Legacy)', 'cornerstone' ),

  'common.layout.format'           => __( '{{context}} Layout', 'cornerstone' ),
  'common.layout.format-plural'    => __( '{{context}} Layouts', 'cornerstone' ),

  'common.component-slot'  => __( 'Slot', 'cornerstone' ),
  'common.component-slots'  => __( 'Slots', 'cornerstone' ),

  'common.elements-undefined-preview'      => __( 'This element could not render because its definition is missing. You might need to activate a plugin.', 'cornerstone' ),
  'common.elements-component-missing-preview'      => __( "The id ({{component_id}}) for this Component could not be resolved.\nElement ID ({{id}})", 'cornerstone' ),
  'common.elements-preview-unavailable'    => __( 'No Preview Available (<a target="_blank" href="{{url}}">View Live</a>)', 'cornerstone' ),
  'common.elements-preview-template-error' => __( 'This element could not render due to invalid markup. You may have unclosed HTML tags in your content.', 'cornerstone' ),
  'common.elements-preview-unknown'        => __( 'This element failed to render. Additional error information may be found in your browsers developer tools.', 'cornerstone' ),

  'common.menu-fallback' => __( 'Create a Menu', 'cornerstone' ),

  'common.cs-no-subset' => _x( 'cs-no-subset', 'Translate to: (greek, cyrillic, vietnamese) to add an additional font subset.', 'cornerstone' ),

  'common.editor.insert-image' => __( 'Insert Image', 'cornerstone' ),
  'common.editor.lists'        => __( 'Lists', 'cornerstone' ),
  'common.editor.alignment'    => __( 'Alignment', 'cornerstone' ),

  'common.preview.choose-layout' => __( 'Choose a Layout', 'cornerstone' ),


  'common.undefined-title' => __( 'Undefined Element', 'cornerstone' ),
  'common.region-title'    => __( 'Region', 'cornerstone' ),

  // Assignments
  // -----------
  'common.assign.unassigned'                             => __( 'Unassigned', 'cornerstone' ),
  'common.assign.multiple'                               => __( 'Multiple Assignments', 'cornerstone' ),


);
