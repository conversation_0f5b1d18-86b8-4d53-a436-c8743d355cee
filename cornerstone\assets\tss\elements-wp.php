[{"1": [{"2": ["breadcrumbs", [], [{"3": [["&.x-crumbs"], [{"4": ["changedmixin", [{"5": "auto"}, {"6": "breadcrumbs_width"}, {"5": "width"}]]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_max_width), get-base(breadcrumbs_max_width))", [{"8": ["get", "(breadcrumbs_max_width)", [{"6": "breadcrumbs_max_width"}]]}, {"8": ["get-base", "(breadcrumbs_max_width)", [{"6": "breadcrumbs_max_width"}]]}]]}, [{"9": ["max-width", {"8": ["get", "(breadcrumbs_max_width)", [{"6": "breadcrumbs_max_width"}]]}, false]}]]}, {"4": ["margin", [{"8": ["get-base", "(breadcrumbs_margin)", [{"6": "breadcrumbs_margin"}]]}, {"8": ["get", "(breadcrumbs_margin)", [{"6": "breadcrumbs_margin"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(breadcrumbs_border_width)", [{"6": "breadcrumbs_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(breadcrumbs_border_style)", [{"6": "breadcrumbs_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_border_color)", [{"6": "breadcrumbs_border_color"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(breadcrumbs_border_radius)", [{"6": "breadcrumbs_border_radius"}]]}, {"8": ["get", "(breadcrumbs_border_radius)", [{"6": "breadcrumbs_border_radius"}]]}]]}, {"4": ["padding", [{"8": ["get-base", "(breadcrumbs_padding)", [{"6": "breadcrumbs_padding"}]]}, {"8": ["get", "(breadcrumbs_padding)", [{"6": "breadcrumbs_padding"}]]}]]}, {"9": ["font-family", {"8": ["global-ff", "(get(breadcrumbs_font_family))", [{"8": ["get", "(breadcrumbs_font_family)", [{"6": "breadcrumbs_font_family"}]]}]]}, false]}, {"9": ["font-size", {"8": ["get", "(breadcrumbs_font_size)", [{"6": "breadcrumbs_font_size"}]]}, false]}, {"9": ["font-style", {"8": ["get", "(breadcrumbs_font_style)", [{"6": "breadcrumbs_font_style"}]]}, false]}, {"9": ["font-weight", {"8": ["global-fw", "(get(breadcrumbs_font_family),get(breadcrumbs_font_weight))", [{"8": ["get", "(breadcrumbs_font_family)", [{"6": "breadcrumbs_font_family"}]]}, {"8": ["get", "(breadcrumbs_font_weight)", [{"6": "breadcrumbs_font_weight"}]]}]]}, false]}, {"9": ["line-height", {"8": ["get", "(breadcrumbs_line_height)", [{"6": "breadcrumbs_line_height"}]]}, false]}, {"4": ["background-color", [{"6": "breadcrumbs_bg_color"}]]}, {"4": ["box-shadow", [{"8": ["get", "(breadcrumbs_box_shadow_dimensions)", [{"6": "breadcrumbs_box_shadow_dimensions"}]]}, {"8": ["get", "(breadcrumbs_box_shadow_color)", [{"6": "breadcrumbs_box_shadow_color"}]]}]]}]]}, {"3": [[".x-crumbs-list"], [{"7": [{"8": ["get", "(breadcrumbs_reverse)", [{"6": "breadcrumbs_reverse"}]]}, [{"9": ["flex-direction", {"6": "row-reverse"}, false]}]]}, {"9": ["justify-content", {"8": ["get", "(breadcrumbs_flex_justify)", [{"6": "breadcrumbs_flex_justify"}]]}, false]}, {"7": [{"8": ["get", "(breadcrumbs_delimiter)", [{"6": "breadcrumbs_delimiter"}]]}, [{"7": [{"8": ["get", "(breadcrumbs_reverse)", [{"6": "breadcrumbs_reverse"}]]}, [{"9": ["margin-right", {"8": ["calc", {"11": ["(%s * -1)", [{"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}]]}, [{"12": [{"6": {"11": ["%s", [{"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}]]}}, "*", {"13": ["-", {"14": "1"}]}]}]]}, true]}], {"15": [{"9": ["margin-left", {"8": ["calc", {"11": ["(%s * -1)", [{"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}]]}, [{"12": [{"6": {"11": ["%s", [{"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}]]}}, "*", {"13": ["-", {"14": "1"}]}]}]]}, true]}]}]}]]}]]}, {"3": [[".x-crumbs-list-item"], [{"7": [{"8": ["get", "(breadcrumbs_delimiter)", [{"6": "breadcrumbs_delimiter"}]]}, [{"7": [{"8": ["get", "(breadcrumbs_reverse)", [{"6": "breadcrumbs_reverse"}]]}, [{"9": ["margin-right", {"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}, false]}], {"15": [{"9": ["margin-left", {"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}, false]}]}]}]]}]]}, {"3": [[".x-crumbs-link"], [{"7": [{"8": ["is-set", "(get(breadcrumbs_links_max_width), get-base(breadcrumbs_links_max_width))", [{"8": ["get", "(breadcrumbs_links_max_width)", [{"6": "breadcrumbs_links_max_width"}]]}, {"8": ["get-base", "(breadcrumbs_links_max_width)", [{"6": "breadcrumbs_links_max_width"}]]}]]}, [{"9": ["max-width", {"8": ["get", "(breadcrumbs_links_max_width)", [{"6": "breadcrumbs_links_max_width"}]]}, false]}]]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_links_min_width), get-base(breadcrumbs_links_min_width))", [{"8": ["get", "(breadcrumbs_links_min_width)", [{"6": "breadcrumbs_links_min_width"}]]}, {"8": ["get-base", "(breadcrumbs_links_min_width)", [{"6": "breadcrumbs_links_min_width"}]]}]]}, [{"9": ["min-width", {"8": ["get", "(breadcrumbs_links_min_width)", [{"6": "breadcrumbs_links_min_width"}]]}, false]}]]}, {"4": ["margin", [{"8": ["get-base", "(breadcrumbs_links_margin)", [{"6": "breadcrumbs_links_margin"}]]}, {"8": ["get", "(breadcrumbs_links_margin)", [{"6": "breadcrumbs_links_margin"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(breadcrumbs_links_border_width)", [{"6": "breadcrumbs_links_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(breadcrumbs_links_border_style)", [{"6": "breadcrumbs_links_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_border_color)", [{"6": "breadcrumbs_links_border_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_border_color_alt)", [{"6": "breadcrumbs_links_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(breadcrumbs_links_border_radius)", [{"6": "breadcrumbs_links_border_radius"}]]}, {"8": ["get", "(breadcrumbs_links_border_radius)", [{"6": "breadcrumbs_links_border_radius"}]]}]]}, {"4": ["padding", [{"8": ["get-base", "(breadcrumbs_links_padding)", [{"6": "breadcrumbs_links_padding"}]]}, {"8": ["get", "(breadcrumbs_links_padding)", [{"6": "breadcrumbs_links_padding"}]]}]]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_letter_spacing), get-base(breadcrumbs_letter_spacing))", [{"8": ["get", "(breadcrumbs_letter_spacing)", [{"6": "breadcrumbs_letter_spacing"}]]}, {"8": ["get-base", "(breadcrumbs_letter_spacing)", [{"6": "breadcrumbs_letter_spacing"}]]}]]}, [{"9": ["letter-spacing", {"8": ["get", "(breadcrumbs_letter_spacing)", [{"6": "breadcrumbs_letter_spacing"}]]}, false]}]]}, {"4": ["changedmixin", [{"5": "1em"}, {"6": "breadcrumbs_links_base_font_size"}, {"5": "font-size"}]]}, {"9": ["font-style", {"8": ["get", "(breadcrumbs_links_font_style)", [{"6": "breadcrumbs_links_font_style"}]]}, false]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_links_letter_spacing), get-base(breadcrumbs_links_letter_spacing))", [{"8": ["get", "(breadcrumbs_links_letter_spacing)", [{"6": "breadcrumbs_links_letter_spacing"}]]}, {"8": ["get-base", "(breadcrumbs_links_letter_spacing)", [{"6": "breadcrumbs_links_letter_spacing"}]]}]]}, [{"9": ["letter-spacing", {"8": ["get", "(breadcrumbs_links_letter_spacing)", [{"6": "breadcrumbs_links_letter_spacing"}]]}, false]}]]}, {"9": ["line-height", {"8": ["get", "(breadcrumbs_links_line_height)", [{"6": "breadcrumbs_links_line_height"}]]}, false]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_links_text_align), get-base(breadcrumbs_links_text_align))", [{"8": ["get", "(breadcrumbs_links_text_align)", [{"6": "breadcrumbs_links_text_align"}]]}, {"8": ["get-base", "(breadcrumbs_links_text_align)", [{"6": "breadcrumbs_links_text_align"}]]}]]}, [{"9": ["text-align", {"8": ["get", "(breadcrumbs_links_text_align)", [{"6": "breadcrumbs_links_text_align"}]]}, false]}]]}, {"4": ["text-shadow", [{"10": ["dimensions", {"8": ["get", "(breadcrumbs_links_text_shadow_dimensions)", [{"6": "breadcrumbs_links_text_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_text_shadow_color)", [{"6": "breadcrumbs_links_text_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_text_shadow_color_alt)", [{"6": "breadcrumbs_links_text_shadow_color_alt"}]]}]}]]}, {"7": [{"8": ["is-set", "(get(breadcrumbs_links_text_transform), get-base(breadcrumbs_links_text_transform))", [{"8": ["get", "(breadcrumbs_links_text_transform)", [{"6": "breadcrumbs_links_text_transform"}]]}, {"8": ["get-base", "(breadcrumbs_links_text_transform)", [{"6": "breadcrumbs_links_text_transform"}]]}]]}, [{"9": ["text-transform", {"8": ["get", "(breadcrumbs_links_text_transform)", [{"6": "breadcrumbs_links_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"8": ["get", "(breadcrumbs_links_color)", [{"6": "breadcrumbs_links_color"}]]}, {"8": ["get", "(breadcrumbs_links_color_alt)", [{"6": "breadcrumbs_links_color_alt"}]]}]]}, {"4": ["background-color", [{"6": "breadcrumbs_links_bg_color"}, {"6": "breadcrumbs_links_bg_color_alt"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(breadcrumbs_links_box_shadow_dimensions)", [{"6": "breadcrumbs_links_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_box_shadow_color)", [{"6": "breadcrumbs_links_box_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_box_shadow_color_alt)", [{"6": "breadcrumbs_links_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [[".x-crumbs-link:hover"], [{"4": ["text-color-alt", [{"8": ["get", "(breadcrumbs_links_color)", [{"6": "breadcrumbs_links_color"}]]}, {"8": ["get", "(breadcrumbs_links_color_alt)", [{"6": "breadcrumbs_links_color_alt"}]]}]]}, {"4": ["border-alt", [{"10": ["width", {"8": ["get", "(breadcrumbs_links_border_width)", [{"6": "breadcrumbs_links_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(breadcrumbs_links_border_style)", [{"6": "breadcrumbs_links_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_border_color)", [{"6": "breadcrumbs_links_border_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_border_color_alt)", [{"6": "breadcrumbs_links_border_color_alt"}]]}]}]]}, {"4": ["background-color", [{"6": "breadcrumbs_links_bg_color"}, {"6": "breadcrumbs_links_bg_color_alt"}]]}, {"4": ["box-shadow-alt", [{"10": ["dimensions", {"8": ["get", "(breadcrumbs_links_box_shadow_dimensions)", [{"6": "breadcrumbs_links_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_box_shadow_color)", [{"6": "breadcrumbs_links_box_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_box_shadow_color_alt)", [{"6": "breadcrumbs_links_box_shadow_color_alt"}]]}]}]]}, {"4": ["text-shadow", [{"10": ["dimensions", {"8": ["get", "(breadcrumbs_links_text_shadow_dimensions)", [{"6": "breadcrumbs_links_text_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(breadcrumbs_links_text_shadow_color)", [{"6": "breadcrumbs_links_text_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(breadcrumbs_links_text_shadow_color_alt)", [{"6": "breadcrumbs_links_text_shadow_color_alt"}]]}]}]]}]]}, {"7": [{"8": ["get", "(breadcrumbs_delimiter)", [{"6": "breadcrumbs_delimiter"}]]}, [{"3": [[".x-crumbs-delimiter"], [{"7": [{"8": ["get", "(breadcrumbs_reverse)", [{"6": "breadcrumbs_reverse"}]]}, [{"9": ["margin-right", {"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}, false]}], {"15": [{"9": ["margin-left", {"8": ["get", "(breadcrumbs_delimiter_spacing)", [{"6": "breadcrumbs_delimiter_spacing"}]]}, false]}]}]}, {"4": ["text-color", [{"8": ["get", "(breadcrumbs_delimiter_color)", [{"6": "breadcrumbs_delimiter_color"}]]}]]}, {"4": ["text-shadow", [{"8": ["get", "(breadcrumbs_delimiter_text_shadow_dimensions)", [{"6": "breadcrumbs_delimiter_text_shadow_dimensions"}]]}, {"8": ["get", "(breadcrumbs_delimiter_text_shadow_color)", [{"6": "breadcrumbs_delimiter_text_shadow_color"}]]}]]}]]}]]}]]}, {"2": ["comment-form", [], [{"4": ["margin", [{"8": ["get-base", "(comment_form_margin)", [{"6": "comment_form_margin"}]]}, {"8": ["get", "(comment_form_margin)", [{"6": "comment_form_margin"}]]}]]}]]}, {"2": ["comment-list", [], [{"4": ["margin", [{"8": ["get-base", "(comment_list_margin)", [{"6": "comment_list_margin"}]]}, {"8": ["get", "(comment_list_margin)", [{"6": "comment_list_margin"}]]}]]}]]}, {"2": ["comment-pagination", [], []]}, {"2": ["pagination", [], [{"4": ["changedmixin", [{"5": "auto"}, {"6": "pagination_width"}, {"5": "width"}]]}, {"7": [{"8": ["is-set", "(get(pagination_max_width), get-base(pagination_max_width))", [{"8": ["get", "(pagination_max_width)", [{"6": "pagination_max_width"}]]}, {"8": ["get-base", "(pagination_max_width)", [{"6": "pagination_max_width"}]]}]]}, [{"9": ["max-width", {"8": ["get", "(pagination_max_width)", [{"6": "pagination_max_width"}]]}, false]}]]}, {"4": ["margin", [{"8": ["get-base", "(pagination_margin)", [{"6": "pagination_margin"}]]}, {"8": ["get", "(pagination_margin)", [{"6": "pagination_margin"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_border_width)", [{"6": "pagination_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_border_style)", [{"6": "pagination_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_border_color)", [{"6": "pagination_border_color"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(pagination_border_radius)", [{"6": "pagination_border_radius"}]]}, {"8": ["get", "(pagination_border_radius)", [{"6": "pagination_border_radius"}]]}]]}, {"4": ["padding", [{"8": ["get-base", "(pagination_padding)", [{"6": "pagination_padding"}]]}, {"8": ["get", "(pagination_padding)", [{"6": "pagination_padding"}]]}]]}, {"4": ["changedmixin", [{"5": "1em"}, {"6": "pagination_base_font_size"}, {"5": "font-size"}]]}, {"4": ["background-color", [{"6": "pagination_bg_color"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_box_shadow_dimensions)", [{"6": "pagination_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_box_shadow_color)", [{"6": "pagination_box_shadow_color"}]]}]}]]}, {"3": [[".x-paginate-inner"], [{"9": ["justify-content", {"8": ["get", "(pagination_flex_justify)", [{"6": "pagination_flex_justify"}]]}, false]}, {"7": [{"8": ["is-set", "(get(pagination_items_gap), get-base(pagination_items_gap))", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}, {"8": ["get-base", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}, [{"9": ["margin-right", {"8": ["calc", {"11": ["(%s * -1)", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}, [{"12": [{"6": {"11": ["%s", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}}, "*", {"13": ["-", {"14": "1"}]}]}]]}, false]}, {"9": ["margin-bottom", {"8": ["calc", {"11": ["(%s * -1)", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}, [{"12": [{"6": {"11": ["%s", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}}, "*", {"13": ["-", {"14": "1"}]}]}]]}, false]}]]}]]}, {"3": [[".x-paginate-inner > *"], [{"7": [{"8": ["get", "(pagination_items_grow)", [{"6": "pagination_items_grow"}]]}, [{"9": ["flex-grow", {"14": "1"}, false]}, {"9": ["flex-basis", {"16": ["0", "%"]}, false]}]]}, {"9": ["min-width", {"8": ["get", "(pagination_items_min_width)", [{"6": "pagination_items_min_width"}]]}, false]}, {"9": ["min-height", {"8": ["get", "(pagination_items_min_height)", [{"6": "pagination_items_min_height"}]]}, false]}, {"7": [{"8": ["is-set", "(get(pagination_items_gap), get-base(pagination_items_gap))", [{"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}, {"8": ["get-base", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}]]}, [{"9": ["margin-right", {"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}, false]}, {"9": ["margin-bottom", {"8": ["get", "(pagination_items_gap)", [{"6": "pagination_items_gap"}]]}, false]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_items_border_color)", [{"6": "pagination_items_border_color"}]]}]}, {"10": ["alt", {"8": ["get", "(pagination_items_border_color_alt)", [{"6": "pagination_items_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(pagination_items_border_radius)", [{"6": "pagination_items_border_radius"}]]}, {"8": ["get", "(pagination_items_border_radius)", [{"6": "pagination_items_border_radius"}]]}]]}, {"4": ["padding", [{"8": ["get-base", "(pagination_items_padding)", [{"6": "pagination_items_padding"}]]}, {"8": ["get", "(pagination_items_padding)", [{"6": "pagination_items_padding"}]]}]]}, {"9": ["font-family", {"8": ["global-ff", "(get(pagination_items_font_family))", [{"8": ["get", "(pagination_items_font_family)", [{"6": "pagination_items_font_family"}]]}]]}, false]}, {"9": ["font-size", {"8": ["get", "(pagination_items_font_size)", [{"6": "pagination_items_font_size"}]]}, false]}, {"4": ["changedmixin", [{"5": "normal"}, {"6": "pagination_items_font_style"}, {"5": "font-style"}]]}, {"9": ["font-weight", {"8": ["global-fw", "(get(pagination_items_font_family),get(pagination_items_font_weight))", [{"8": ["get", "(pagination_items_font_family)", [{"6": "pagination_items_font_family"}]]}, {"8": ["get", "(pagination_items_font_weight)", [{"6": "pagination_items_font_weight"}]]}]]}, false]}, {"4": ["text-color", [{"8": ["get", "(pagination_items_text_color)", [{"6": "pagination_items_text_color"}]]}, {"8": ["get", "(pagination_items_text_color_alt)", [{"6": "pagination_items_text_color_alt"}]]}]]}, {"4": ["background-color", [{"6": "pagination_items_bg_color"}, {"6": "pagination_items_bg_color_alt"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_items_box_shadow_color)", [{"6": "pagination_items_box_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(pagination_items_box_shadow_color_alt)", [{"6": "pagination_items_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [[".x-paginate-inner > a:hover"], [{"4": ["text-color-alt", [{"8": ["get", "(pagination_items_text_color)", [{"6": "pagination_items_text_color"}]]}, {"8": ["get", "(pagination_items_text_color_alt)", [{"6": "pagination_items_text_color_alt"}]]}]]}, {"4": ["border-alt", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_items_border_color)", [{"6": "pagination_items_border_color"}]]}]}, {"10": ["alt", {"8": ["get", "(pagination_items_border_color_alt)", [{"6": "pagination_items_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(pagination_items_border_radius)", [{"6": "pagination_items_border_radius"}]]}, {"8": ["get", "(pagination_items_border_radius)", [{"6": "pagination_items_border_radius"}]]}]]}, {"4": ["background-color-alt", [{"8": ["get", "(pagination_items_bg_color)", [{"6": "pagination_items_bg_color"}]]}, {"8": ["get", "(pagination_items_bg_color_alt)", [{"6": "pagination_items_bg_color_alt"}]]}]]}, {"4": ["box-shadow-alt", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_items_box_shadow_color)", [{"6": "pagination_items_box_shadow_color"}]]}]}, {"10": ["alt", {"8": ["get", "(pagination_items_box_shadow_color_alt)", [{"6": "pagination_items_box_shadow_color_alt"}]]}]}]]}]]}, {"7": [{"12": [{"8": ["get", "(pagination_numbered_hide)", [{"6": "pagination_numbered_hide"}]]}, "!=", {"5": "xl"}]}, [{"3": [[".x-paginate-inner > .current"], [{"4": ["text-color", [{"8": ["get", "(pagination_current_text_color)", [{"6": "pagination_current_text_color"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_current_border_color)", [{"6": "pagination_current_border_color"}]]}]}, {"10": ["colorOnly", {"6": true}]}]]}, {"4": ["background-color", [{"6": "pagination_current_bg_color"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_current_box_shadow_color)", [{"6": "pagination_current_box_shadow_color"}]]}]}, {"10": ["noAltAllowNone", {"6": true}]}]]}]]}]]}, {"7": [{"12": [{"8": ["get", "(pagination_numbered_hide)", [{"6": "pagination_numbered_hide"}]]}, "!=", {"5": "xl"}]}, [{"7": [{"8": ["get", "(pagination_dots)", [{"6": "pagination_dots"}]]}, [{"3": [[".x-paginate-inner > .dots"], [{"4": ["text-color", [{"8": ["get", "(pagination_dots_text_color)", [{"6": "pagination_dots_text_color"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_dots_border_color)", [{"6": "pagination_dots_border_color"}]]}]}, {"10": ["colorOnly", {"6": true}]}]]}, {"4": ["background-color", [{"6": "pagination_dots_bg_color"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_dots_box_shadow_color)", [{"6": "pagination_dots_box_shadow_color"}]]}]}, {"10": ["noAltAllowNone", {"6": true}]}]]}]]}]]}]]}, {"7": [{"12": [{"8": ["get", "(pagination_numbered_hide)", [{"6": "pagination_numbered_hide"}]]}, "!=", {"5": "xl"}]}, [{"7": [{"8": ["get", "(pagination_prev_next)", [{"6": "pagination_prev_next"}]]}, [{"3": [[".x-paginate-inner > .prev", ".x-paginate-inner > .next"], [{"4": ["text-color", [{"8": ["get", "(pagination_prev_next_text_color)", [{"6": "pagination_prev_next_text_color"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_prev_next_border_color)", [{"6": "pagination_prev_next_border_color"}]]}]}, {"10": ["colorOnly", {"6": true}]}]]}, {"4": ["background-color", [{"6": "pagination_prev_next_bg_color"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_prev_next_box_shadow_color)", [{"6": "pagination_prev_next_box_shadow_color"}]]}]}, {"10": ["noAltAllowNone", {"6": true}]}]]}]]}, {"3": [[".x-paginate-inner > .prev:hover", ".x-paginate-inner > .next:hover"], [{"4": ["text-color", [{"8": ["get", "(pagination_prev_next_text_color_alt)", [{"6": "pagination_prev_next_text_color_alt"}]]}]]}, {"4": ["border", [{"10": ["width", {"8": ["get", "(pagination_items_border_width)", [{"6": "pagination_items_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(pagination_items_border_style)", [{"6": "pagination_items_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_prev_next_border_color_alt)", [{"6": "pagination_prev_next_border_color_alt"}]]}]}, {"10": ["colorOnly", {"6": true}]}]]}, {"4": ["background-color", [{"6": "pagination_prev_next_bg_color_alt"}]]}, {"4": ["box-shadow", [{"10": ["dimensions", {"8": ["get", "(pagination_items_box_shadow_dimensions)", [{"6": "pagination_items_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(pagination_prev_next_box_shadow_color_alt)", [{"6": "pagination_prev_next_box_shadow_color_alt"}]]}]}, {"10": ["noAltAllowNone", {"6": true}]}]]}]]}]]}]]}]]}, {"2": ["widget-area", [], [{"9": ["font-size", {"8": ["get", "(widget_area_base_font_size)", [{"6": "widget_area_base_font_size"}]]}, false]}, {"4": ["margin", [{"8": ["get-base", "(widget_area_margin)", [{"6": "widget_area_margin"}]]}, {"8": ["get", "(widget_area_margin)", [{"6": "widget_area_margin"}]]}]]}, {"4": ["box-shadow", [{"10": ["width", {"8": ["get", "(widget_area_border_width)", [{"6": "widget_area_border_width"}]]}]}, {"10": ["style", {"8": ["get", "(widget_area_border_style)", [{"6": "widget_area_border_style"}]]}]}, {"10": ["base", {"8": ["get", "(widget_area_border_color)", [{"6": "widget_area_border_color"}]]}]}]]}, {"4": ["border-radius", [{"8": ["get-base", "(widget_area_border_radius)", [{"6": "widget_area_border_radius"}]]}, {"8": ["get", "(widget_area_border_radius)", [{"6": "widget_area_border_radius"}]]}]]}, {"4": ["padding", [{"8": ["get-base", "(widget_area_padding)", [{"6": "widget_area_padding"}]]}, {"8": ["get", "(widget_area_padding)", [{"6": "widget_area_padding"}]]}]]}, {"4": ["background-color", [{"6": "widget_area_bg_color"}]]}, {"4": ["box-shadow", [{"10": ["dimension", {"8": ["get", "(widget_area_box_shadow_dimensions)", [{"6": "widget_area_box_shadow_dimensions"}]]}]}, {"10": ["base", {"8": ["get", "(widget_area_box_shadow_color)", [{"6": "widget_area_box_shadow_color"}]]}]}]]}, {"3": [[".widget:not(:first-child)"], [{"9": ["margin", {"17": [{"8": ["get", "(widget_area_spacing)", [{"6": "widget_area_spacing"}]]}, {"14": "0"}, {"14": "0"}]}, false]}]]}, {"3": [[".widget .h-widget"], [{"9": ["margin", {"17": [{"14": "0"}, {"14": "0"}, {"8": ["get", "(widget_area_headline_spacing)", [{"6": "widget_area_headline_spacing"}]]}]}, false]}]]}]]}]}, {"document": 1, "module": 2, "styleRule": 3, "include": 4, "singleQuotedString": 5, "primitive": 6, "if": 7, "call": 8, "assignProperty": 9, "keywordArgument": 10, "interpolated": 11, "operation": 12, "unary": 13, "number": 14, "else": 15, "dimension": 16, "list": 17}]