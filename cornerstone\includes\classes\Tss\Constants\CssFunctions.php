<?php

namespace Themeco\Cornerstone\Tss\Constants;


class CssFunctions {

  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Functions

  public const LIST = [

    // Transform
    // ---------

    'matrix',
    'matrix3d',
    'perspective',
    'rotate',
    'rotate3d',
    'rotatex',
    'rotatey',
    'rotatez',
    'scale',
    'scale3d',
    'scalex',
    'scaley',
    'scalez',
    'skew',
    'skewx',
    'skewy',
    'translate',
    'translate3d',
    'translatex',
    'translatey',
    'translatez',

    // Math
    // ----

    'calc',
    'clamp',
    'max',
    'min',
    'minmax',
    'mod',
    'abs',
    'acos',
    'asin',
    'atan',
    'atan2',
    'attr',
    'cos',
    'exp',
    'hypot',
    'log',
    'mod',
    'pow',
    'rem',
    'round',
    'sign',
    'sin',
    'sqrt',
    'tan',

    // Filters
    // -------

    'blur',
    'brightness',
    'contrast',
    'drop-shadow',
    'grayscale',
    'hue-rotate',
    'invert',
    'opacity',
    'saturate',
    'sepia',

    // Color
    // -----

    'color',
    'color-mix',
    'color-contrast',
    'device-cmyk',
    'hsl',
    'hsla',
    'hwb',
    'lab',
    'lch',
    'rgb',
    'rgba',

    // Image
    // -----

    'conic-gradient',
    'cross-fade',
    'image',
    'image-set',
    'linear-gradient',
    'radial-gradient',
    'repeating-linear-gradient',
    'repeating-radial-gradient',
    'repeating-conic-gradient',

    // Counter
    // -------

    'counter',
    'counters',

    // Font
    // -------

    'stylistic',
    'styleset',
    'character-variant',
    'swash',
    'ornaments',
    'annotation',

    // Shape
    // -----

    'circle',
    'ellipse',
    'inset',
    'path',
    'polygon',

    // Other
    // ------

    'cubic-bezier',
    'element',
    'env',
    'fit-content',
    'format',
    'leader',
    'local',
    'paint',
    'repeat',
    'steps',
    'symbols',
    'target-counter',
    'target-counters',
    'target-text',
    'toggle',
    'url',
    'var'

  ];

}