(()=>{var{attach:o,util:a}=window.csGlobal.rivet,{onLoad:c}=a;c(function(){if(window.jQuery){let n=function(e){let t='<span class="x-anchor-content" style="-webkit-justify-content: center; justify-content: center; -webkit-align-items: center; align-items: center;"><span class="x-anchor-text"><span class="x-anchor-text-primary"></span></span></span>';r(e).find(".button").removeClass("button").addClass("x-anchor").wrapInner(t)};var s=n;let r=window.jQuery;o(".x-mini-cart",e=>{n(e)}),r(document).on("added_to_cart wc_fragments_loaded wc_fragments_refreshed","body",()=>{Array.from(document.querySelectorAll(".x-mini-cart")).forEach(n)}),r(document).on("wc_cart_button_updated",(e,t)=>{t.hasClass("x-anchor-button")&&t.parent().find(".added_to_cart").remove()}),r(()=>{o(".x-preview-woocommerce-product-gallery",e=>{let t=r(e);t.addClass("woocommerce-product-gallery").removeClass("x-woocommerce-product-gallery"),t.trigger("wc-product-gallery-before-init",[e,window.wc_single_product_params]),t.wc_product_gallery(window.wc_single_product_params),t.trigger("wc-product-gallery-after-init",[e,window.wc_single_product_params])})})}});})();
