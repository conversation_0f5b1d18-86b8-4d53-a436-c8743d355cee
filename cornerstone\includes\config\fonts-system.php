<?php

return array(
  'arial' => array(
    'source'  => 'system',
    'family'  => 'Arial',
    'stack'   => 'Arial, "Helvetica Neue", Helvetica, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'arialnarrow' => array(
    'source'  => 'system',
    'family'  => 'Arial Narrow',
    'stack'   => '"Arial Narrow", Arial, "Helvetica Neue", Helvetica, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'baskerville' => array(
    'source'  => 'system',
    'family'  => 'Baskerville',
    'stack'   => 'Baskerville, "Baskerville old face", "Hoefler Text", Garamond, "Times New Roman", serif',
    'weights' => array( '400', '400italic', '600', '600italic', '700', '700italic' )
  ),
  'bodonimt' => array(
    'source'  => 'system',
    'family'  => 'Bodoni MT',
    'stack'   => '"Bodoni MT", Didot, "Didot LT STD", "Hoefler Text", Garamond, "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'bookantiqua' => array(
    'source'  => 'system',
    'family'  => 'Book Antiqua',
    'stack'   => '"Book Antiqua", Palatino, "Palatino Linotype", "Palatino LT STD", Georgia, serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'calibri' => array(
    'source'  => 'system',
    'family'  => 'Calibri',
    'stack'   => 'Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'cambria' => array(
    'source'  => 'system',
    'family'  => 'Cambria',
    'stack'   => 'Cambria, Georgia, serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'candara' => array(
    'source'  => 'system',
    'family'  => 'Candara',
    'stack'   => 'Candara, Calibri, Segoe, "Segoe UI", Optima, Arial, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'consolas' => array(
    'source'  => 'system',
    'family'  => 'Consolas',
    'stack'   => 'Consolas, monaco, monospace',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'couriernew' => array(
    'source'  => 'system',
    'family'  => 'Courier New',
    'stack'   => '"Courier New", Courier, "Lucida Sans Typewriter", "Lucida Typewriter", monospace',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'didot' => array(
    'source'  => 'system',
    'family'  => 'Didot',
    'stack'   => 'Didot, "Didot LT STD", "Hoefler Text", Garamond, "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'futura' => array(
    'source'  => 'system',
    'family'  => 'Futura',
    'stack'   => 'Futura, "Trebuchet MS", Arial, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'garamond' => array(
    'source'  => 'system',
    'family'  => 'Garamond',
    'stack'   => 'Garamond, Baskerville, "Baskerville Old Face", "Hoefler Text", "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'geneva' => array(
    'source'  => 'system',
    'family'  => 'Geneva',
    'stack'   => 'Geneva, Tahoma, Verdana, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'georgia' => array(
    'source'  => 'system',
    'family'  => 'Georgia',
    'stack'   => 'Georgia, Palatino, "Palatino Linotype", Times, "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'gillsans' => array(
    'source'  => 'system',
    'family'  => 'Gill Sans',
    'stack'   => '"Gill Sans", "Gill Sans MT", Calibri, sans-serif',
    'weights' => array( '300', '300italic', '400', '400italic', '600', '600italic', '700', '700italic' )
  ),
  'goudyoldstyle' => array(
    'source'  => 'system',
    'family'  => 'Goudy Old Style',
    'stack'   => '"Goudy Old Style", Garamond, "Big Caslon", "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'helvetica' => array(
    'source'  => 'system',
    'family'  => 'Helvetica',
    'stack'   => 'Helvetica, Arial, sans-serif',
    'weights' => array( '300', '300italic', '400', '400italic', '700', '700italic' )
  ),
  'helveticaneue' => array(
    'source'  => 'system',
    'family'  => 'Helvetica Neue',
    'stack'   => '"Helvetica Neue", Helvetica, Arial, sans-serif',
    'weights' => array( '100', '100italic', '300', '300italic', '400', '400italic', '500', '500italic', '700', '700italic' )
  ),
  'hoeflertext' => array(
    'source'  => 'system',
    'family'  => 'Hoefler Text',
    'stack'   => '"Hoefler Text", "Baskerville old face", Garamond, "Times New Roman", serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'lucidabright' => array(
    'source'  => 'system',
    'family'  => 'Lucida Bright',
    'stack'   => '"Lucida Bright", Georgia, serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'lucidagrande' => array(
    'source'  => 'system',
    'family'  => 'Lucida Grande',
    'stack'   => '"Lucida Grande", "Lucida Sans Unicode", "Lucida Sans", Geneva, Verdana, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'lucidasanstypewriter' => array(
    'source'  => 'system',
    'family'  => 'Lucida Sans Typewriter',
    'stack'   => '"Lucida Sans Typewriter", "Lucida Console", monaco, "Bitstream Vera Sans Mono", monospace',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'monaco' => array(
    'source'  => 'system',
    'family'  => 'Monaco',
    'stack'   => 'Monaco, Consolas, "Lucida Console", monospace',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'optima' => array(
    'source'  => 'system',
    'family'  => 'Optima',
    'stack'   => 'Optima, Segoe, "Segoe UI", Candara, Calibri, Arial, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'palatino' => array(
    'source'  => 'system',
    'family'  => 'Palatino',
    'stack'   => 'Palatino, "Palatino Linotype", "Palatino LT STD", "Book Antiqua", Georgia, serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'system-ui' => array(
    'source'  => 'system',
    'family'  => 'system-ui',
    'stack'   => 'system-ui, -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif',
    'weights' => [
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    ]
  ),
  'tahoma' => array(
    'source'  => 'system',
    'family'  => 'Tahoma',
    'stack'   => 'Tahoma, Geneva, Verdana, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'timesnewroman' => array(
    'source'  => 'system',
    'family'  => 'Times New Roman',
    'stack'   => '"Times New Roman", Georgia, serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'trebuchetms' => array(
    'source'  => 'system',
    'family'  => 'Trebuchet MS',
    'stack'   => '"Trebuchet MS", "Lucida Grande", "Lucida Sans Unicode", "Lucida Sans", Tahoma, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
  'verdana' => array(
    'source'  => 'system',
    'family'  => 'Verdana',
    'stack'   => 'Verdana, Geneva, sans-serif',
    'weights' => array( '400', '400italic', '700', '700italic' )
  ),
);
