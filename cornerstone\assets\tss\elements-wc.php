[{"1": [{"2": ["tp-wc-add-to-cart-form", [], [{"3": ["margin", [{"4": ["get-base", "(add_to_cart_form_margin)", [{"5": ["add", "_to_cart_form_margin"]}]]}, {"4": ["get", "(add_to_cart_form_margin)", [{"5": ["add", "_to_cart_form_margin"]}]]}]]}]]}, {"2": ["mini-cart", [{"6": "isNested"}], [{"7": [{"6": "isNested"}, [{"3": ["changedmixin", [{"8": "auto"}, {"9": "cart_width"}, {"8": "width"}]]}, {"7": [{"4": ["is-set", "(get(cart_max_width), get-base(cart_max_width))", [{"4": ["get", "(cart_max_width)", [{"9": "cart_max_width"}]]}, {"4": ["get-base", "(cart_max_width)", [{"9": "cart_max_width"}]]}]]}, [{"10": ["max-width", {"4": ["get", "(cart_max_width)", [{"9": "cart_max_width"}]]}, false]}]]}, {"3": ["margin", [{"4": ["get-base", "(cart_margin)", [{"9": "cart_margin"}]]}, {"4": ["get", "(cart_margin)", [{"9": "cart_margin"}]]}]]}, {"3": ["border", [{"11": ["width", {"4": ["get", "(cart_border_width)", [{"9": "cart_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(cart_border_style)", [{"9": "cart_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(cart_border_color)", [{"9": "cart_border_color"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_border_radius)", [{"9": "cart_border_radius"}]]}, {"4": ["get", "(cart_border_radius)", [{"9": "cart_border_radius"}]]}]]}, {"3": ["padding", [{"4": ["get-base", "(cart_padding)", [{"9": "cart_padding"}]]}, {"4": ["get", "(cart_padding)", [{"9": "cart_padding"}]]}]]}, {"3": ["background-color", [{"9": "cart_bg"}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_box_shadow_dimensions)", [{"9": "cart_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_box_shadow_color)", [{"9": "cart_box_shadow_color"}]]}]}]]}, {"7": [{"12": [{"4": ["get", "(cart_title)", [{"9": "cart_title"}]]}, "!=", {"8": ""}]}, [{"13": [[".x-mini-cart-title"], [{"3": ["margin", [{"4": ["get-base", "(cart_title_margin)", [{"9": "cart_title_margin"}]]}, {"4": ["get", "(cart_title_margin)", [{"9": "cart_title_margin"}]]}]]}, {"10": ["font-family", {"4": ["global-ff", "(get(cart_title_font_family))", [{"4": ["get", "(cart_title_font_family)", [{"9": "cart_title_font_family"}]]}]]}, false]}, {"10": ["font-size", {"4": ["get", "(cart_title_font_size)", [{"9": "cart_title_font_size"}]]}, false]}, {"10": ["font-style", {"4": ["get", "(cart_title_font_style)", [{"9": "cart_title_font_style"}]]}, false]}, {"10": ["font-weight", {"4": ["global-fw", "(get(cart_title_font_family),get(cart_title_font_weight))", [{"4": ["get", "(cart_title_font_family)", [{"9": "cart_title_font_family"}]]}, {"4": ["get", "(cart_title_font_weight)", [{"9": "cart_title_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"4": ["get", "(cart_title_line_height)", [{"9": "cart_title_line_height"}]]}, false]}, {"7": [{"4": ["is-set", "(get(cart_title_letter_spacing), get-base(cart_title_letter_spacing))", [{"4": ["get", "(cart_title_letter_spacing)", [{"9": "cart_title_letter_spacing"}]]}, {"4": ["get-base", "(cart_title_letter_spacing)", [{"9": "cart_title_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"4": ["get", "(cart_title_letter_spacing)", [{"9": "cart_title_letter_spacing"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_title_text_align), get-base(cart_title_text_align))", [{"4": ["get", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}, {"4": ["get-base", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}]]}, [{"10": ["text-align", {"4": ["get", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_title_text_decoration), get-base(cart_title_text_decoration))", [{"4": ["get", "(cart_title_text_decoration)", [{"9": "cart_title_text_decoration"}]]}, {"4": ["get-base", "(cart_title_text_decoration)", [{"9": "cart_title_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"4": ["get", "(cart_title_text_decoration)", [{"9": "cart_title_text_decoration"}]]}, false]}]]}, {"3": ["text-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_title_text_shadow_dimensions)", [{"9": "cart_title_text_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_title_text_shadow_color)", [{"9": "cart_title_text_shadow_color"}]]}]}]]}, {"7": [{"4": ["is-set", "(get(cart_title_text_transform), get-base(cart_title_text_transform))", [{"4": ["get", "(cart_title_text_transform)", [{"9": "cart_title_text_transform"}]]}, {"4": ["get-base", "(cart_title_text_transform)", [{"9": "cart_title_text_transform"}]]}]]}, [{"10": ["text-transform", {"4": ["get", "(cart_title_text_transform)", [{"9": "cart_title_text_transform"}]]}, false]}]]}, {"3": ["text-color", [{"4": ["get", "(cart_title_text_color)", [{"9": "cart_title_text_color"}]]}]]}]]}]]}]]}, {"13": [["li.empty"], [{"10": ["line-height", {"4": ["get", "(cart_links_line_height)", [{"9": "cart_links_line_height"}]]}, false]}, {"7": [{"4": ["is-set", "(get(cart_title_text_align), get-base(cart_title_text_align))", [{"4": ["get", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}, {"4": ["get-base", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}]]}, [{"10": ["text-align", {"4": ["get", "(cart_title_text_align)", [{"9": "cart_title_text_align"}]]}, false]}]]}, {"3": ["text-color", [{"4": ["get", "(cart_links_text_color)", [{"9": "cart_links_text_color"}]]}, {"4": ["get", "(cart_links_text_color_alt)", [{"9": "cart_links_text_color_alt"}]]}]]}]]}, {"13": [[".cart_list"], [{"10": ["order", {"4": ["get", "(cart_order_items)", [{"9": "cart_order_items"}]]}, false]}]]}, {"13": [[".mini_cart_item"], [{"3": ["margin", [{"4": ["get-base", "(cart_items_margin)", [{"9": "cart_items_margin"}]]}, {"4": ["get", "(cart_items_margin)", [{"9": "cart_items_margin"}]]}]]}, {"3": ["border", [{"11": ["width", {"4": ["get", "(cart_items_border_width)", [{"9": "cart_items_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(cart_items_border_style)", [{"9": "cart_items_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(cart_items_border_color)", [{"9": "cart_items_border_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_items_border_color_alt)", [{"9": "cart_items_border_color_alt"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_items_border_radius)", [{"9": "cart_items_border_radius"}]]}, {"4": ["get", "(cart_items_border_radius)", [{"9": "cart_items_border_radius"}]]}]]}, {"3": ["padding", [{"4": ["get-base", "(cart_items_padding)", [{"9": "cart_items_padding"}]]}, {"4": ["get", "(cart_items_padding)", [{"9": "cart_items_padding"}]]}]]}, {"3": ["background-color", [{"9": "cart_items_bg"}, {"9": "cart_items_bg_alt"}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_items_box_shadow_dimensions)", [{"9": "cart_items_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_items_box_shadow_color)", [{"9": "cart_items_box_shadow_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_items_box_shadow_color_alt)", [{"9": "cart_items_box_shadow_color_alt"}]]}]}]]}]]}, {"13": [[".mini_cart_item:hover"], [{"3": ["border-alt", [{"11": ["width", {"4": ["get", "(cart_items_border_width)", [{"9": "cart_items_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(cart_items_border_style)", [{"9": "cart_items_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(cart_items_border_color)", [{"9": "cart_items_border_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_items_border_color_alt)", [{"9": "cart_items_border_color_alt"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_items_border_radius)", [{"9": "cart_items_border_radius"}]]}, {"4": ["get", "(cart_items_border_radius)", [{"9": "cart_items_border_radius"}]]}]]}, {"3": ["background-color", [{"9": "cart_items_bg"}, {"9": "cart_items_bg_alt"}]]}, {"3": ["box-shadow-alt", [{"11": ["dimensions", {"4": ["get", "(cart_items_box_shadow_dimensions)", [{"9": "cart_items_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_items_box_shadow_color)", [{"9": "cart_items_box_shadow_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_items_box_shadow_color_alt)", [{"9": "cart_items_box_shadow_color_alt"}]]}]}]]}]]}, {"7": [{"14": ["not", {"4": ["get", "(cart_items_display_remove)", [{"9": "cart_items_display_remove"}]]}]}, [{"13": [[".mini_cart_item .remove"], [{"10": ["display", {"9": "none"}, true]}, {"10": ["visibility", {"9": "hidden"}, true]}]]}]]}, {"13": [[".mini_cart_item img"], [{"10": ["width", {"4": ["get", "(cart_thumbs_width)", [{"9": "cart_thumbs_width"}]]}, false]}, {"10": ["margin-right", {"4": ["get", "(cart_items_content_spacing)", [{"9": "cart_items_content_spacing"}]]}, false]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_thumbs_border_radius)", [{"9": "cart_thumbs_border_radius"}]]}, {"4": ["get", "(cart_thumbs_border_radius)", [{"9": "cart_thumbs_border_radius"}]]}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_thumbs_box_shadow_dimensions)", [{"9": "cart_thumbs_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_thumbs_box_shadow_color)", [{"9": "cart_thumbs_box_shadow_color"}]]}]}]]}]]}, {"13": [[".rtl & .mini_cart_item img"], [{"10": ["margin-left", {"4": ["get", "(cart_items_content_spacing)", [{"9": "cart_items_content_spacing"}]]}, false]}, {"10": ["margin-right", {"15": "0"}, false]}]]}, {"13": [[".mini_cart_item a"], [{"10": ["font-family", {"4": ["global-ff", "(get(cart_links_font_family))", [{"4": ["get", "(cart_links_font_family)", [{"9": "cart_links_font_family"}]]}]]}, false]}, {"10": ["font-size", {"4": ["get", "(cart_links_font_size)", [{"9": "cart_links_font_size"}]]}, false]}, {"10": ["font-style", {"4": ["get", "(cart_links_font_style)", [{"9": "cart_links_font_style"}]]}, false]}, {"10": ["font-weight", {"4": ["global-fw", "(get(cart_links_font_family),get(cart_links_font_weight))", [{"4": ["get", "(cart_links_font_family)", [{"9": "cart_links_font_family"}]]}, {"4": ["get", "(cart_links_font_weight)", [{"9": "cart_links_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"4": ["get", "(cart_links_line_height)", [{"9": "cart_links_line_height"}]]}, false]}, {"7": [{"4": ["is-set", "(get(cart_links_letter_spacing), get-base(cart_links_letter_spacing))", [{"4": ["get", "(cart_links_letter_spacing)", [{"9": "cart_links_letter_spacing"}]]}, {"4": ["get-base", "(cart_links_letter_spacing)", [{"9": "cart_links_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"4": ["get", "(cart_links_letter_spacing)", [{"9": "cart_links_letter_spacing"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_links_text_align), get-base(cart_links_text_align))", [{"4": ["get", "(cart_links_text_align)", [{"9": "cart_links_text_align"}]]}, {"4": ["get-base", "(cart_links_text_align)", [{"9": "cart_links_text_align"}]]}]]}, [{"10": ["text-align", {"4": ["get", "(cart_links_text_align)", [{"9": "cart_links_text_align"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_links_text_decoration), get-base(cart_links_text_decoration))", [{"4": ["get", "(cart_links_text_decoration)", [{"9": "cart_links_text_decoration"}]]}, {"4": ["get-base", "(cart_links_text_decoration)", [{"9": "cart_links_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"4": ["get", "(cart_links_text_decoration)", [{"9": "cart_links_text_decoration"}]]}, false]}]]}, {"3": ["text-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_links_text_shadow_dimensions)", [{"9": "cart_links_text_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_links_text_shadow_color)", [{"9": "cart_links_text_shadow_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_links_text_shadow_color_alt)", [{"9": "cart_links_text_shadow_color_alt"}]]}]}]]}, {"7": [{"4": ["is-set", "(get(cart_links_text_transform), get-base(cart_links_text_transform))", [{"4": ["get", "(cart_links_text_transform)", [{"9": "cart_links_text_transform"}]]}, {"4": ["get-base", "(cart_links_text_transform)", [{"9": "cart_links_text_transform"}]]}]]}, [{"10": ["text-transform", {"4": ["get", "(cart_links_text_transform)", [{"9": "cart_links_text_transform"}]]}, false]}]]}, {"3": ["text-color", [{"4": ["get", "(cart_links_text_color)", [{"9": "cart_links_text_color"}]]}, {"4": ["get", "(cart_links_text_color_alt)", [{"9": "cart_links_text_color_alt"}]]}]]}]]}, {"13": [[".mini_cart_item a:hover", ".mini_cart_item a:focus"], [{"3": ["text-color-alt", [{"4": ["get", "(cart_links_text_color)", [{"9": "cart_links_text_color"}]]}, {"4": ["get", "(cart_links_text_color_alt)", [{"9": "cart_links_text_color_alt"}]]}]]}, {"3": ["text-shadow-alt", [{"11": ["dimensions", {"4": ["get", "(cart_links_text_shadow_dimensions)", [{"9": "cart_links_text_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_links_text_shadow_color)", [{"9": "cart_links_text_shadow_color"}]]}]}, {"11": ["alt", {"4": ["get", "(cart_links_text_shadow_color_alt)", [{"9": "cart_links_text_shadow_color_alt"}]]}]}]]}]]}, {"13": [[".mini_cart_item .remove"], [{"10": ["width", {"4": ["calc", {"16": ["(1em * %s)", [{"4": ["get", "(cart_links_line_height)", [{"9": "cart_links_line_height"}]]}]]}, [{"12": [{"5": ["1", "em"]}, "*", {"9": {"16": ["%s", [{"4": ["get", "(cart_links_line_height)", [{"9": "cart_links_line_height"}]]}]]}}]}]]}, false]}, {"10": ["margin-left", {"4": ["get", "(cart_items_content_spacing)", [{"9": "cart_items_content_spacing"}]]}, false]}]]}, {"13": [[".rtl & .mini_cart_item .remove"], [{"10": ["margin-left", {"15": "0"}, false]}, {"10": ["margin-right", {"4": ["get", "(cart_items_content_spacing)", [{"9": "cart_items_content_spacing"}]]}, false]}]]}, {"13": [[".mini_cart_item .quantity"], [{"10": ["font-family", {"4": ["global-ff", "(get(cart_quantity_font_family))", [{"4": ["get", "(cart_quantity_font_family)", [{"9": "cart_quantity_font_family"}]]}]]}, false]}, {"10": ["font-size", {"4": ["get", "(cart_quantity_font_size)", [{"9": "cart_quantity_font_size"}]]}, false]}, {"10": ["font-style", {"4": ["get", "(cart_quantity_font_style)", [{"9": "cart_quantity_font_style"}]]}, false]}, {"10": ["font-weight", {"4": ["global-fw", "(get(cart_quantity_font_family),get(cart_quantity_font_weight))", [{"4": ["get", "(cart_quantity_font_family)", [{"9": "cart_quantity_font_family"}]]}, {"4": ["get", "(cart_quantity_font_weight)", [{"9": "cart_quantity_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"4": ["get", "(cart_quantity_line_height)", [{"9": "cart_quantity_line_height"}]]}, false]}, {"7": [{"4": ["is-set", "(get(cart_quantity_letter_spacing), get-base(cart_quantity_letter_spacing))", [{"4": ["get", "(cart_quantity_letter_spacing)", [{"9": "cart_quantity_letter_spacing"}]]}, {"4": ["get-base", "(cart_quantity_letter_spacing)", [{"9": "cart_quantity_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"4": ["get", "(cart_quantity_letter_spacing)", [{"9": "cart_quantity_letter_spacing"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_quantity_text_align), get-base(cart_quantity_text_align))", [{"4": ["get", "(cart_quantity_text_align)", [{"9": "cart_quantity_text_align"}]]}, {"4": ["get-base", "(cart_quantity_text_align)", [{"9": "cart_quantity_text_align"}]]}]]}, [{"10": ["text-align", {"4": ["get", "(cart_quantity_text_align)", [{"9": "cart_quantity_text_align"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_quantity_text_decoration), get-base(cart_quantity_text_decoration))", [{"4": ["get", "(cart_quantity_text_decoration)", [{"9": "cart_quantity_text_decoration"}]]}, {"4": ["get-base", "(cart_quantity_text_decoration)", [{"9": "cart_quantity_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"4": ["get", "(cart_quantity_text_decoration)", [{"9": "cart_quantity_text_decoration"}]]}, false]}]]}, {"3": ["text-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_quantity_text_shadow_dimensions)", [{"9": "cart_quantity_text_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_quantity_text_shadow_color)", [{"9": "cart_quantity_text_shadow_color"}]]}]}]]}, {"7": [{"4": ["is-set", "(get(cart_quantity_text_transform), get-base(cart_quantity_text_transform))", [{"4": ["get", "(cart_quantity_text_transform)", [{"9": "cart_quantity_text_transform"}]]}, {"4": ["get-base", "(cart_quantity_text_transform)", [{"9": "cart_quantity_text_transform"}]]}]]}, [{"10": ["text-transform", {"4": ["get", "(cart_quantity_text_transform)", [{"9": "cart_quantity_text_transform"}]]}, false]}]]}, {"3": ["text-color", [{"4": ["get", "(cart_quantity_text_color)", [{"9": "cart_quantity_text_color"}]]}]]}]]}, {"13": [[".total"], [{"10": ["order", {"4": ["get", "(cart_order_total)", [{"9": "cart_order_total"}]]}, false]}, {"3": ["margin", [{"4": ["get-base", "(cart_total_margin)", [{"9": "cart_total_margin"}]]}, {"4": ["get", "(cart_total_margin)", [{"9": "cart_total_margin"}]]}]]}, {"3": ["border", [{"11": ["width", {"4": ["get", "(cart_total_border_width)", [{"9": "cart_total_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(cart_total_border_style)", [{"9": "cart_total_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(cart_total_border_color)", [{"9": "cart_total_border_color"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_total_border_radius)", [{"9": "cart_total_border_radius"}]]}, {"4": ["get", "(cart_total_border_radius)", [{"9": "cart_total_border_radius"}]]}]]}, {"3": ["padding", [{"4": ["get-base", "(cart_total_padding)", [{"9": "cart_total_padding"}]]}, {"4": ["get", "(cart_total_padding)", [{"9": "cart_total_padding"}]]}]]}, {"10": ["font-family", {"4": ["global-ff", "(get(cart_total_font_family))", [{"4": ["get", "(cart_total_font_family)", [{"9": "cart_total_font_family"}]]}]]}, false]}, {"10": ["font-size", {"4": ["get", "(cart_total_font_size)", [{"9": "cart_total_font_size"}]]}, false]}, {"10": ["font-style", {"4": ["get", "(cart_total_font_style)", [{"9": "cart_total_font_style"}]]}, false]}, {"10": ["font-weight", {"4": ["global-fw", "(get(cart_total_font_family),get(cart_total_font_weight))", [{"4": ["get", "(cart_total_font_family)", [{"9": "cart_total_font_family"}]]}, {"4": ["get", "(cart_total_font_weight)", [{"9": "cart_total_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"4": ["get", "(cart_total_line_height)", [{"9": "cart_total_line_height"}]]}, false]}, {"7": [{"4": ["is-set", "(get(cart_total_letter_spacing), get-base(cart_total_letter_spacing))", [{"4": ["get", "(cart_total_letter_spacing)", [{"9": "cart_total_letter_spacing"}]]}, {"4": ["get-base", "(cart_total_letter_spacing)", [{"9": "cart_total_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"4": ["get", "(cart_total_letter_spacing)", [{"9": "cart_total_letter_spacing"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_total_text_align), get-base(cart_total_text_align))", [{"4": ["get", "(cart_total_text_align)", [{"9": "cart_total_text_align"}]]}, {"4": ["get-base", "(cart_total_text_align)", [{"9": "cart_total_text_align"}]]}]]}, [{"10": ["text-align", {"4": ["get", "(cart_total_text_align)", [{"9": "cart_total_text_align"}]]}, false]}]]}, {"7": [{"4": ["is-set", "(get(cart_total_text_decoration), get-base(cart_total_text_decoration))", [{"4": ["get", "(cart_total_text_decoration)", [{"9": "cart_total_text_decoration"}]]}, {"4": ["get-base", "(cart_total_text_decoration)", [{"9": "cart_total_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"4": ["get", "(cart_total_text_decoration)", [{"9": "cart_total_text_decoration"}]]}, false]}]]}, {"3": ["text-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_total_text_shadow_dimensions)", [{"9": "cart_total_text_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_total_text_shadow_color)", [{"9": "cart_total_text_shadow_color"}]]}]}]]}, {"7": [{"4": ["is-set", "(get(cart_total_text_transform), get-base(cart_total_text_transform))", [{"4": ["get", "(cart_total_text_transform)", [{"9": "cart_total_text_transform"}]]}, {"4": ["get-base", "(cart_total_text_transform)", [{"9": "cart_total_text_transform"}]]}]]}, [{"10": ["text-transform", {"4": ["get", "(cart_total_text_transform)", [{"9": "cart_total_text_transform"}]]}, false]}]]}, {"3": ["text-color", [{"4": ["get", "(cart_total_text_color)", [{"9": "cart_total_text_color"}]]}]]}, {"3": ["background-color", [{"9": "cart_total_bg"}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_total_box_shadow_dimensions)", [{"9": "cart_total_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_total_box_shadow_color)", [{"9": "cart_total_box_shadow_color"}]]}]}]]}]]}, {"13": [[".buttons"], [{"10": ["order", {"4": ["get", "(cart_order_buttons)", [{"9": "cart_order_buttons"}]]}, false]}, {"10": ["justify-content", {"4": ["get", "(cart_buttons_justify_content)", [{"9": "cart_buttons_justify_content"}]]}, false]}, {"3": ["margin", [{"4": ["get-base", "(cart_buttons_margin)", [{"9": "cart_buttons_margin"}]]}, {"4": ["get", "(cart_buttons_margin)", [{"9": "cart_buttons_margin"}]]}]]}, {"3": ["border", [{"11": ["width", {"4": ["get", "(cart_buttons_border_width)", [{"9": "cart_buttons_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(cart_buttons_border_style)", [{"9": "cart_buttons_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(cart_buttons_border_color)", [{"9": "cart_buttons_border_color"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(cart_buttons_border_radius)", [{"9": "cart_buttons_border_radius"}]]}, {"4": ["get", "(cart_buttons_border_radius)", [{"9": "cart_buttons_border_radius"}]]}]]}, {"3": ["padding", [{"4": ["get-base", "(cart_buttons_padding)", [{"9": "cart_buttons_padding"}]]}, {"4": ["get", "(cart_buttons_padding)", [{"9": "cart_buttons_padding"}]]}]]}, {"3": ["background-color", [{"9": "cart_buttons_bg"}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(cart_buttons_box_shadow_dimensions)", [{"9": "cart_buttons_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(cart_buttons_box_shadow_color)", [{"9": "cart_buttons_box_shadow_color"}]]}]}]]}]]}]]}, {"2": ["products", [], [{"3": ["margin", [{"4": ["get-base", "(products_margin)", [{"9": "products_margin"}]]}, {"4": ["get", "(products_margin)", [{"9": "products_margin"}]]}]]}]]}, {"2": ["tp-wc-product-gallery", [], [{"3": ["changedmixin", [{"8": "visible"}, {"9": "product_gallery_overflow"}, {"8": "overflow"}]]}, {"7": [{"4": ["is-set", "(get(product_gallery_max_width), get-base(product_gallery_max_width))", [{"4": ["get", "(product_gallery_max_width)", [{"9": "product_gallery_max_width"}]]}, {"4": ["get-base", "(product_gallery_max_width)", [{"9": "product_gallery_max_width"}]]}]]}, [{"10": ["max-width", {"4": ["get", "(product_gallery_max_width)", [{"9": "product_gallery_max_width"}]]}, false]}]]}, {"3": ["margin", [{"4": ["get-base", "(product_gallery_margin)", [{"9": "product_gallery_margin"}]]}, {"4": ["get", "(product_gallery_margin)", [{"9": "product_gallery_margin"}]]}]]}, {"3": ["border", [{"11": ["width", {"4": ["get", "(product_gallery_border_width)", [{"9": "product_gallery_border_width"}]]}]}, {"11": ["style", {"4": ["get", "(product_gallery_border_style)", [{"9": "product_gallery_border_style"}]]}]}, {"11": ["base", {"4": ["get", "(product_gallery_border_color)", [{"9": "product_gallery_border_color"}]]}]}]]}, {"3": ["border-radius", [{"4": ["get-base", "(product_gallery_border_radius)", [{"9": "product_gallery_border_radius"}]]}, {"4": ["get", "(product_gallery_border_radius)", [{"9": "product_gallery_border_radius"}]]}]]}, {"3": ["padding", [{"4": ["get-base", "(product_gallery_padding)", [{"9": "product_gallery_padding"}]]}, {"4": ["get", "(product_gallery_padding)", [{"9": "product_gallery_padding"}]]}]]}, {"10": ["font-size", {"4": ["get", "(product_gallery_base_font_size)", [{"9": "product_gallery_base_font_size"}]]}, false]}, {"3": ["background-color", [{"9": "product_gallery_bg_color"}]]}, {"3": ["box-shadow", [{"11": ["dimensions", {"4": ["get", "(product_gallery_box_shadow_dimensions)", [{"9": "product_gallery_box_shadow_dimensions"}]]}]}, {"11": ["base", {"4": ["get", "(product_gallery_box_shadow_color)", [{"9": "product_gallery_box_shadow_color"}]]}]}]]}]]}, {"2": ["tp-wc-shop-notices", [], [{"3": ["margin", [{"4": ["get-base", "(shop_notices_margin)", [{"9": "shop_notices_margin"}]]}, {"4": ["get", "(shop_notices_margin)", [{"9": "shop_notices_margin"}]]}]]}]]}, {"2": ["tp-wc-shop-sort", [], [{"3": ["margin", [{"4": ["get-base", "(shop_sort_margin)", [{"9": "shop_sort_margin"}]]}, {"4": ["get", "(shop_sort_margin)", [{"9": "shop_sort_margin"}]]}]]}]]}]}, {"document": 1, "module": 2, "include": 3, "call": 4, "dimension": 5, "variable": 6, "if": 7, "singleQuotedString": 8, "primitive": 9, "assignProperty": 10, "keywordArgument": 11, "operation": 12, "styleRule": 13, "unary": 14, "number": 15, "interpolated": 16}]