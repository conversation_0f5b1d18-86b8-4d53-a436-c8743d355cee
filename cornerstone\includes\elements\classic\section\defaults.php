<?php

/**
 * Element Defaults: Section
 */

return array(

  'title'        => '',
	'id'           => '',
	'class'        => '',
	'style'        => '',
	'text_align'   => 'none',
	'visibility'   => array(),
	'margin'       => array( '0px', '0px', '0px', '0px', 'unlinked' ),
	'padding'      => array( '45px', '0px', '45px', '0px', 'unlinked' ),
	'border_style' => 'none',
	'border_color' => '',
	'border_width' => array( '1px', '1px', '1px', '1px', 'linked' ),

	'bg_type'           => 'none',
	'bg_image'          => '',
	'bg_color'          => '',
	'bg_video'          => '',
	'bg_pattern_toggle' => false,
	'parallax'          => false,
	'bg_video'          => '',
	'bg_video_poster'   => '',

	'separator_top_type'        => 'none',
	'separator_top_height'      => '50px',
	'separator_top_inset'       => '0px',
	'separator_top_angle_point' => '50',

	'separator_bottom_type'        => 'none',
	'separator_bottom_height'      => '50px',
	'separator_bottom_inset'       => '0px',
	'separator_bottom_angle_point' => '50',

);
